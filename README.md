## Prerequisite

> - NodeJS v22.14.0
> - npm v11.2.0
> - react-redux v9.2.0
> - redux-persist v6.0.0
> - Tailwindcss - v4.0.0
> - Prettier - code formatter extension vscode
> - Eslint

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:4100](http://localhost:4100) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

### A top-level directory layout

```bash
├── README.md
├── api
| ├── index.js
| └── routes.js
├── app
| ├── StoreProvider.jsx
| ├── components
| | ├── auth
| | ├── config
| | └── layout
| ├── favicon.ico
| ├── globals.css
| ├── layout.js
| └── page.js
├── eslint.config.mjs
├── jsconfig.json
├── next.config.mjs
├── package.json
├── postcss.config.mjs
├── public
| ├── file.svg
| ├── globe.svg
| ├── next.svg
| ├── vercel.svg
| └── window.svg
├── routes
| └── urls.js
├── store
| ├── actions
| | ├── authActions.js
| | └── userActions.js
| ├── api
| | └── authApi.js
| ├── index.js
| ├── reducers
| | ├── authReducer.js
| | ├── createProfileReducer.js
| | ├── index.js
| | ├── masterReducer.js
| | ├── ui.js
| | └── userReducer.js
| └── utils
| └── errorHandling.js
├── utils
| ├── constant.js
| ├── cookiesStorageKeys.js
| ├── errorMessages.js
| ├── function.js
| └── schema.js
└── yarn.lock
```
