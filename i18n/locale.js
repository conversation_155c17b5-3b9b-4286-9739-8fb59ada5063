'use server';

// import { cookies } from 'next/headers';
import { defaultLocale } from '@/i18n/config';
// import { cookiesKey } from '@/utils/cookiesStorageKeys';

// In this example the locale is read from a cookie. You could alternatively
// also read it from a database, backend service, or any other source.

export async function getUserLocale() {
  // return cookies()?.get(cookiesKey?.languageCode)?.value || defaultLocale;
  return defaultLocale;
}

// export async function setUserLocale(locale) {
//   cookies()?.set(cookiesKey?.languageCode, locale);
// }
