import { getRequestConfig } from 'next-intl/server';
import { getUserLocale } from './locale';
import { showErrorToast } from '@/utils/function';
// import { LANGUAGE_JSON_URL } from '@/app/components/config';

// export default getRequestConfig(async () => {
//   const locale = await getUserLocale();
//   const apiUrl = `${LANGUAGE_JSON_URL}/locales/${locale}.json`;

//   try {
//     const response = await fetch(apiUrl);

//     if (!response.ok) {
//       throw new Error(`Failed to fetch translations for locale: ${locale}`);
//     }

//     const messages = await response.json();

//     return {
//       locale,
//       messages,
//     };
//   } catch (error) {
//     console.error('Error fetching locale JSON:', error);

//     return {
//       locale,
//       messages: {}, // Fallback to an empty object if the fetch fails
//     };
//   }
// });

export default getRequestConfig(async () => {
  const locale = await getUserLocale();
  let messages = {};

  try {
    messages = (await import(`@/lang/${locale}.json`)).default;
  } catch (error) {
    showErrorToast(`Error loading locale JSON for: ${locale}`, error);
    // messages remains as an empty object
  }

  return {
    locale,
    messages,
  };
});
