import { HOME } from '@/routes/urls';
import { cookiesKey } from '@/utils/cookiesStorageKeys';
import { removeCookie } from '@/utils/function';

/************************************   
   @purpose : function used to handle the api error 
   <AUTHOR> INIC
  **********************************/
export const handleApiError = (error) => {
  if (error.message) {
    if (error.statusCode === 401) {
      removeCookie(cookiesKey.accessToken);
      window.location.href = HOME;
    }
  }
  //   return Promise.reject(error);
};
