// store/config.js
import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import rootReducer from './reducers';
import { resetStore } from './actions/userActions';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['user', 'auth', 'onboarding', 'product'], // Reducers to be persisted
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          // Add any other action types that need to be ignored
        ],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

export const persistor = persistStore(store);

// Export a function to reset the store
export const resetReduxStore = () => {
  store.dispatch(resetStore());
};
