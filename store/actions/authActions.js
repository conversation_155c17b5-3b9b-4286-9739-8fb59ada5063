import * as types from '@/store/actions/userActions';

/** ***************** 
@purpose :  Handle login user action to save token
@Parameter : {value, key}
<AUTHOR> INIC
******************/
export const userRegisterAction = (payload) => {
  return {
    type: types.registerUser.toString(),
    payload,
  };
};

/** ***************** 
@purpose :  Handle login user action to save token
@Parameter : {value, key}
<AUTHOR> INIC
******************/
export const loginUserTokenAction = (payload) => {
  return {
    type: types.loginUserKey.toString(),
    payload,
  };
};

/** ***************** 
@purpose :  Handle set token in cookie
@Parameter : {value, key}
<AUTHOR> INIC
******************/
export const setUserTokenAction = (payload) => {
  return {
    type: types.setTokenKey.toString(),
    payload,
  };
};

/** ***************** 
@purpose :  Handle logout user action to clear token
@Parameter : {value, key}
<AUTHOR> INIC
******************/
export const logoutUserTokenAction = () => ({
  type: types.logoutUserKey.toString(),
});

/** ***************** 
@purpose :  Handle login user action to save token
@Parameter : {value, key}
<AUTHOR> INIC
******************/
export const setUserDetailsAction = (payload) => ({
  type: types.setUserDetailsKey.toString(),
  payload,
});

/** ***************** 
@purpose :  handle reset user auth details if user refreshes
@Parameter : {value, key}
<AUTHOR> INIC
******************/

export const resetUserDetailsAction = (payload) => ({
  type: types.resetAuthStateKey.toString(),
  payload,
});

/** ***************** 
@purpose :  handle reset user auth details if user refreshes
@Parameter : {value, key}
<AUTHOR> INIC
******************/

export const resetForgetOtpAction = (payload) => ({
  type: types.resetForgetOtp.toString(),
  payload,
});

/** ***************** 
@purpose :  handle set reset password token
@Parameter : {value, key}
<AUTHOR> INIC
******************/

export const setResetPasswordTokenAction = (payload) => ({
  type: types.resetForgetPassword.toString(),
  payload,
});

export const logoutUserAction = (payload) => ({
  type: types.logoutUser.toString(),
  payload,
});

export const updateStepAction = (payload) => ({
  type: types.updateStep.toString(),
  payload,
});
