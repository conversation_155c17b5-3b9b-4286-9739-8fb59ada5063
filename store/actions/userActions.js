// store/actions/userActions.js
import { createAction } from '@reduxjs/toolkit';

/** ***************** 
@purpose : All action types
<AUTHOR> INIC
***************** */

/**
 * UI Actions
 */
export const setUiKey = createAction('ui/setKey');
export const toggleUiModal = createAction('ui/toggleModal');

export const loginUser = createAction('user/login');
export const logoutUser = createAction('user/logout');
export const resetStore = createAction('RESET_STORE');

/**
 * Auth Actions
 */
export const registerUser = createAction('user/register');
export const loginUserKey = createAction('auth/login');
export const setTokenKey = createAction('auth/setToken');
export const setSocialUserDetailsKey = createAction('user/userSocialDetails');
export const logoutUserKey = createAction('auth/logout');
export const setUserDetailsKey = createAction('user/userDetails');
export const resetAuthStateKey = createAction('auth/resetState');
export const resetForgetOtp = createAction('auth/forgetOtp');
export const resetForgetPassword = createAction('auth/forgetPassword');

//onBoarding Actions

export const updateStep = createAction('onboarding/updateStep');

//Product Actions

export const setTiers = createAction('product/setTiers');
export const addTier = createAction('product/addTier');
export const removeTier = createAction('product/removeTier');

export const setProductId = createAction('productId/create');
export const deleteProductId = createAction('productId/delete');

export const resetProductState = createAction('product/resetState');
export const resetProductComboState = createAction('productComboId/delete');

export const addProductCombosState = createAction('productCombos/add');
export const setProductComboId = createAction('productComboId/create');
