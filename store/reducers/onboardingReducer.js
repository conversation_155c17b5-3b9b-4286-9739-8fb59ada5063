import { createReducer } from '@reduxjs/toolkit';
import {
  getIndustryTagsAsync,
  getVendorDetailsAsync,
  onboardingUserAsync,
  onboardingVenderAsync,
} from '../api/onboardingApi';
import { updateStepAction } from '../actions/authActions';
import { updateStep } from '../actions/userActions';

const initialState = {
  sections: {
    accountCreation: false,
    sellerInformation: false,
    businessDetails: false,
    categorySelection: false,
    legalAndTaxInfo: false,
  },
  completedStep: 0,
  formData: {
    accountCreation: {},
    sellerInformation: {},
    businessDetails: {},
    categorySelection: {},
    legalAndTaxInfo: {},
  },
  onboardingData: {},
  franchiseIndustryTags: [],
  onboardingDetails: {},
};
const onboardingReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(onboardingUserAsync.pending, (state) => {})
    .addCase(onboardingUserAsync.fulfilled, (state, action) => {
      state.onboardingData = action?.payload?.data;
    })
    .addCase(onboardingUserAsync.rejected, (state, action) => {})
    .addCase(onboardingVenderAsync.pending, (state) => {})
    .addCase(onboardingVenderAsync.fulfilled, (state, action) => {
      state.completedStep = Number(action?.payload?.data?.step);
    })
    .addCase(onboardingVenderAsync.rejected, (state, action) => {})
    .addCase(getIndustryTagsAsync.pending, (state) => {})
    .addCase(getIndustryTagsAsync.fulfilled, (state, action) => {
      state.franchiseIndustryTags = action?.payload?.data;
    })
    .addCase(getIndustryTagsAsync.rejected, (state, action) => {})
    .addCase(getVendorDetailsAsync.pending, (state) => {})
    .addCase(getVendorDetailsAsync.fulfilled, (state, action) => {
      state.onboardingDetails = action?.payload?.data || action?.payload;
    })
    .addCase(getVendorDetailsAsync.rejected, (state, action) => {});
});

export default onboardingReducer;
