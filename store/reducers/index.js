// store/reducers/index.js
import { combineReducers } from '@reduxjs/toolkit';

import uiReducer from './ui';
import authReducer from './authReducer';
import onboardingReducer from './onboardingReducer';
import { logoutUser } from '../actions/userActions';
import productReducer from './productReducer';
import catalogueManagementReducer from './catalogueManagementReducer';
import vendorManagementReducer from './vendorsReducer';

const appReducer = combineReducers({
  ui: uiReducer,
  auth: authReducer,
  onboarding: onboardingReducer,
  product: productReducer,
  catalogueManagement: catalogueManagementReducer,
  vendorManagement: vendorManagementReducer,
});

// Root reducer to reset the store on logout
const rootReducer = (state, action) => {
  if (action.type === logoutUser.type) {
    localStorage.removeItem('persist:root'); // Clears persisted state
    state = undefined;
  }

  return appReducer(state, action);
};

export default rootReducer;
