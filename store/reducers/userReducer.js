// store/reducers/userReducer.js
import { createReducer } from '@reduxjs/toolkit';
import {
  loginUser<PERSON>ey,
  logoutUserKey,
  setSocialUserDetailsKey,
  setTokenKey,
  setUserDetailsKey,
} from '../actions/userActions';

const initialState = {
  socialUser: null,
  user: null,
  accessToken: '',
  refreshToken: '',
};

const userReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(setSocialUserDetailsKey, (state, action) => {
      state.socialUser = action.payload.res;
    })
    .addCase(loginUserKey, (state, action) => {
      state.user = action.payload.user;
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
    })
    .addCase(logoutUserKey.toString(), (state) => {
      state.accessToken = '';
      state.data = {};
      state.refreshToken = '';
    })
    .addCase(setTokenKey, (state, action) => {
      state.accessToken = action.payload.accessToken;
    })
    .addCase(setUserDetailsKey, (state, action) => {
      state.user = action.payload.user;
    });
});

export default userReducer;
