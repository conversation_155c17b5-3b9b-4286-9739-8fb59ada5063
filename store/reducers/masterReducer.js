import { createReducer } from '@reduxjs/toolkit';
import {
  getMasterDataAsync,
  getMasterDataCurrentStatusAsync,
} from '../api/masterApi';

const initialState = {
  getUserMasterData: null,
  getUserMasterDataLoading: false,
  getUserMasterDataError: null,
  getCurrentStatusMasterData: null,
  getCurrentStatusDataLoading: false,
  getCurrentStatusDataError: null,
};
const masterReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(getMasterDataAsync.pending, (state) => {
      state.getUserMasterDataLoading = true;
      state.getUserMasterDataError = null;
    })
    .addCase(getMasterDataAsync.fulfilled, (state, action) => {
      state.getUserMasterDataLoading = false;
      state.getUserMasterData = action.payload;
    })
    .addCase(getMasterDataAsync.rejected, (state, action) => {
      state.getUserMasterDataLoading = false;
      state.getUserMasterDataError = action.error.message;
    })
    .addCase(getMasterDataCurrentStatusAsync.pending, (state) => {
      state.getCurrentStatusDataLoading = true;
      state.getCurrentStatusDataError = null;
    })
    .addCase(getMasterDataCurrentStatusAsync.fulfilled, (state, action) => {
      state.getCurrentStatusDataLoading = false;
      state.getCurrentStatusMasterData = action.payload;
    })
    .addCase(getMasterDataCurrentStatusAsync.rejected, (state, action) => {
      state.getCurrentStatusDataLoading = false;
      state.getCurrentStatusDataError = action.error.message;
    });
});

export default masterReducer;
