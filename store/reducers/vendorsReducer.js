import { createReducer } from '@reduxjs/toolkit';
import { fetchVendorAsync, getVendorDetails, getVendorDetailsAsync } from '../api/vendorsApi';
// import {
//   getMasterDataAsync,
//   getMasterDataCurrentStatusAsync,
// } from '../api/masterApi';

const initialState = {
  getUserMasterData: null,
  getUserMasterDataLoading: false,
  getUserMasterDataError: null,
  getCurrentStatusMasterData: null,
  getCurrentStatusDataLoading: false,
  getCurrentStatusDataError: null,
  vendors: null,
  vendorsLoading: false,
  vendorsError: null,
  vendorsDetails: null,
  vendorsDetailsLoading: false,
  vendorsDetailsError: null,
};

const vendorManagementReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(fetchVendorAsync.pending, (state) => {
      state.vendorsLoading = true;
      state.vendorsError = null;
    })
    .addCase(fetchVendorAsync.fulfilled, (state, action) => {
      state.vendorsLoading = false;
      state.vendors = action.payload;
    })
    .addCase(fetchVendorAsync.rejected, (state, action) => {
      state.vendorsLoading = false;
      state.vendorsError = action.payload || action.error.message;
    })
    .addCase(getVendorDetailsAsync.pending, (state) => {
      state.vendorsDetailsLoading = true;
      state.vendorsDetailsError = null;
    })
    .addCase(getVendorDetailsAsync.fulfilled, (state, action) => {
      state.vendorsDetailsLoading = false;
      state.vendorsDetails = action.payload;
    })
    .addCase(getVendorDetailsAsync.rejected, (state, action) => {
      state.vendorsDetailsLoading = false;
      state.vendorsDetailsError = action.payload || action.error.message;
    });
});

export default vendorManagementReducer;
