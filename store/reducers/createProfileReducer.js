import { createReducer } from '@reduxjs/toolkit';
import { createUpdateProfileAsync } from '../api/createProfileApi';

const initialState = {
  createUpdateProfile: null,
  createUpdateProfileLoading: false,
  createUpdateProfileError: null,
};
const createProfileReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(createUpdateProfileAsync.pending, (state) => {
      state.createUpdateProfileLoading = true;
      state.createUpdateProfileError = null;
    })
    .addCase(createUpdateProfileAsync.fulfilled, (state, action) => {
      state.createUpdateProfileLoading = false;
      state.createUpdateProfile = action.payload;
    })
    .addCase(createUpdateProfileAsync.rejected, (state, action) => {
      state.createUpdateProfileLoading = false;
      state.createUpdateProfileError = action.error.message;
    });
});

export default createProfileReducer;
