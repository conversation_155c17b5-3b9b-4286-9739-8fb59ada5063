import { createReducer } from '@reduxjs/toolkit';
import {
  addProdCombosGenInfo,
  createAttributesVariantsAsync,
  getBrandsAsync,
  getProductDetails,
  getProductsAsync,
} from '../api/productApi';
import {
  addProductCombosState,
  addTier,
  deleteProductId,
  removeTier,
  resetProductState,
  resetProductComboState,
  setProductId,
  setTiers,
  setProductComboId,
} from '../actions/userActions';
// import {
//   getIndustryTagsAsync,
//   getVendorDetailsAsync,
//   onboardingUserAsync,
//   onboardingVenderAsync,
// } from '../api/onboardingApi';
// import { updateStepAction } from '../actions/authActions';
// import { updateStep } from '../actions/userActions';

const initialState = {
  tiers: [],
  productId: null,
  productDetails: null,
  productSetDetails: null,
  productComboId: null,
  productCombosList: [],
};
const productReducer = createReducer(initialState, (builder) => {
  builder
    .addCase(getProductsAsync.pending, (state) => {})
    .addCase(getProductsAsync.rejected, (state, action) => {})
    .addCase(getBrandsAsync.pending, (state) => {})
    .addCase(getBrandsAsync.rejected, (state, action) => {})

    .addCase(getProductsAsync.fulfilled, (state, action) => {
      state.products = action.payload.data;
    })
    .addCase(getBrandsAsync.fulfilled, (state, action) => {
      state.brands = action.payload.data;
    })
    .addCase(setTiers, (state, action) => {
      state.tiers = action.payload;
    })
    .addCase(addTier, (state, action) => {
      state.tiers.push(action.payload);
    })
    .addCase(removeTier, (state, action) => {
      state.tiers = state.tiers.filter((tier) => tier.id !== action.payload);
    })

    .addCase(setProductId, (state, action) => {
      state.productId = action.payload;
    })
    .addCase(deleteProductId, (state, action) => {
      state.productId = null;
    })
    .addCase(getProductDetails.pending, (state, action) => {})
    .addCase(getProductDetails.fulfilled, (state, { payload }) => {
      if (
        payload?.data?.['general-info']?.['product_type'].toLowerCase() ==
        'retail'
      ) {
        state.productDetails = payload.data;
      } else if (
        payload?.data?.['general-info']?.['product_type'].toLowerCase() ==
        'bundle'
      ) {
        state.productSetDetails = payload.data;
      }
    })
    .addCase(getProductDetails.rejected, (state, action) => {})
    .addCase(addProdCombosGenInfo.fulfilled, (state, action) => {
      state.productComboId = action?.payload?.data?.id;
    })
    .addCase(resetProductState, () => initialState)

    .addCase(createAttributesVariantsAsync.fulfilled, (state, action) => {
      state.productDetails = action.payload.data;
    })
    .addCase(resetProductComboState, (state) => {
      state.productComboId = null;
    })
    .addCase(addProductCombosState, (state, action) => {
      const newIds = Array.isArray(action.payload.selectedIds)
        ? action.payload.selectedIds
        : [];
      const existingIds = state.productCombosList || [];
      const combined = Array.from(new Set([...existingIds, ...newIds]));
      state.productCombosList = combined;
    })
    .addCase(setProductComboId, (state, action) => {
      state.productComboId = action.payload;
    });
});

export default productReducer;
