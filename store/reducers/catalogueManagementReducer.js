import { createReducer } from '@reduxjs/toolkit';
import { fetchAttributesAsync } from '../api/catalogueManagementApi';
// import {
//   getMasterDataAsync,
//   getMasterDataCurrentStatusAsync,
// } from '../api/masterApi';

const initialState = {
  getUserMasterData: null,
  getUserMasterDataLoading: false,
  getUserMasterDataError: null,
  getCurrentStatusMasterData: null,
  getCurrentStatusDataLoading: false,
  getCurrentStatusDataError: null,
  attributes: null,
  attributesLoading: false,
  attributesError: null,
};

const catalogueManagement = createReducer(initialState, (builder) => {
  builder
    .addCase(fetchAttributesAsync.pending, (state) => {
      state.attributesLoading = true;
      state.attributesError = null;
    })
    .addCase(fetchAttributesAsync.fulfilled, (state, action) => {
      state.attributesLoading = false;
      state.attributes = action.payload;
    })
    .addCase(fetchAttributesAsync.rejected, (state, action) => {
      state.attributesLoading = false;
      state.attributesError = action.payload || action.error.message;
    });
});

export default catalogueManagement;
