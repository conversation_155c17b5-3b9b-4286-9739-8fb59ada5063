import { createReducer } from '@reduxjs/toolkit';
import { toggleUiModal } from '../actions/userActions';

/** ***************** 
@purpose : Intital ui reducer data
<AUTHOR> INIC
***************** */
const initialState = {
  modals: {
    signin: false,
    register: false,
    forgetPassword: false,
    setNewPassword: false,
    changePassword: false,
  },
};
/** ***************** 
  @purpose : UI Reducer
  @Parameter : {ui_ini_data, action}
  <AUTHOR> INIC
  ***************** */

const uiReducer = createReducer(initialState, (builder, key) => {
  builder.addCase(toggleUiModal, (state, action) => {
    state.modals = { ...state.modals, ...action.payload };
  });
});

export default uiReducer;
