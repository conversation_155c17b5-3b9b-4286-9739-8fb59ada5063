// reducers/authReducer.js

import { createReducer } from '@reduxjs/toolkit';
import {
  getVerificationCodeAsync,
  resendVerifyCodeAsync,
  verifyCodeAsync,
  getOtpAsync,
  sendOtpAsync,
  loginUserAsync,
  forgetPasswordAsync,
  resetPasswordAsync,
  verifyForgotPasswordOtp,
} from '../api/authApi';
import { setUserDetailsAction } from '../actions/authActions';
import {
  loginUserKey,
  logoutUser,
  resetAuthStateKey,
  resetForgetOtp,
  resetForgetPassword,
  setTokenKey,
  setUserDetailsKey,
} from '../actions/userActions';

// Initial state
const initialState = {
  // verificationCode: null,
  // verificationLoading: false,
  // verificationError: null,
  // verifyLoading: false,
  // verifyError: null,
  // resendVerificationCode: null,
  // resendVerificationLoading: false,
  // resendVerificationError: null,
  userDetails: {},
  otpSent: false,
  otpLoading: false,
  otpError: null,
  otpVerificationLoading: false,
  otpVerificationError: null,
  userLoginLoading: false,
  userLoginError: null,
  userToken: null,
  resetPasswordToken: null,
  forgetPasswordLoading: false,
  forgetPasswordError: null,
  resetPasswordLoading: false,
  resetPasswordError: null,
};

// Reducer
const authReducer = createReducer(initialState, (builder) => {
  builder
    // .addCase(getVerificationCodeAsync.pending, (state) => {
    //   state.verificationLoading = true;
    //   state.verificationError = null;
    // })
    // .addCase(getVerificationCodeAsync.fulfilled, (state, action) => {
    //   state.verificationLoading = false;
    //   state.verificationCode = action.payload;
    // })
    // .addCase(getVerificationCodeAsync.rejected, (state, action) => {
    //   state.verificationLoading = false;
    //   state.verificationError = action.error.message;
    // })
    // .addCase(verifyCodeAsync.pending, (state) => {
    //   state.verifyLoading = true;
    //   state.verifyError = null;
    // })
    // .addCase(verifyCodeAsync.fulfilled, (state) => {
    //   state.verifyLoading = false;
    //   // You can handle successful verification if needed
    // })
    // .addCase(verifyCodeAsync.rejected, (state, action) => {
    //   state.verifyLoading = false;
    //   state.verifyError = action.error.message;
    // })
    // //resend
    // .addCase(resendVerifyCodeAsync.pending, (state) => {
    //   state.resendVerificationLoading = true;
    //   state.resendVerificationError = null;
    // })
    // .addCase(resendVerifyCodeAsync.fulfilled, (state, action) => {
    //   state.resendVerificationLoading = false;
    //   state.resendVerificationCode = action.payload;
    // })
    // .addCase(resendVerifyCodeAsync.rejected, (state, action) => {
    //   state.resendVerificationLoading = false;
    //   state.resendVerificationError = action.error.message;
    // });
    // .addCase(getOtpAsync.pending, (state, action) => {
    //   state.otpLoading = true;
    //   state.otpError = null;
    // })
    // .addCase(getOtpAsync.fulfilled, (state, action) => {
    //   state.otpLoading = false;
    //   state.otpSent = true;
    // })
    // .addCase(getOtpAsync.rejected, (state, action) => {
    //   state.otpLoading = false;
    //   state.otpError = action.error.message || 'Something went wrong';
    // })
    .addCase(setUserDetailsKey, (state, action) => {
      state.userDetails = action.payload;
    })
    .addCase(loginUserKey, (state, action) => {
      state.userToken = action.payload;
    })
    .addCase(resetForgetPassword, (state, action) => {
      state.resetPasswordToken = action?.payload?.token;
    })
    .addCase(resetAuthStateKey, (state) => {
      state.otpSent = false;
      state.userDetails = {};
      state.otpError = null;
      state.otpLoading = false;
    })
    .addCase(logoutUser, (state) => {
      state.userToken = null;
      state.userDetails = {};
    })
    // .addCase(sendOtpAsync.pending, (state, action) => {
    //   state.otpVerificationLoading = true;
    //   state.otpVerificationError = null;
    // })
    // .addCase(sendOtpAsync.fulfilled, (state, action) => {
    //   state.otpVerificationLoading = false;
    // })
    // .addCase(sendOtpAsync.rejected, (state, action) => {
    //   state.otpVerificationLoading = false;
    //   state.otpVerificationError =
    //     action.error.message || 'Something went wrong';
    // })
    // .addCase(loginUserAsync.pending, (state, action) => {
    //   state.userLoginLoading = true;
    //   state.userLoginError = null;
    // })
    // .addCase(loginUserAsync.fulfilled, (state, action) => {
    //   state.userLoginLoading = false;
    //   state.userDetails = action?.payload?.data?.user;
    // })
    // .addCase(loginUserAsync.rejected, (state, action) => {
    //   state.userLoginLoading = false;
    //   state.userLoginError = action.error.message || 'Something went wrong';
    // })
    // .addCase(forgetPasswordAsync.pending, (state, action) => {
    //   state.forgetPasswordLoading = true;
    //   state.forgetPasswordError = null;
    // })
    // .addCase(forgetPasswordAsync.fulfilled, (state, action) => {
    //   state.forgetPasswordLoading = false;
    // })
    // .addCase(forgetPasswordAsync.rejected, (state, action) => {
    //   state.forgetPasswordLoading = false;
    //   state.forgetPasswordError =
    //     action.error.message || 'Something went wrong';
    // })
    // .addCase(resetPasswordAsync.pending, (state, action) => {
    //   state.resetPasswordLoading = true;
    //   state.resetPasswordError = null;
    // })
    // .addCase(resetPasswordAsync.fulfilled, (state, action) => {
    //   state.resetPasswordLoading = false;
    // })
    // .addCase(resetPasswordAsync.rejected, (state, action) => {
    //   state.resetPasswordLoading = false;
    //   state.resetPasswordLoading =
    //     action.error.message || 'Something went wrong';
    // })
    // .addCase(verifyForgotPasswordOtp.pending, (state, action) => {})
    // .addCase(verifyForgotPasswordOtp.fulfilled, (state, action) => {})
    // .addCase(verifyForgotPasswordOtp.rejected, (state, action) => {});
});

export default authReducer;
