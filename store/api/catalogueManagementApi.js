// All Master Management API

import { get, remove } from '@/api';
import { LIST_ALL_ATTRIBUTES} from '@/api/routes';
import { createAsyncThunk } from '@reduxjs/toolkit';

// Get All industries
export const fetchAttributesAsync = createAsyncThunk(
  'catalogueManagement/fetchAll',
  async (payload, thunkAPI) => {
    try {
      const response = await get(LIST_ALL_ATTRIBUTES, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const deleteAttributesAsync = createAsyncThunk(
  'catalogueManagement/delete',
  async ( payload , thunkAPI) => {
    try {
      const response = await remove(
        `${LIST_ALL_ATTRIBUTES}/${payload}`,
        true,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);