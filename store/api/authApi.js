// api/authApi.js

import { get, post } from '@/api';
import {
  GET_USER_REGISTERATION_OTP,
  USER_LOGIN,
  USER_REGISTERATION_OTP,
  VERIFICATION_CODE,
  VERIFY_USER_REGISTERATION_OTP,
  FORGET_PASSWORD,
  RESET_PASSWORD,
  VERIFY_FORGET_PASSWORD_OTP,
  USER_LOGOUT,
  USER_ONBOARDING,
  VERIFY_2FA,
} from '@/api/routes';
import { API_URL } from '@/app/components/config';
import {
  removeCookie,
  setCookie,
  transformResponseHandler,
} from '@/utils/function';
import { createAsyncThunk } from '@reduxjs/toolkit';
import {
  loginUserTokenAction,
  logoutUserAction,
  setResetPasswordTokenAction,
  setUserDetailsAction,
} from '../actions/authActions';
import { cookiesKey } from '@/utils/cookiesStorageKeys';

export const getVerificationCodeAsync = createAsyncThunk(
  'auth/getVerificationCodeAsync',
  async (paylaod, thunkAPI) => {
    try {
      const response = await post(VERIFICATION_CODE, paylaod, false);
      transformResponseHandler(response);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getOtpAsync = createAsyncThunk(
  'auth/getOTP',
  async (payload, thunkAPI) => {
    try {
      const response = await post(GET_USER_REGISTERATION_OTP, payload, false);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const sendOtpAsync = createAsyncThunk(
  'auth/sendOTP',
  async (payload, thunkAPI) => {
    try {
      const response = await post(
        VERIFY_USER_REGISTERATION_OTP,
        payload,
        false
      );
      // transformResponseHandler(response);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      thunkAPI.dispatch(
        loginUserTokenAction({
          token: response?.data?.token,
        })
      );
      setCookie(cookiesKey.token, response?.data?.token);
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const verifyForgotPasswordOtp = createAsyncThunk(
  'auth/verifyOtp',
  async (payload, thunkAPI) => {
    try {
      const response = await post(VERIFY_FORGET_PASSWORD_OTP, payload, false);
      // transformResponseHandler(response);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const loginUserAsync = createAsyncThunk(
  'auth/userLogin',
  async (payload, thunkAPI) => {
    try {
      const response = await post(USER_LOGIN, payload, false);
      if (!response.status) {
        return thunkAPI.rejectWithValue(response);
      }
      if (!response?.data?.requires_2fa) {
        thunkAPI.dispatch(
          loginUserTokenAction({
            token: response?.data?.token,
          })
        );
        setCookie(cookiesKey.token, response?.data?.token);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const verify2FAAsync = createAsyncThunk(
  'auth/verify2FA',
  async (payload, thunkAPI) => {
    try {
      const response = await post(VERIFY_2FA, payload, false);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      thunkAPI.dispatch(
        loginUserTokenAction({
          token: response?.data?.token,
        })
      );
      setCookie(cookiesKey.token, response?.data?.token);

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const forgetPasswordAsync = createAsyncThunk(
  'auth/forgetPassword',
  async (payload, thunkAPI) => {
    try {
      const response = await post(FORGET_PASSWORD, payload, false);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      thunkAPI.dispatch(setUserDetailsAction({ email: payload?.email }));
      setCookie(cookiesKey.passwordResetToken, response?.data?.token);
      thunkAPI.dispatch(
        setResetPasswordTokenAction({ token: response?.data?.token })
      );
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const resetPasswordAsync = createAsyncThunk(
  'auth/resetPassword',
  async (payload, thunkAPI) => {
    try {
      const response = await post(RESET_PASSWORD, payload, false);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      // thunkAPI.dispatch(
      //   loginUserTokenAction({
      //    token : response?.data?.token
      //   })
      // );
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const logoutUserAsync = createAsyncThunk(
  'auth/logoutUser',
  async (payload, thunkAPI) => {
    try {
      const response = await post(USER_LOGOUT, payload, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      removeCookie(cookiesKey?.token);
      removeCookie(cookiesKey?.onboardingStatus);
      thunkAPI.dispatch(logoutUserAction({}));
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const verifyCodeAsync = createAsyncThunk(
  'auth/verifyCodeAsync',
  async ({ otpVerificationToken, otp }, thunkAPI) => {
    `${API_URL}${'v1/user/verification-code'}`;
    try {
      const response = await fetch(`${API_URL}${'/v1/user/verify-code'}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          deviceid: 'web',
          language: 'en',
        },
        body: JSON.stringify({ otpVerificationToken, otp }),
      });
      const responseData = await response.json();
      transformResponseHandler(responseData);

      if (!response.ok) {
        throw new Error('Failed to verify code');
      }
      thunkAPI.dispatch(
        loginUserTokenAction({
          user: responseData?.data,
          accessToken: responseData?.extraMeta?.accessToken,
          refreshToken: responseData?.extraMeta?.refreshToken,
        })
      );
      setCookie(cookiesKey.accessToken, responseData?.extraMeta?.accessToken);
      return responseData;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);
export const resendVerifyCodeAsync = createAsyncThunk(
  'auth/resendVerifyCodeAsync',
  async ({ mobile, phoneCode, signInType }, thunkAPI) => {
    try {
      const response = await fetch(
        `${API_URL}${'/v1/user/verification-code/resend'}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            deviceid: 'web',
            language: 'en',
          },
          body: JSON.stringify({ mobile, phoneCode, signInType }),
        }
      );
      const responseData = await response.json();
      transformResponseHandler(responseData);

      if (!response.ok) {
        throw new Error('Failed to verify code');
      }
      return responseData;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);
