// api/authApi.js

import { get, getFromAdmin, post, put } from '@/api';
import {
  ADD_ADDRESS,
  ADD_ATTRIBUTES,
  ADD_CATEGORY,
  ADD_GENERAL_INFO,
  ADD_PRODUCT_DESCRIPTION,
  ADD_PRODUCT_META_INFO,
  ADD_PRODUCT_POLICIES_INFO,
  ADD_PRODUCT_PRICING,
  ADD_PRODUCT_PROMOTIONS,
  ADD_PRODUCT_SHIPPING_INFO,
  ARCHIEVE_PRODUCT,
  CREATE_NEW_PRODUCT,
  CREATE_PRODUCT,
  CREATE_VARIANT,
  EDIT_PRODUCT,
  GET_ADDRESS,
  GET_ATTRIBUTES,
  GET_BRANDS,
  GET_PRODUCT_DETAILS,
  GET_COUNTRY_OF_ORIGIN,
  GET_EXTERNAL_PRODUCT_ID_TYPE,
  GET_PRODUCT_STYLES,
  GET_PRODUCTS,
  GET_TAXATION_TYPES,
  GET_UNIT_MEASUREMENTS,
  GET_UNIT_WEIGHTS,
  GET_UNITS,
  UPDATE_PREFERENCE,
  GET_PROD_COMBO_ID,
  GET_VARIANTS,
  UPDATE_PRODUCT_COMBO_DETAILS,
  ADD_GENERAL_INFO_EDIT,
  ADD_PROD_COMBO_GEN_INFO,
  GET_VALUE_COMBOS_LIST,
  PROD_COMBO_GEN_INFO_EDIT,
} from '@/api/routes';

import { createAsyncThunk } from '@reduxjs/toolkit';

export const getProductsAsync = createAsyncThunk(
  'manage-products/fetch',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_PRODUCTS, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getVariantsAsync = createAsyncThunk(
  'Variants/fetch',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_VARIANTS, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getBrandsAsync = createAsyncThunk(
  'product/brands',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_BRANDS, {}, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getAttributesAsync = createAsyncThunk(
  'product/attributes',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_ATTRIBUTES, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

function convertToFormData(obj, form = new FormData(), parentKey = '') {
  for (const key in obj) {
    if (!obj.hasOwnProperty(key)) continue;

    const value = obj[key];
    const fullKey = parentKey
      ? Array.isArray(obj)
        ? `${parentKey}[${key}]`
        : `${parentKey}[${key}]`
      : key;

    if (value instanceof File) {
      form.append(fullKey, value);
    } else if (Array.isArray(value)) {
      if (value.length === 0) {
        form.append(`${fullKey}`, '');
      } else {
        value.forEach((item, index) => {
          const arrayKey = `${fullKey}[${index}]`;
          if (typeof item === 'object' && item !== null) {
            convertToFormData(item, form, arrayKey);
          } else {
            form.append(arrayKey, item);
          }
        });
      }
    } else if (typeof value === 'object' && value !== null) {
      convertToFormData(value, form, fullKey);
    } else if (value !== undefined && value !== null) {
      form.append(fullKey, value);
    }
  }

  return form;
}

function convertToFormDataAttributes(
  obj,
  form = new FormData(),
  parentKey = ''
) {
  for (const key in obj) {
    const value = obj[key];
    const fullKey = parentKey ? `${parentKey}[${key}]` : key;

    console.log(fullKey, 'FULLKEY');

    if (value instanceof File) {
      form.append(fullKey, value);
    } else if (Array.isArray(value)) {
      if (value.length === 0) {
        form.append(fullKey, []); // append an empty entry
      } else {
        value.forEach((item, index) => {
          if (item instanceof File) {
            form.append(`${fullKey}[${index}]`, item);
          } else if (item?.file instanceof File) {
            form.append(`${fullKey}[${index}]`, item.file);
          } else if (typeof item === 'object') {
            convertToFormDataAttributes(item, form, `${fullKey}[${index}]`);
          } else {
            form.append(`${fullKey}[${index}]`, item);
          }
        });
      }
    } else if (typeof value === 'object' && value !== null) {
      convertToFormDataAttributes(value, form, fullKey);
    } else if (value !== undefined && value !== null) {
      form.append(fullKey, value);
    }
  }
  return form;
}

export const createProductAsync = createAsyncThunk(
  'product/create',
  async (payload, thunkAPI) => {
    try {
      const formData = convertToFormData(payload);

      const response = await post(CREATE_PRODUCT, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const editProductAsync = createAsyncThunk(
  'product/edit',
  async (payload, thunkAPI) => {
    const { productId, ...data } = payload;

    const formData = convertToFormData(data);
    formData.append('_method', 'PUT');
    try {
      const response = await post(
        `${EDIT_PRODUCT}/${productId}`,
        formData,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProductAsync = createAsyncThunk(
  'product/get',
  async (payload, thunkAPI) => {
    const { productId } = payload;
    try {
      const response = await get(
        `${GET_PRODUCTS}/${productId}`,
        {},
        false,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const addAttributeAsync = createAsyncThunk(
  'product/attributes',
  async (payload, thunkAPI) => {
    const { attribute_id, ...modifiedPayload } = payload;

    try {
      let response;

      if (payload.type === 'image') {
        const formData = new FormData();
        formData.append('name', payload.name);
        formData.append('value', payload.value); // assuming this is the actual image file
        formData.append('type', payload.type);

        response = await post(
          `${ADD_ATTRIBUTES}/${attribute_id}`,
          formData,
          true,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
          }
        );
      } else {
        response = await post(
          `${ADD_ATTRIBUTES}/${attribute_id}`,
          modifiedPayload,
          true
        );
      }

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const createPickupAddress = createAsyncThunk(
  'pickupAddress/create',
  async (payload, thunkAPI) => {
    try {
      const response = await post(ADD_ADDRESS, payload, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getPickupAddress = createAsyncThunk(
  'pickupAddress/get',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_ADDRESS, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProductStyles = createAsyncThunk(
  'productStyles/get',
  async (payload, thunkAPI) => {
    try {
      const response = await getFromAdmin(GET_PRODUCT_STYLES, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getExternalProductIdType = createAsyncThunk(
  'externalProdcutIdType/get',
  async (payload, thunkAPI) => {
    try {
      const response = await getFromAdmin(
        GET_EXTERNAL_PRODUCT_ID_TYPE,
        {},
        false,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// export const createPickupAddress = createAsyncThunk(
//   'pickupAddress/create',
//   async (payload, thunkAPI) => {
//     try {
//       const response = await post(ADD_ADDRESS, payload, true);

//       if (!response.ok) {
//         return thunkAPI.rejectWithValue(response.message);
//       }
//       return response;
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error.message);
//     }
//   }
// )

export const getProdcutUnits = createAsyncThunk(
  'productUnits/get',
  async (thunkAPI) => {
    try {
      const response = await get(GET_UNITS, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProdcutUnitsMeasurements = createAsyncThunk(
  'productUnitsMeasurements/get',
  async (thunkAPI) => {
    try {
      const response = await get(GET_UNIT_MEASUREMENTS, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProdcutUnitsWeigths = createAsyncThunk(
  'productUnitsWeigths/get',
  async (thunkAPI) => {
    try {
      const response = await get(GET_UNIT_WEIGHTS, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const archieveProductAsync = createAsyncThunk(
  'product/archieve',
  async (payload, thunkAPI) => {
    const { productId } = payload;
    try {
      const response = await post(
        `${GET_PRODUCTS}/${productId}/${ARCHIEVE_PRODUCT}`,
        {},
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const updateProductPreferencesAsync = createAsyncThunk(
  'product/preferences',
  async (payload, thunkAPI) => {
    const { pageIdentifier, ...data } = payload;

    try {
      const response = await put(
        `${UPDATE_PREFERENCE}/${pageIdentifier}`,
        data,
        true
      );

      // if (!response.ok) {
      //   return thunkAPI.rejectWithValue(response.message);
      // }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// export const onboardingGetPhoneOtp = createAsyncThunk(
//   'onboarding/getPhoneOtp',
//   async (payload, thunkAPI) => {
//     try {
//       const response = await post(GET_PHONE_OTP ,payload, true)
//       if (!response.ok) {
//         return thunkAPI.rejectWithValue(response.message);
//       }
//       return response;
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error.message);
//     }
//   }
// );

// export const onboardingVerifyPhoneOtp = createAsyncThunk(
//   'onboarding/verifyphoneOtp',
//   async (payload, thunkAPI) => {
//     try {
//       const response = await post(VERIFY_PHONE_OTP,payload,true)
//       if (!response.ok) {
//         return thunkAPI.rejectWithValue(response.message);
//       }
//       return response;
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error.message);
//     }
//   }
// );

// export const vendorFeedbackAsync = createAsyncThunk(
//   'onboarding/vendorFeedbackAsync',
//   async (payload, thunkAPI) => {
//     try {
//       const response = await post(FEEDBACK_VENDOR,payload,true)
//       if (!response.ok) {
//         return thunkAPI.rejectWithValue(response.message);
//       }
//       return response;
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error.message);
//     }
//   }
// );

export const addCategoryAsync = createAsyncThunk(
  'category/add',
  async (payload, thunkAPI) => {
    try {
      const response = await post(ADD_CATEGORY, payload, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const createAttributesVariantsAsync = createAsyncThunk(
  'variants/create',
  async (payload, thunkAPI) => {
    const { productId, ...restPayload } = payload;
    try {
      const formData = convertToFormDataAttributes(restPayload);

      const response = await post(
        `${CREATE_NEW_PRODUCT}/${productId}${CREATE_VARIANT}`,
        formData,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProductDetailsAsync = createAsyncThunk(
  'productDetails/get',
  async (payload, thunkAPI) => {
    try {
      const url = GET_PRODUCT_DETAILS.replace('{id}', payload);
      const response = await get(url, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitGeneralInfo = createAsyncThunk(
  'generalInfo/add',
  async (payload, thunkAPI) => {
    try {
      let url 
      let formData
      if(payload.product_id){
        const { product_id, ...modifiedPayload } = payload;
        formData = convertToFormDataAttributes(modifiedPayload);
        url = ADD_GENERAL_INFO_EDIT.replace('{id}', product_id);
      }else{
         formData = convertToFormDataAttributes(payload);
         url = ADD_GENERAL_INFO
      }   

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitPricing = createAsyncThunk(
  'productPricing/add',
  async (payload, thunkAPI) => {
    try {
      const { product_id, ...modifiedPayload } = payload;
      const formData = convertToFormData(modifiedPayload);
      const url = ADD_PRODUCT_PRICING.replace('{id}', product_id);

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitProductDescription = createAsyncThunk(
  'productDescription/add',
  async (payload, thunkAPI) => {
    try {
      const { product_id, ...modifiedPayload } = payload;
      const formData = convertToFormData(modifiedPayload);
      const url = ADD_PRODUCT_DESCRIPTION.replace('{id}', product_id);

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitProductPromotions = createAsyncThunk(
  'productPromotions/add',
  async (payload, thunkAPI) => {
    try {
      const { product_id, ...modifiedPayload } = payload;
      const formData = convertToFormData(modifiedPayload);
      const url = ADD_PRODUCT_PROMOTIONS.replace('{id}', product_id);

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitProductShippingInfo = createAsyncThunk(
  'productShippingInfo/add',
  async (payload, thunkAPI) => {
    try {
      const { product_id, ...modifiedPayload } = payload;
      const formData = convertToFormData(modifiedPayload);
      const url = ADD_PRODUCT_SHIPPING_INFO.replace('{id}', product_id);

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitProductPoliciesInfo = createAsyncThunk(
  'productPoliciesInfo/add',
  async (payload, thunkAPI) => {
    try {
      const { product_id, ...modifiedPayload } = payload;
      const formData = convertToFormData(modifiedPayload);
      const url = ADD_PRODUCT_POLICIES_INFO.replace('{id}', product_id);

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const submitProductMetaInfo = createAsyncThunk(
  'productMetaInfo/add',
  async (payload, thunkAPI) => {
    try {
      const { product_id, ...modifiedPayload } = payload;
      const formData = convertToFormData(modifiedPayload);
      const url = ADD_PRODUCT_META_INFO.replace('{id}', product_id);

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getCountryOfOrigin = createAsyncThunk(
  'countryOfOrigin/get',
  async (thunkAPI) => {
    try {
      const response = await getFromAdmin(
        GET_COUNTRY_OF_ORIGIN,
        {},
        false,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProductDetails = createAsyncThunk(
  'productDetails/get',
  async (payload, thunkAPI) => {
    try {
      const url = GET_PRODUCT_DETAILS.replace('{id}', payload);
      const response = await get(url, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getTaxationTypes = createAsyncThunk(
  'taxationTypeGet/get',
  async (thunkAPI) => {
    try {
      const response = await getFromAdmin(GET_TAXATION_TYPES, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const addProdCombosGenInfo = createAsyncThunk(
  'addProdCombosGenInfo/add',
  async (payload, thunkAPI) => {
    try {
      let response;
      if (payload?.combo_id) {
        const updatedPayload = { ...payload };
        delete updatedPayload.combo_id;

        response = await post(
          PROD_COMBO_GEN_INFO_EDIT.replace('{id}', payload?.combo_id),
          updatedPayload,
          true
        );
      } else {
        const updatedPayload = { ...payload };
        delete updatedPayload.combo_id;
        response = await post(ADD_PROD_COMBO_GEN_INFO, updatedPayload, true);
      }

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getProdComboDetails = createAsyncThunk(
  'getProdComboDetails/get',
  async (payload, thunkAPI) => {
    try {
      const url = GET_PROD_COMBO_ID.replace('{id}', payload);
      const response = await get(url, {}, false, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const updateProdComboDetails = createAsyncThunk(
  'productMetaInfo/add',
  async (payload, thunkAPI) => {
    try {
      const { product_combo_id, ...modifiedPayload } = payload;
      const formData = modifiedPayload;
      const url = UPDATE_PRODUCT_COMBO_DETAILS.replace(
        '{id}',
        product_combo_id
      );

      const response = await post(url, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getValueCombosListAsync = createAsyncThunk(
  'valueCombo/fetch',
  async (payload, thunkAPI) => {
    try {
      const response = await get(GET_VALUE_COMBOS_LIST, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);
