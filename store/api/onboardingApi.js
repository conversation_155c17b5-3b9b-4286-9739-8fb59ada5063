// api/authApi.js

import { get, post, put } from '@/api';
import {
  USER_ONBOARDING,
  VENDOR_ONBOARDING,
  INDUSTRY_TAGS,
  VENDOR_DETAILS,
  GET_PHONE_OTP,
  VERIFY_PHONE_OTP,
  FEEDBACK_VENDOR,
} from '@/api/routes';

import { createAsyncThunk } from '@reduxjs/toolkit';
import { updateStepAction } from '../actions/authActions';

export const onboardingUserAsync = createAsyncThunk(
  'user/onboarding',
  async (payload, thunkAPI) => {
    try {
      const response = await get(USER_ONBOARDING, {}, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const onboardingVenderAsync = createAsyncThunk(
  'user/onboardingVender',
  async (payload, thunkAPI) => {
    try {
      // Create FormData
      const formData = new FormData();

      Object.entries(payload).forEach(([key, value]) => {
        if (Array.isArray(value)) {
          if (value.every((item) => typeof item === 'object' && item !== null)) {
            formData.append(key, JSON.stringify(value));
          } else {

            value.forEach((item, index) => {
              formData.append(`${key}[${index}]`, item);
            });
          }
        } else if (typeof value === 'object' && value !== null) {
          formData.append(key, value);
        } else {
          formData.append(key, value);
        }
      });

      const response = await post(VENDOR_ONBOARDING, formData, true);

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);


export const getIndustryTagsAsync = createAsyncThunk(
  'user/getIndustryTags',
  async (payload, thunkAPI) => {
    try {
      const response = await post(INDUSTRY_TAGS, payload, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getVendorDetailsAsync = createAsyncThunk(
  'user/vendorDetails',
  async (payload, thunkAPI) => {
    try {
      const response = await get(VENDOR_DETAILS, {}, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const onboardingGetPhoneOtp = createAsyncThunk(
  'onboarding/getPhoneOtp',
  async (payload, thunkAPI) => {
    try {
      const response = await post(GET_PHONE_OTP, payload, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const onboardingVerifyPhoneOtp = createAsyncThunk(
  'onboarding/verifyphoneOtp',
  async (payload, thunkAPI) => {
    try {
      const response = await post(VERIFY_PHONE_OTP, payload, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const vendorFeedbackAsync = createAsyncThunk(
  'onboarding/vendorFeedbackAsync',
  async (payload, thunkAPI) => {
    try {
      const response = await post(FEEDBACK_VENDOR, payload, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);
