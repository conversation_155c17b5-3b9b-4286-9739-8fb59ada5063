import { get, remove } from '@/api';
import { LIST_ALL_VENDORS} from '@/api/routes';
import { createAsyncThunk } from '@reduxjs/toolkit';

export const fetchVendorAsync = createAsyncThunk(
  'vendors/fetchAll',
  async (payload, thunkAPI) => {
    try {
      const response = await get(LIST_ALL_VENDORS, payload, false, true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const getVendorDetailsAsync = createAsyncThunk(
  'vendors/fetchDetails',
  async (payload, thunkAPI) => {
    try {
      const response = await get(
        `${LIST_ALL_VENDORS}/${payload}`, '', '', true);
      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }
      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

export const approveRejectVendor = createAsyncThunk(
  'vendors/approveReject',
  async (payload, thunkAPI) => {
    console.log(payload, "ppp");
    try {
      const { id, status } = payload; // e.g., { id: 'abc', status: 'approved' }

      const response = await put(
        `${LIST_ALL_VENDORS}/${id}/approve-reject`,
        { status }, // send payload in body
        false,
        true
      );

      if (!response.ok) {
        return thunkAPI.rejectWithValue(response.message);
      }

      return response;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

// export const deleteAttributesAsync = createAsyncThunk(
//   'catalogueManagement/delete',
//   async ( payload , thunkAPI) => {
//     try {
//       const response = await remove(
//         `${LIST_ALL_ATTRIBUTES}/${payload}`,
//         true,
//         true
//       );

//       if (!response.ok) {
//         return thunkAPI.rejectWithValue(response.message);
//       }

//       return response;
//     } catch (error) {
//       return thunkAPI.rejectWithValue(error.message);
//     }
//   }
// );