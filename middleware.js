import { NextResponse } from 'next/server';
import { cookiesKey } from './utils/cookiesStorageKeys';

const protectedRoutes = [
  '/business-details',
  '/under-review',
  '/dashboard',
  '/vendors-hub',
  '/manage-products',
  '/manage-products/create',
  '/retail-orders',
  '/bulk-orders',
  '/procurement',
];
const publicRoutes = ['/forget-password', '/reset-password', '/login'];

export function middleware(request) {
  // Extract cookies
  const authToken = request.cookies.get(cookiesKey?.token)?.value;
  const resetPasswordToken = request.cookies.get(
    cookiesKey?.passwordResetToken
  )?.value;

  // Extract page url/pathname
  const url = request.nextUrl.clone();

  // Redirect logged-in users away from the public routes
  if (publicRoutes.includes(url.pathname) && authToken) {
    return NextResponse.redirect(new URL('/', request.url)); // Redirect to "/" (which is your registration page)
  }

  // Check if the requested URL is a protected route
  if (protectedRoutes.includes(url.pathname) && !authToken) {
    // If no authToken, redirect to home (registration) page
    return NextResponse.redirect(new URL('/', request.url));
  }

  // Redirect logged-in users away from the login page
  if (url.pathname === '/' && authToken) {
    return NextResponse.redirect(new URL('/dashboard', request.url)); // Redirect to landing or any other page
  }

  // Redirect to home if user is trying to access /reset-password without resetPasswordToken
  if (url.pathname.startsWith('/reset-password') && !resetPasswordToken) {
    return NextResponse.redirect(new URL('/', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/',
    '/business-details',
    '/under-review',
    '/dashboard',
    '/vendors-hub',
    '/manage-products',
    '/manage-products/create',
    '/retail-orders',
    '/bulk-orders',
    '/procurement',
    '/forget-password',
    '/reset-password',
    '/login',
  ],
};
