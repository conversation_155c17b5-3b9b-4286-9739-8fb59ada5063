apiVersion: apps/v1
kind: Deployment
metadata:
  name: hubsups-admin-dev
  labels:
    app: hubsups-admin-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: hubsups-admin-dev
  template:
    metadata:
      labels:
        app: hubsups-admin-dev
    spec:
      imagePullSecrets:
        - name: creddocker
      containers:
        - name: hubsups-admin-dev
          image: harbor.indianic.com/hubsups/nextjs-admin-dev:latest
          ports:
            - containerPort: 3000
          imagePullPolicy: Always
---
apiVersion: v1
kind: Service
metadata:
  name: hubsups-admin-dev
  labels:
    app: hubsups-admin-dev
spec:
  selector:
    app: hubsups-admin-dev
  ports:
    - port: 3000
      targetPort: 3000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hubsups-admin-dev-ingress
spec:
  #tls:
  #- hosts:
  #- test.example.com
  #secretName: nginx-tls-secret
  rules:
    - host: hubsups-admin-dev.devpress.net
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: hubsups-admin-dev
                port:
                  number: 3000
