'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import ComingSoon from '../components/coming-soon/ComingSoon';

const BuyerManagement = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);  
  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        {/* Add this state at the top of the Products component */}

        {/* Update the sidebar component to pass the state */}
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="sm:flex bg-white rounded-xl w-[calc(100% - 12px)]">
            <div className="text-center w-full max-w-6xl mx-auto h-[calc(100vh-108px)] flex items-center justify-center flex-col gap-3">
              <ComingSoon />
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default BuyerManagement;
