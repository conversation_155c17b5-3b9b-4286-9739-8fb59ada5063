'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import WarehouseDirectoryTab from './components/WarehouseDirectoryTab';
import ProductInventoryTab from './components/ProductInventoryTab';
import StockLogsTab from './components/StockLogsTab';
import StockTransferTab from './components/StockTransferTab';
import LowStockAlertsTab from './components/LowStockAlertsTab';
import InventoryAdjustmentTab from './components/InventoryAdjustmentTab';

const Inventory = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('warehouse-directory');

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Inventory & Warehouse' }
  ];

  const tabs = [
    { id: 'warehouse-directory', label: 'Warehouse Directory' },
    { id: 'product-inventory', label: 'Product Inventory'},
    { id: 'stock-logs', label: 'Stock Logs'},
    { id: 'stock-transfer', label: 'Stock Transfer'},
    { id: 'low-stock-alerts', label: 'Low Stock Alerts'},
    { id: 'inventory-adjustment', label: 'Inventory Adjustment'}
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'warehouse-directory':
        return <WarehouseDirectoryTab />;
      case 'product-inventory':
        return <ProductInventoryTab />;
      case 'stock-logs':
        return <StockLogsTab />;
      case 'stock-transfer':
        return <StockTransferTab />;
      case 'low-stock-alerts':
        return <LowStockAlertsTab />;
      case 'inventory-adjustment':
        return <InventoryAdjustmentTab />;
      default:
        return <WarehouseDirectoryTab />;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-2xl font-bold text-dark-500">Inventory & Warehouse Management</h1>
              {/* <p className="text-sm text-gray-500">Manage warehouses, track inventory, and monitor stock levels</p> */}
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          <div className="bg-white rounded-xl w-[calc(100% - 12px)]">
            {/* Tabs */}
            <div className="border-b border-border-color">
              <div className="flex overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-3 xl:px-6 py-3 xl:py-4 text-sm font-semibold whitespace-nowrap border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-500'
                        : 'border-transparent text-gray-300 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {/* <span className={`${tab.icon} text-base`} /> */}
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-4 xl:p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Inventory;
