'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Image from 'next/image';
import FilterField from '../../components/table/FilterField';
import InventoryDetailModal from './InventoryDetailModal';
import AdjustStockModal from './AdjustStockModal';
import TransferStockModal from './TransferStockModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ProductInventoryTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedInventory, setSelectedInventory] = useState(null);
  const [isInventoryDetailOpen, setIsInventoryDetailOpen] = useState(false);
  const [isAdjustStockOpen, setIsAdjustStockOpen] = useState(false);
  const [isTransferStockOpen, setIsTransferStockOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Sample data for product inventory
  const [inventory, setInventory] = useState([
    {
      id: 'INV-001',
      productName: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      variant: 'Black - Standard',
      warehouseName: 'Main Distribution Center',
      warehouseId: 'WH-001',
      availableStock: 150,
      reservedStock: 25,
      inTransit: 50,
      damagedStock: 5,
      reorderLevel: 50,
      category: 'Electronics',
      thumbnail: '/images/product-3.jpg',
      lastUpdated: '2024-01-20T14:20:00Z',
      stockStatus: 'In Stock'
    },
    {
      id: 'INV-002',
      productName: 'Cotton T-Shirt',
      sku: 'CTS-002',
      variant: 'Red - Medium',
      warehouseName: 'East Coast Fulfillment',
      warehouseId: 'WH-002',
      availableStock: 25,
      reservedStock: 10,
      inTransit: 0,
      damagedStock: 2,
      reorderLevel: 50,
      category: 'Clothing',
      thumbnail: '/images/product-2.jpg',
      lastUpdated: '2024-01-19T16:45:00Z',
      stockStatus: 'Low Stock'
    },
    {
      id: 'INV-003',
      productName: 'Office Chair Bundle',
      sku: 'OCB-003',
      variant: null,
      warehouseName: 'Vendor Storage Facility',
      warehouseId: 'WH-003',
      availableStock: 0,
      reservedStock: 0,
      inTransit: 15,
      damagedStock: 0,
      reorderLevel: 10,
      category: 'Furniture',
      thumbnail: '/images/product-1.jpg',
      lastUpdated: '2024-01-18T13:30:00Z',
      stockStatus: 'Out of Stock'
    },
    {
      id: 'INV-004',
      productName: 'Smartphone Case',
      sku: 'SPC-004',
      variant: 'iPhone 14 - Clear',
      warehouseName: 'Main Distribution Center',
      warehouseId: 'WH-001',
      availableStock: 300,
      reservedStock: 45,
      inTransit: 0,
      damagedStock: 8,
      reorderLevel: 100,
      category: 'Accessories',
      thumbnail: '/images/product-3.jpg',
      lastUpdated: '2024-01-21T10:15:00Z',
      stockStatus: 'In Stock'
    },
    {
      id: 'INV-005',
      productName: 'Gaming Laptop',
      sku: 'GL-005',
      variant: null,
      warehouseName: 'East Coast Fulfillment',
      warehouseId: 'WH-002',
      availableStock: 5,
      reservedStock: 2,
      inTransit: 10,
      damagedStock: 1,
      reorderLevel: 15,
      category: 'Computers',
      thumbnail: '/images/product-1.jpg',
      lastUpdated: '2024-01-16T12:00:00Z',
      stockStatus: 'Critical'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Products', count: inventory.length },
    { id: 'In Stock', label: 'In Stock', count: inventory.filter(i => i.stockStatus === 'In Stock').length },
    { id: 'Low Stock', label: 'Low Stock', count: inventory.filter(i => i.stockStatus === 'Low Stock').length },
    { id: 'Out of Stock', label: 'Out of Stock', count: inventory.filter(i => i.stockStatus === 'Out of Stock').length },
    { id: 'Critical', label: 'Critical', count: inventory.filter(i => i.stockStatus === 'Critical').length }
  ];

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         item.sku.toLowerCase().includes(filterText.toLowerCase()) ||
                         item.warehouseName.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || item.stockStatus === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const getStockStatusColor = (status) => {
    switch (status) {
      case 'In Stock':
        return 'bg-green-100 text-green-800';
      case 'Low Stock':
        return 'bg-yellow-100 text-yellow-800';
      case 'Out of Stock':
        return 'bg-red-100 text-red-800';
      case 'Critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-400">{row.sku}</div>
            {row.variant && (
              <div className="text-xs text-gray-300">{row.variant}</div>
            )}
          </div>
        </div>
      ),
      width: '280px',
    },
    {
      name: 'Warehouse',
      selector: row => row.warehouseName,
      sortable: true,
      width: '180px',
    },
    {
      name: 'Available',
      selector: row => row.availableStock,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium ${
          row.availableStock <= row.reorderLevel ? 'text-red-600' : 
          row.availableStock <= row.reorderLevel * 2 ? 'text-yellow-600' : 
          'text-green-600'
        }`}>
          {row.availableStock}
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Reserved',
      selector: row => row.reservedStock,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-gray-500">{row.reservedStock}</span>
      ),
      // width: '100px',
    },
    {
      name: 'In Transit',
      selector: row => row.inTransit,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-info-600">{row.inTransit}</span>
      ),
      // width: '100px',
    },
    {
      name: 'Damaged',
      selector: row => row.damagedStock,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-red-600">{row.damagedStock}</span>
      ),
      // width: '100px',
    },
    {
      name: 'Reorder Level',
      selector: row => row.reorderLevel,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-gray-500">{row.reorderLevel}</span>
      ),
      // width: '120px',
    },
    {
      name: 'Status',
      selector: row => row.stockStatus,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStockStatusColor(row.stockStatus)}`}>
          {row.stockStatus}
        </span>
      ),
      width: '120px',
    },
    {
      name: 'Actions',
      // grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewInventory(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleAdjustStock(row)}
            data-tooltip-id="adjust-tooltip"
            data-tooltip-content="Adjust Stock"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-wrench text-base" />
          </button>
          <button
            onClick={() => handleTransferStock(row)}
            data-tooltip-id="transfer-tooltip"
            data-tooltip-content="Transfer Stock"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-arrows-left-right text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="adjust-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="transfer-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // width: '140px',
    },
  ];

  const handleViewInventory = (inventoryItem) => {
    setSelectedInventory(inventoryItem);
    setIsInventoryDetailOpen(true);
  };

  const handleAdjustStock = (inventoryItem) => {
    setSelectedInventory(inventoryItem);
    setIsAdjustStockOpen(true);
  };

  const handleTransferStock = (inventoryItem) => {
    setSelectedInventory(inventoryItem);
    setIsTransferStockOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Product Inventory Tracking</h2>
          <span className="text-sm text-gray-300">
            {filteredInventory.length} of {inventory.length} products
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-download-simple font-normal text-lg" />
            Bulk Import
          </button>
          <button className="btn btn-primary inline-flex items-center gap-2">
            <span className="icon icon-arrows-clockwise font-normal text-lg" />
            Sync Inventory
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search"
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredInventory}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-package text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No inventory found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <InventoryDetailModal
        isOpen={isInventoryDetailOpen}
        onClose={() => setIsInventoryDetailOpen(false)}
        inventory={selectedInventory}
      />

      <AdjustStockModal
        isOpen={isAdjustStockOpen}
        onClose={() => setIsAdjustStockOpen(false)}
        inventory={selectedInventory}
      />

      <TransferStockModal
        isOpen={isTransferStockOpen}
        onClose={() => setIsTransferStockOpen(false)}
        inventory={selectedInventory}
      />
    </div>
  );
};

export default ProductInventoryTab;
