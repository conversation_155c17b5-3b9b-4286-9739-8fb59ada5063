'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Image from 'next/image';
import Swal from 'sweetalert2';
import FilterField from '../../components/table/FilterField';
import TransferDetailModal from './TransferDetailModal';
import CreateTransferModal from './CreateTransferModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const StockTransferTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [isTransferDetailOpen, setIsTransferDetailOpen] = useState(false);
  const [isCreateTransferOpen, setIsCreateTransferOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Sample data for stock transfers
  const [transfers, setTransfers] = useState([
    {
      id: 'TRF-001',
      transferId: 'TRF-001',
      transferDate: '2024-01-20T10:30:00Z',
      productName: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      variant: 'Black - Standard',
      sourceWarehouse: 'Main Distribution Center',
      destinationWarehouse: 'East Coast Fulfillment',
      quantity: 50,
      status: 'Received',
      receivedDate: '2024-01-22T14:20:00Z',
      transportDetails: 'FedEx Ground - Tracking: 1234567890',
      remarks: 'Urgent transfer for high demand region',
      processedBy: 'John Smith',
      thumbnail: '/images/product-3.jpg'
    },
    {
      id: 'TRF-002',
      transferId: 'TRF-002',
      transferDate: '2024-01-19T14:20:00Z',
      productName: 'Cotton T-Shirt',
      sku: 'CTS-002',
      variant: 'Red - Medium',
      sourceWarehouse: 'Vendor Storage Facility',
      destinationWarehouse: 'Main Distribution Center',
      quantity: 100,
      status: 'In Transit',
      receivedDate: null,
      transportDetails: 'UPS Ground - Tracking: 9876543210',
      remarks: 'Regular stock rebalancing',
      processedBy: 'Sarah Johnson',
      thumbnail: '/images/product-2.jpg'
    },
    {
      id: 'TRF-003',
      transferId: 'TRF-003',
      transferDate: '2024-01-18T16:45:00Z',
      productName: 'Office Chair Bundle',
      sku: 'OCB-003',
      variant: null,
      sourceWarehouse: 'East Coast Fulfillment',
      destinationWarehouse: 'Seasonal Storage Hub',
      quantity: 25,
      status: 'Pending',
      receivedDate: null,
      transportDetails: 'Internal Transport - Vehicle: VH-001',
      remarks: 'Moving excess inventory to seasonal storage',
      processedBy: 'Mike Wilson',
      thumbnail: '/images/product-1.jpg'
    },
    {
      id: 'TRF-004',
      transferId: 'TRF-004',
      transferDate: '2024-01-17T11:15:00Z',
      productName: 'Smartphone Case',
      sku: 'SPC-004',
      variant: 'iPhone 14 - Clear',
      sourceWarehouse: 'Main Distribution Center',
      destinationWarehouse: 'Vendor Storage Facility',
      quantity: 75,
      status: 'Cancelled',
      receivedDate: null,
      transportDetails: 'DHL Express - Cancelled due to address issue',
      remarks: 'Cancelled due to incorrect destination address',
      processedBy: 'Lisa Brown',
      thumbnail: '/images/product-3.jpg'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Transfers', count: transfers.length },
    { id: 'Pending', label: 'Pending', count: transfers.filter(t => t.status === 'Pending').length },
    { id: 'In Transit', label: 'In Transit', count: transfers.filter(t => t.status === 'In Transit').length },
    { id: 'Received', label: 'Received', count: transfers.filter(t => t.status === 'Received').length },
    { id: 'Cancelled', label: 'Cancelled', count: transfers.filter(t => t.status === 'Cancelled').length }
  ];

  const filteredTransfers = transfers.filter(transfer => {
    const matchesSearch = transfer.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         transfer.sku.toLowerCase().includes(filterText.toLowerCase()) ||
                         transfer.transferId.toLowerCase().includes(filterText.toLowerCase()) ||
                         transfer.sourceWarehouse.toLowerCase().includes(filterText.toLowerCase()) ||
                         transfer.destinationWarehouse.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || transfer.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'In Transit':
        return 'bg-info-500/20 text-info-500';
      case 'Received':
        return 'bg-green-100 text-green-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Transfer ID',
      selector: row => row.transferId,
      sortable: true,
      cell: (row) => (
        <div className="py-2">
          <div className="font-medium text-sm">{row.transferId}</div>
          <div className="text-xs text-gray-300">{formatDate(row.transferDate)}</div>
        </div>
      ),
      width: '140px',
    },
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-400">{row.sku}</div>
            {row.variant && (
              <div className="text-xs text-gray-300">{row.variant}</div>
            )}
          </div>
        </div>
      ),
      width: '280px',
    },
    {
      name: 'From → To',
      selector: row => row.sourceWarehouse,
      sortable: true,
      cell: (row) => (
        <div className="py-2">
          <div className="text-sm">{row.sourceWarehouse}</div>
          <div className="flex items-center gap-1 text-xs text-gray-300">
            <span className="icon icon-arrow-right" />
          </div>
          <div className="text-sm">{row.destinationWarehouse}</div>
        </div>
      ),
      width: '240px',
    },
    {
      name: 'Quantity',
      selector: row => row.quantity,
      sortable: true,
      cell: (row) => (
        <span className="font-medium text-sm">{row.quantity}</span>
      ),
      // width: '100px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.status)}`}>
          {row.status}
        </span>
      ),
      // width: '120px',
    },
    {
      name: 'Transport',
      selector: row => row.transportDetails,
      sortable: true,
      wrap: true,
      cell: (row) => (
        <div className="text-sm text-gray-500" title={row.transportDetails}>
          {row.transportDetails}
        </div>
      ),
      width: '240px',
    },
    {
      name: 'Processed By',
      selector: row => row.processedBy,
      sortable: true,
      // width: '120px',
    },
    {
      name: 'Actions',
      // grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewTransfer(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          
          {row.status === 'Pending' && (
            <button
              onClick={() => handleCancelTransfer(row.id)}
              data-tooltip-id="cancel-tooltip"
              data-tooltip-content="Cancel Transfer"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            >
              <span className="icon icon-x text-base" />
            </button>
          )}

          {row.status === 'In Transit' && (
            <button
              onClick={() => handleMarkReceived(row.id)}
              data-tooltip-id="received-tooltip"
              data-tooltip-content="Mark as Received"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-success-500/10 hover:text-success-500 rounded-lg cursor-pointer transition-base"
            >
              <span className="icon icon-check-3 text-base" />
            </button>
          )}

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="cancel-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="received-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // width: '120px',
    },
  ];

  const handleViewTransfer = (transfer) => {
    setSelectedTransfer(transfer);
    setIsTransferDetailOpen(true);
  };

  const handleCancelTransfer = async (transferId) => {
    const transfer = transfers.find(t => t.id === transferId);
    
    const result = await Swal.fire({
      title: 'Cancel Transfer',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-gray-600">Are you sure you want to cancel this stock transfer?</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="mb-2">
              <p class="text-sm font-medium text-gray-900">${transfer?.transferId || 'Unknown Transfer'}</p>
              <p class="text-xs text-gray-500">${transfer?.productName || 'N/A'} (${transfer?.sku || 'N/A'})</p>
            </div>
            <div class="text-xs text-gray-500">
              <p>From: ${transfer?.sourceWarehouse || 'N/A'}</p>
              <p>To: ${transfer?.destinationWarehouse || 'N/A'}</p>
              <p>Quantity: ${transfer?.quantity || 0} units</p>
            </div>
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc2626',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Cancel Transfer',
      cancelButtonText: 'Keep Transfer',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      setTransfers(prev => prev.map(t => 
        t.id === transferId ? { ...t, status: 'Cancelled' } : t
      ));
      
      Swal.fire({
        title: 'Cancelled!',
        text: 'Transfer has been cancelled successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });
    }
  };

  const handleMarkReceived = async (transferId) => {
    const transfer = transfers.find(t => t.id === transferId);
    
    const result = await Swal.fire({
      title: 'Mark as Received',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-gray-600">Confirm that this stock transfer has been received at the destination warehouse.</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="mb-2">
              <p class="text-sm font-medium text-gray-900">${transfer?.transferId || 'Unknown Transfer'}</p>
              <p class="text-xs text-gray-500">${transfer?.productName || 'N/A'} (${transfer?.sku || 'N/A'})</p>
            </div>
            <div class="text-xs text-gray-500">
              <p>Destination: ${transfer?.destinationWarehouse || 'N/A'}</p>
              <p>Quantity: ${transfer?.quantity || 0} units</p>
            </div>
          </div>
        </div>
      `,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b857',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Mark as Received',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      setTransfers(prev => prev.map(t => 
        t.id === transferId ? { 
          ...t, 
          status: 'Received',
          receivedDate: new Date().toISOString()
        } : t
      ));
      
      Swal.fire({
        title: 'Received!',
        text: 'Transfer has been marked as received successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });
    }
  };

  const handleCreateTransfer = (transferData) => {
    const newTransfer = {
      ...transferData,
      id: `TRF-${Date.now()}`,
      transferId: `TRF-${Date.now()}`,
      transferDate: new Date().toISOString(),
      status: 'Pending',
      receivedDate: null,
      processedBy: 'Current User' // This would come from auth context
    };
    setTransfers(prev => [newTransfer, ...prev]);
    console.log('Transfer created:', newTransfer);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Stock Transfer Between Warehouses</h2>
          <span className="text-sm text-gray-300">
            {filteredTransfers.length} of {transfers.length} transfers
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
          <button
            onClick={() => setIsCreateTransferOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-normal text-lg" />
            Create Transfer
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search"
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredTransfers}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-arrows-left-right text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No transfers found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={false}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <TransferDetailModal
        isOpen={isTransferDetailOpen}
        onClose={() => setIsTransferDetailOpen(false)}
        transfer={selectedTransfer}
      />

      <CreateTransferModal
        isOpen={isCreateTransferOpen}
        onClose={() => setIsCreateTransferOpen(false)}
        onCreateTransfer={handleCreateTransfer}
      />
    </div>
  );
};

export default StockTransferTab;
