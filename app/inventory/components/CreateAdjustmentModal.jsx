'use client';

import React, { useState, useEffect } from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const CreateAdjustmentModal = ({ isOpen, onClose, onCreateAdjustment }) => {
  const [formData, setFormData] = useState({
    productSku: '',
    productName: '',
    variant: '',
    warehouseId: '',
    existingStock: '',
    actualStockCounted: '',
    adjustmentReason: 'Audit',
    remarks: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample data - in real app this would come from API
  const products = [
    { sku: 'WBH-001', name: 'Wireless Bluetooth Headphones', variants: ['Black - Standard', 'White - Standard'], currentStock: 150 },
    { sku: 'CTS-002', name: 'Cotton T-Shirt', variants: ['Red - Small', 'Red - Medium', 'Red - Large', 'Blue - Small', 'Blue - Medium'], currentStock: 75 },
    { sku: 'OCB-003', name: 'Office Chair Bundle', variants: [], currentStock: 20 },
    { sku: 'SPC-004', name: 'Smartphone Case', variants: ['iPhone 14 - Clear', 'iPhone 14 - Black', 'iPhone 15 - Clear'], currentStock: 200 },
    { sku: 'GL-005', name: 'Gaming Laptop', variants: [], currentStock: 12 }
  ];

  const warehouses = [
    { id: 'WH-001', name: 'Main Distribution Center' },
    { id: 'WH-002', name: 'East Coast Fulfillment' },
    { id: 'WH-003', name: 'Vendor Storage Facility' },
    { id: 'WH-004', name: 'Seasonal Storage Hub' }
  ];

  const reasonOptions = [
    'Audit',
    'Shrinkage',
    'Damage',
    'Others'
  ];

  useEffect(() => {
    if (isOpen) {
      setFormData({
        productSku: '',
        productName: '',
        variant: '',
        warehouseId: '',
        existingStock: '',
        actualStockCounted: '',
        adjustmentReason: 'Audit',
        remarks: ''
      });
      setErrors({});
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle product selection
    if (name === 'productSku') {
      const selectedProduct = products.find(p => p.sku === value);
      setFormData(prev => ({
        ...prev,
        productName: selectedProduct ? selectedProduct.name : '',
        variant: '',
        existingStock: selectedProduct ? selectedProduct.currentStock.toString() : ''
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productSku) {
      newErrors.productSku = 'Please select a product';
    }

    if (!formData.warehouseId) {
      newErrors.warehouseId = 'Please select a warehouse';
    }

    if (!formData.existingStock || formData.existingStock < 0) {
      newErrors.existingStock = 'Please enter valid existing stock';
    }

    if (!formData.actualStockCounted || formData.actualStockCounted < 0) {
      newErrors.actualStockCounted = 'Please enter valid actual stock count';
    }

    if (!formData.adjustmentReason) {
      newErrors.adjustmentReason = 'Please select an adjustment reason';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedProduct = products.find(p => p.sku === formData.productSku);
      const selectedWarehouse = warehouses.find(w => w.id === formData.warehouseId);
      
      const existingStock = parseInt(formData.existingStock);
      const actualStock = parseInt(formData.actualStockCounted);
      const difference = actualStock - existingStock;

      const adjustmentData = {
        productName: selectedProduct.name,
        sku: formData.productSku,
        variant: formData.variant || null,
        warehouseName: selectedWarehouse.name,
        existingStock: existingStock,
        actualStockCounted: actualStock,
        difference: difference,
        adjustmentReason: formData.adjustmentReason,
        remarks: formData.remarks,
        thumbnail: '/images/product-1.jpg' // Default thumbnail
      };

      onCreateAdjustment(adjustmentData);
      onClose();
    } catch (error) {
      console.error('Error creating adjustment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProduct = products.find(p => p.sku === formData.productSku);
  const difference = formData.existingStock && formData.actualStockCounted 
    ? parseInt(formData.actualStockCounted) - parseInt(formData.existingStock)
    : 0;

  const getDifferenceColor = (diff) => {
    if (diff > 0) return 'text-green-600';
    if (diff < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getDifferenceIcon = (diff) => {
    if (diff > 0) return 'icon-arrow-up';
    if (diff < 0) return 'icon-arrow-down';
    return 'icon-minus';
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Create Inventory Adjustment"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Product Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product <span className="text-red-500">*</span>
              </label>
              <select
                name="productSku"
                value={formData.productSku}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.productSku ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select a product</option>
                {products.map(product => (
                  <option key={product.sku} value={product.sku}>
                    {product.sku} - {product.name}
                  </option>
                ))}
              </select>
              {errors.productSku && (
                <p className="text-red-500 text-xs mt-1">{errors.productSku}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Variant
              </label>
              <select
                name="variant"
                value={formData.variant}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                disabled={!selectedProduct || selectedProduct.variants.length === 0}
              >
                <option value="">Select variant (if applicable)</option>
                {selectedProduct?.variants.map(variant => (
                  <option key={variant} value={variant}>
                    {variant}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Warehouse Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Warehouse <span className="text-red-500">*</span>
            </label>
            <select
              name="warehouseId"
              value={formData.warehouseId}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.warehouseId ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">Select warehouse</option>
              {warehouses.map(warehouse => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </option>
              ))}
            </select>
            {errors.warehouseId && (
              <p className="text-red-500 text-xs mt-1">{errors.warehouseId}</p>
            )}
          </div>

          {/* Stock Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Existing Stock (System) <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="existingStock"
                value={formData.existingStock}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.existingStock ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Current system stock"
                min="0"
                readOnly={!!selectedProduct}
              />
              {errors.existingStock && (
                <p className="text-red-500 text-xs mt-1">{errors.existingStock}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Actual Stock Counted <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="actualStockCounted"
                value={formData.actualStockCounted}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.actualStockCounted ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Physical count result"
                min="0"
              />
              {errors.actualStockCounted && (
                <p className="text-red-500 text-xs mt-1">{errors.actualStockCounted}</p>
              )}
            </div>
          </div>

          {/* Difference Preview */}
          {formData.existingStock && formData.actualStockCounted && (
            <div className="p-4 bg-gray-50 rounded-lg border">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Calculated Difference:</span>
                <div className={`flex items-center gap-1 text-lg font-bold ${getDifferenceColor(difference)}`}>
                  <span className={`icon ${getDifferenceIcon(difference)} text-base`} />
                  {difference > 0 ? '+' : ''}{difference} units
                </div>
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {difference > 0 ? 'Stock will be increased' : 
                 difference < 0 ? 'Stock will be decreased' : 
                 'No change in stock level'}
              </div>
            </div>
          )}

          {/* Adjustment Reason */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Adjustment Reason <span className="text-red-500">*</span>
            </label>
            <select
              name="adjustmentReason"
              value={formData.adjustmentReason}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.adjustmentReason ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              {reasonOptions.map(reason => (
                <option key={reason} value={reason}>
                  {reason}
                </option>
              ))}
            </select>
            {errors.adjustmentReason && (
              <p className="text-red-500 text-xs mt-1">{errors.adjustmentReason}</p>
            )}
          </div>

          {/* Remarks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Explain the reason for this adjustment..."
            />
          </div>

          {/* Adjustment Summary */}
          {formData.productSku && formData.warehouseId && formData.existingStock && formData.actualStockCounted && (
            <div className={`p-4 rounded-lg border ${
              difference > 0 ? 'bg-green-50 border-green-200' : 
              difference < 0 ? 'bg-red-50 border-red-200' : 
              'bg-gray-50 border-gray-200'
            }`}>
              <h5 className={`text-sm font-medium mb-2 ${
                difference > 0 ? 'text-green-700' : 
                difference < 0 ? 'text-red-700' : 
                'text-gray-700'
              }`}>
                Adjustment Summary:
              </h5>
              <div className={`text-sm ${
                difference > 0 ? 'text-green-600' : 
                difference < 0 ? 'text-red-600' : 
                'text-gray-600'
              }`}>
                <p>• Product: <strong>{selectedProduct?.name}</strong> {formData.variant && `(${formData.variant})`}</p>
                <p>• Warehouse: <strong>{warehouses.find(w => w.id === formData.warehouseId)?.name}</strong></p>
                <p>• System Stock: <strong>{formData.existingStock} units</strong></p>
                <p>• Physical Count: <strong>{formData.actualStockCounted} units</strong></p>
                <p>• Adjustment: <strong>{difference > 0 ? '+' : ''}{difference} units</strong></p>
                <p>• Reason: <strong>{formData.adjustmentReason}</strong></p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Adjustment...' : 'Create Adjustment'}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default CreateAdjustmentModal;
