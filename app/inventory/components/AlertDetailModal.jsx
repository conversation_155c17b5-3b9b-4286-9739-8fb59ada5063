'use client';

import React from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AlertDetailModal = ({ isOpen, onClose, alert }) => {
  if (!alert) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getAlertLevelColor = (level) => {
    switch (level) {
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'Low':
        return 'bg-yellow-100 text-yellow-800';
      case 'Out of Stock':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getAlertIcon = (level) => {
    switch (level) {
      case 'Critical':
        return 'icon-warning-circle';
      case 'Low':
        return 'icon-warning';
      case 'Out of Stock':
        return 'icon-x-circle';
      default:
        return 'icon-info';
    }
  };

  const stockPercentage = alert.reorderLevel > 0 ? (alert.currentStock / alert.reorderLevel) * 100 : 0;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Low Stock Alert Details"
    >
      <div className="space-y-6 p-6">
        {/* Header Info */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={alert.thumbnail}
              alt={alert.productName}
              width={64}
              height={64}
              className="object-cover w-16 h-16 rounded-lg"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900">{alert.productName}</h3>
            <p className="text-sm text-gray-500 mt-1">SKU: {alert.sku}</p>
            {alert.variant && (
              <p className="text-sm text-gray-400 mt-1">Variant: {alert.variant}</p>
            )}
            <div className="flex items-center gap-2 mt-2">
              <span className={`inline-flex items-center gap-1 px-3 py-1 text-xs font-semibold rounded-full ${getAlertLevelColor(alert.alertLevel)}`}>
                <span className={`icon ${getAlertIcon(alert.alertLevel)} text-xs`} />
                {alert.alertLevel}
              </span>
              <span className="text-xs text-gray-500">Category: {alert.category}</span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Days Low</div>
            <div className={`text-2xl font-bold ${
              alert.daysLow > 7 ? 'text-red-600' : 
              alert.daysLow > 3 ? 'text-yellow-600' : 
              'text-gray-600'
            }`}>
              {alert.daysLow}
            </div>
          </div>
        </div>

        {/* Stock Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-red-50 p-4 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">{alert.currentStock}</div>
            <div className="text-sm text-red-700 font-medium">Current Stock</div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600">{alert.reorderLevel}</div>
            <div className="text-sm text-yellow-700 font-medium">Reorder Level</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600">{alert.suggestedReorderQty}</div>
            <div className="text-sm text-blue-700 font-medium">Suggested Qty</div>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <div className="text-2xl font-bold text-gray-600">{Math.round(stockPercentage)}%</div>
            <div className="text-sm text-gray-700 font-medium">of Reorder Level</div>
          </div>
        </div>

        {/* Stock Level Visualization */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Stock Level Analysis
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Current Stock vs Reorder Level</span>
              <span className="font-medium">{alert.currentStock} / {alert.reorderLevel} units</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className={`h-3 rounded-full ${
                  stockPercentage <= 25 ? 'bg-red-500' :
                  stockPercentage <= 50 ? 'bg-yellow-500' :
                  'bg-green-500'
                }`}
                style={{ width: `${Math.min(stockPercentage, 100)}%` }}
              ></div>
            </div>
            
            <div className="text-xs text-gray-500">
              {alert.currentStock === 0 ? (
                <span className="text-red-600 font-medium">⚠️ Out of Stock - Immediate action required</span>
              ) : stockPercentage <= 25 ? (
                <span className="text-red-600 font-medium">🔴 Critical - Stock is critically low</span>
              ) : stockPercentage <= 50 ? (
                <span className="text-yellow-600 font-medium">🟡 Low - Stock is below reorder level</span>
              ) : (
                <span className="text-green-600 font-medium">🟢 Good - Stock is above reorder level</span>
              )}
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Warehouse Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Warehouse Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Warehouse</label>
                <p className="text-sm text-gray-900 mt-1">{alert.warehouseName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Last Replenishment</label>
                <p className="text-sm text-gray-900 mt-1">{formatDate(alert.lastReplenishmentDate)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Days Since Replenishment</label>
                <p className="text-sm text-gray-900 mt-1">
                  {Math.floor((new Date() - new Date(alert.lastReplenishmentDate)) / (1000 * 60 * 60 * 24))} days
                </p>
              </div>
            </div>
          </div>

          {/* Reorder Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Reorder Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Suggested Reorder Quantity</label>
                <p className="text-sm text-gray-900 mt-1 font-medium">{alert.suggestedReorderQty} units</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Shortage Amount</label>
                <p className="text-sm text-red-600 mt-1 font-medium">
                  {Math.max(0, alert.reorderLevel - alert.currentStock)} units below reorder level
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Recommended Action</label>
                <p className="text-sm text-gray-900 mt-1">
                  {alert.currentStock === 0 ? 'Immediate reorder required' :
                   alert.alertLevel === 'Critical' ? 'Urgent reorder recommended' :
                   'Schedule reorder soon'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Recommendations */}
        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <h5 className="text-sm font-medium text-blue-700 mb-2">Recommended Actions:</h5>
          <ul className="text-sm text-blue-600 space-y-1">
            <li>• Create purchase request for {alert.suggestedReorderQty} units</li>
            <li>• Notify vendor about low stock situation</li>
            {alert.currentStock === 0 && <li>• Consider emergency procurement from alternative suppliers</li>}
            <li>• Review reorder level settings if frequent stockouts occur</li>
          </ul>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end gap-3 p-6 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
        <button
          type="button"
          className="btn btn-info"
          onClick={() => {
            // Handle notify vendor action
            console.log('Notify vendor for:', alert);
          }}
        >
          Notify Vendor
        </button>
        <button
          type="button"
          className="btn btn-primary"
          onClick={() => {
            // Handle create purchase request action
            onClose();
          }}
        >
          Create Purchase Request
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default AlertDetailModal;
