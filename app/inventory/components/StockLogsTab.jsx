'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Image from 'next/image';
import FilterField from '../../components/table/FilterField';
import StockLogDetailModal from './StockLogDetailModal';
import AddStockLogModal from './AddStockLogModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const StockLogsTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedLog, setSelectedLog] = useState(null);
  const [isLogDetailOpen, setIsLogDetailOpen] = useState(false);
  const [isAddLogOpen, setIsAddLogOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Sample data for stock logs
  const [stockLogs, setStockLogs] = useState([
    {
      id: 'LOG-001',
      logId: 'LOG-001',
      productName: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      variant: 'Black - Standard',
      warehouseName: 'Main Distribution Center',
      stockInOut: 'In',
      quantity: 100,
      reason: 'Purchase Inward',
      referenceType: 'Purchase Order',
      referenceId: 'PO-2024-001',
      dateTime: '2024-01-20T10:30:00Z',
      processedBy: 'John Smith',
      thumbnail: '/images/product-3.jpg'
    },
    {
      id: 'LOG-002',
      logId: 'LOG-002',
      productName: 'Cotton T-Shirt',
      sku: 'CTS-002',
      variant: 'Red - Medium',
      warehouseName: 'East Coast Fulfillment',
      stockInOut: 'Out',
      quantity: 25,
      reason: 'Order Fulfillment',
      referenceType: 'Sales Order',
      referenceId: 'SO-2024-156',
      dateTime: '2024-01-19T14:20:00Z',
      processedBy: 'Sarah Johnson',
      thumbnail: '/images/product-2.jpg'
    },
    {
      id: 'LOG-003',
      logId: 'LOG-003',
      productName: 'Office Chair Bundle',
      sku: 'OCB-003',
      variant: null,
      warehouseName: 'Vendor Storage Facility',
      stockInOut: 'Out',
      quantity: 5,
      reason: 'Damage Out',
      referenceType: 'Manual',
      referenceId: 'ADJ-2024-003',
      dateTime: '2024-01-18T16:45:00Z',
      processedBy: 'Mike Wilson',
      thumbnail: '/images/product-1.jpg'
    },
    {
      id: 'LOG-004',
      logId: 'LOG-004',
      productName: 'Smartphone Case',
      sku: 'SPC-004',
      variant: 'iPhone 14 - Clear',
      warehouseName: 'Main Distribution Center',
      stockInOut: 'In',
      quantity: 200,
      reason: 'Return Processing',
      referenceType: 'Return',
      referenceId: 'RET-2024-089',
      dateTime: '2024-01-17T11:15:00Z',
      processedBy: 'Lisa Brown',
      thumbnail: '/images/product-3.jpg'
    },
    {
      id: 'LOG-005',
      logId: 'LOG-005',
      productName: 'Gaming Laptop',
      sku: 'GL-005',
      variant: null,
      warehouseName: 'East Coast Fulfillment',
      stockInOut: 'In',
      quantity: 10,
      reason: 'Stock Transfer',
      referenceType: 'Transfer',
      referenceId: 'TRF-2024-012',
      dateTime: '2024-01-16T09:30:00Z',
      processedBy: 'David Chen',
      thumbnail: '/images/product-1.jpg'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Logs', count: stockLogs.length },
    { id: 'In', label: 'Stock In', count: stockLogs.filter(l => l.stockInOut === 'In').length },
    { id: 'Out', label: 'Stock Out', count: stockLogs.filter(l => l.stockInOut === 'Out').length }
  ];

  const filteredLogs = stockLogs.filter(log => {
    const matchesSearch = log.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         log.sku.toLowerCase().includes(filterText.toLowerCase()) ||
                         log.warehouseName.toLowerCase().includes(filterText.toLowerCase()) ||
                         log.referenceId.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || log.stockInOut === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const columns = [
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-400">{row.sku}</div>
            {row.variant && (
              <div className="text-xs text-gray-300">{row.variant}</div>
            )}
          </div>
        </div>
      ),
      width: '300px',
    },
    {
      name: 'Warehouse',
      selector: row => row.warehouseName,
      sortable: true,
      width: '180px',
    },
    {
      name: 'Type',
      selector: row => row.stockInOut,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          row.stockInOut === 'In' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          Stock {row.stockInOut}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Quantity',
      selector: row => row.quantity,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium ${
          row.stockInOut === 'In' ? 'text-green-600' : 'text-red-600'
        }`}>
          {row.stockInOut === 'In' ? '+' : '-'}{row.quantity}
        </span>
      ),
      width: '100px',
    },
    {
      name: 'Reason',
      selector: row => row.reason,
      sortable: true,
      // width: '150px',
    },
    {
      name: 'Reference',
      selector: row => row.referenceId,
      sortable: true,
      cell: (row) => (
        <div>
          <div className="text-sm font-medium">{row.referenceId}</div>
          <div className="text-xs text-gray-300">{row.referenceType}</div>
        </div>
      ),
      // width: '140px',
    },
    {
      name: 'Date & Time',
      selector: row => row.dateTime,
      sortable: true,
      cell: (row) => (
        <span className="text-sm">{formatDate(row.dateTime)}</span>
      ),
      width: '140px',
    },
    {
      name: 'Processed By',
      selector: row => row.processedBy,
      sortable: true,
      // width: '120px',
    },
    {
      name: 'Actions',
      grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewLog(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // width: '80px',
    },
  ];

  const handleViewLog = (log) => {
    setSelectedLog(log);
    setIsLogDetailOpen(true);
  };

  const handleAddLog = (logData) => {
    const newLog = {
      ...logData,
      id: `LOG-${Date.now()}`,
      logId: `LOG-${Date.now()}`,
      dateTime: new Date().toISOString(),
      processedBy: 'Current User' // This would come from auth context
    };
    setStockLogs(prev => [newLog, ...prev]);
    console.log('Stock log added:', newLog);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Stock In / Stock Out Logs</h2>
          <span className="text-sm text-gray-300">
            {filteredLogs.length} of {stockLogs.length} logs
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
          <button
            onClick={() => setIsAddLogOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-normal text-lg" />
            Add Stock Log
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search"
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredLogs}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-list-bullets text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No stock logs found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={false}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <StockLogDetailModal
        isOpen={isLogDetailOpen}
        onClose={() => setIsLogDetailOpen(false)}
        log={selectedLog}
      />

      <AddStockLogModal
        isOpen={isAddLogOpen}
        onClose={() => setIsAddLogOpen(false)}
        onAddLog={handleAddLog}
      />
    </div>
  );
};

export default StockLogsTab;
