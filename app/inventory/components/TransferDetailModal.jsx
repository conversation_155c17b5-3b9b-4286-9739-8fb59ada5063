'use client';

import React from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const TransferDetailModal = ({ isOpen, onClose, transfer }) => {
  if (!transfer) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'In Transit':
        return 'bg-blue-100 text-blue-800';
      case 'Received':
        return 'bg-green-100 text-green-800';
      case 'Cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pending':
        return 'icon-clock';
      case 'In Transit':
        return 'icon-truck';
      case 'Received':
        return 'icon-check-circle';
      case 'Cancelled':
        return 'icon-x-circle';
      default:
        return 'icon-circle';
    }
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Stock Transfer Details"
    >
      <div className="space-y-6 p-6">
        {/* Header Info */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={transfer.thumbnail}
              alt={transfer.productName}
              width={64}
              height={64}
              className="object-cover w-16 h-16 rounded-lg"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900">{transfer.productName}</h3>
            <p className="text-sm text-gray-500 mt-1">SKU: {transfer.sku}</p>
            {transfer.variant && (
              <p className="text-sm text-gray-400 mt-1">Variant: {transfer.variant}</p>
            )}
            <div className="flex items-center gap-2 mt-2">
              <span className={`inline-flex items-center gap-1 px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(transfer.status)}`}>
                <span className={`icon ${getStatusIcon(transfer.status)} text-xs`} />
                {transfer.status}
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Transfer ID</div>
            <div className="text-lg font-semibold text-gray-900">{transfer.transferId}</div>
          </div>
        </div>

        {/* Transfer Route */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Transfer Route</h4>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="text-sm font-medium text-gray-900">{transfer.sourceWarehouse}</div>
              <div className="text-xs text-gray-500">Source Warehouse</div>
            </div>
            <div className="flex items-center gap-2 px-4">
              <span className="icon icon-arrow-right text-gray-400" />
              <div className="text-center">
                <div className="text-lg font-bold text-primary-500">{transfer.quantity}</div>
                <div className="text-xs text-gray-500">units</div>
              </div>
              <span className="icon icon-arrow-right text-gray-400" />
            </div>
            <div className="flex-1 text-right">
              <div className="text-sm font-medium text-gray-900">{transfer.destinationWarehouse}</div>
              <div className="text-xs text-gray-500">Destination Warehouse</div>
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Transfer Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Transfer Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Transfer Date</label>
                <p className="text-sm text-gray-900 mt-1">{formatDate(transfer.transferDate)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Quantity</label>
                <p className="text-sm text-gray-900 mt-1">{transfer.quantity} units</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <p className="text-sm text-gray-900 mt-1">{transfer.status}</p>
              </div>
              
              {transfer.receivedDate && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Received Date</label>
                  <p className="text-sm text-gray-900 mt-1">{formatDate(transfer.receivedDate)}</p>
                </div>
              )}
            </div>
          </div>

          {/* Transport Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Transport Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Transport Details</label>
                <p className="text-sm text-gray-900 mt-1">{transfer.transportDetails}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Processed By</label>
                <p className="text-sm text-gray-900 mt-1">{transfer.processedBy}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Remarks */}
        {transfer.remarks && (
          <div className="space-y-2">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Remarks
            </h4>
            <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">{transfer.remarks}</p>
          </div>
        )}

        {/* Timeline */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Transfer Timeline
          </h4>
          
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="icon icon-plus text-blue-600 text-xs" />
              </div>
              <div>
                <div className="text-sm font-medium text-gray-900">Transfer Created</div>
                <div className="text-xs text-gray-500">{formatDate(transfer.transferDate)}</div>
              </div>
            </div>
            
            {transfer.status !== 'Pending' && transfer.status !== 'Cancelled' && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                  <span className="icon icon-truck text-yellow-600 text-xs" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">In Transit</div>
                  <div className="text-xs text-gray-500">Stock is being transferred</div>
                </div>
              </div>
            )}
            
            {transfer.status === 'Received' && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="icon icon-check text-green-600 text-xs" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">Received</div>
                  <div className="text-xs text-gray-500">{formatDate(transfer.receivedDate)}</div>
                </div>
              </div>
            )}
            
            {transfer.status === 'Cancelled' && (
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <span className="icon icon-x text-red-600 text-xs" />
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-900">Cancelled</div>
                  <div className="text-xs text-gray-500">Transfer was cancelled</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end p-6 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default TransferDetailModal;
