'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Image from 'next/image';
import Swal from 'sweetalert2';
import FilterField from '../../components/table/FilterField';
import AdjustmentDetailModal from './AdjustmentDetailModal';
import CreateAdjustmentModal from './CreateAdjustmentModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const InventoryAdjustmentTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedAdjustment, setSelectedAdjustment] = useState(null);
  const [isAdjustmentDetailOpen, setIsAdjustmentDetailOpen] = useState(false);
  const [isCreateAdjustmentOpen, setIsCreateAdjustmentOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Sample data for inventory adjustments
  const [adjustments, setAdjustments] = useState([
    {
      id: 'ADJ-001',
      adjustmentId: 'ADJ-001',
      productName: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      variant: 'Black - Standard',
      warehouseName: 'Main Distribution Center',
      existingStock: 150,
      actualStockCounted: 145,
      difference: -5,
      adjustmentReason: 'Audit',
      dateOfAdjustment: '2024-01-20T10:30:00Z',
      adjustedBy: 'John Smith',
      remarks: 'Physical count during monthly audit revealed discrepancy',
      thumbnail: '/images/product-3.jpg'
    },
    {
      id: 'ADJ-002',
      adjustmentId: 'ADJ-002',
      productName: 'Cotton T-Shirt',
      sku: 'CTS-002',
      variant: 'Red - Medium',
      warehouseName: 'East Coast Fulfillment',
      existingStock: 75,
      actualStockCounted: 70,
      difference: -5,
      adjustmentReason: 'Shrinkage',
      dateOfAdjustment: '2024-01-19T14:20:00Z',
      adjustedBy: 'Sarah Johnson',
      remarks: 'Stock shrinkage identified during routine check',
      thumbnail: '/images/product-2.jpg'
    },
    {
      id: 'ADJ-003',
      adjustmentId: 'ADJ-003',
      productName: 'Office Chair Bundle',
      sku: 'OCB-003',
      variant: null,
      warehouseName: 'Vendor Storage Facility',
      existingStock: 20,
      actualStockCounted: 25,
      difference: 5,
      adjustmentReason: 'Others',
      dateOfAdjustment: '2024-01-18T16:45:00Z',
      adjustedBy: 'Mike Wilson',
      remarks: 'Found additional units in storage area B',
      thumbnail: '/images/product-1.jpg'
    },
    {
      id: 'ADJ-004',
      adjustmentId: 'ADJ-004',
      productName: 'Smartphone Case',
      sku: 'SPC-004',
      variant: 'iPhone 14 - Clear',
      warehouseName: 'Main Distribution Center',
      existingStock: 200,
      actualStockCounted: 190,
      difference: -10,
      adjustmentReason: 'Damage',
      dateOfAdjustment: '2024-01-17T11:15:00Z',
      adjustedBy: 'Lisa Brown',
      remarks: 'Damaged units removed from inventory',
      thumbnail: '/images/product-3.jpg'
    },
    {
      id: 'ADJ-005',
      adjustmentId: 'ADJ-005',
      productName: 'Gaming Laptop',
      sku: 'GL-005',
      variant: null,
      warehouseName: 'East Coast Fulfillment',
      existingStock: 12,
      actualStockCounted: 15,
      difference: 3,
      adjustmentReason: 'Others',
      dateOfAdjustment: '2024-01-16T09:30:00Z',
      adjustedBy: 'David Chen',
      remarks: 'System error correction - units were not properly recorded',
      thumbnail: '/images/product-1.jpg'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Adjustments', count: adjustments.length },
    { id: 'Positive', label: 'Positive', count: adjustments.filter(a => a.difference > 0).length },
    { id: 'Negative', label: 'Negative', count: adjustments.filter(a => a.difference < 0).length }
  ];

  const filteredAdjustments = adjustments.filter(adjustment => {
    const matchesSearch = adjustment.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         adjustment.sku.toLowerCase().includes(filterText.toLowerCase()) ||
                         adjustment.adjustmentId.toLowerCase().includes(filterText.toLowerCase()) ||
                         adjustment.warehouseName.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || 
                         (activeStatusTab === 'Positive' && adjustment.difference > 0) ||
                         (activeStatusTab === 'Negative' && adjustment.difference < 0);
    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const columns = [
    {
      name: 'Adjustment ID',
      selector: row => row.adjustmentId,
      sortable: true,
      cell: (row) => (
        <div className="py-2">
          <div className="font-medium text-sm">{row.adjustmentId}</div>
          <div className="text-xs text-gray-300">{formatDate(row.dateOfAdjustment)}</div>
        </div>
      ),
      width: '140px',
    },
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-400">{row.sku}</div>
            {row.variant && (
              <div className="text-xs text-gray-300">{row.variant}</div>
            )}
          </div>
        </div>
      ),
      width: '300px',
    },
    {
      name: 'Warehouse',
      selector: row => row.warehouseName,
      sortable: true,
      wrap: true,
      width: '180px',
    },
    {
      name: 'System Stock',
      selector: row => row.existingStock,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-gray-500">{row.existingStock}</span>
      ),
      // width: '120px',
    },
    {
      name: 'Actual Count',
      selector: row => row.actualStockCounted,
      sortable: true,
      cell: (row) => (
        <span className="text-sm font-medium">{row.actualStockCounted}</span>
      ),
      // width: '120px',
    },
    {
      name: 'Difference',
      selector: row => row.difference,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium text-sm ${
          row.difference > 0 ? 'text-green-600' : 
          row.difference < 0 ? 'text-red-600' : 
          'text-gray-500'
        }`}>
          {row.difference > 0 ? '+' : ''}{row.difference}
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Reason',
      selector: row => row.adjustmentReason,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
          row.adjustmentReason === 'Audit' ? 'bg-info-500/20 text-info-500' :
          row.adjustmentReason === 'Shrinkage' ? 'bg-red-100 text-red-800' :
          row.adjustmentReason === 'Damage' ? 'bg-red-100 text-red-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {row.adjustmentReason}
        </span>
      ),
      // width: '120px',
    },
    {
      name: 'Adjusted By',
      selector: row => row.adjustedBy,
      sortable: true,
      // width: '120px',
    },
    {
      name: 'Actions',
      // grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewAdjustment(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleDeleteAdjustment(row.id)}
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete Adjustment"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // width: '100px',
    },
  ];

  const handleViewAdjustment = (adjustment) => {
    setSelectedAdjustment(adjustment);
    setIsAdjustmentDetailOpen(true);
  };

  const handleDeleteAdjustment = async (adjustmentId) => {
    const adjustment = adjustments.find(a => a.id === adjustmentId);
    
    const result = await Swal.fire({
      title: 'Delete Adjustment',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-gray-600">Are you sure you want to delete this inventory adjustment? This action cannot be undone.</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="mb-2">
              <p class="text-sm font-medium text-gray-900">${adjustment?.adjustmentId || 'Unknown Adjustment'}</p>
              <p class="text-xs text-gray-500">${adjustment?.productName || 'N/A'} (${adjustment?.sku || 'N/A'})</p>
            </div>
            <div class="text-xs text-gray-500">
              <p>Warehouse: ${adjustment?.warehouseName || 'N/A'}</p>
              <p>Difference: ${adjustment?.difference > 0 ? '+' : ''}${adjustment?.difference || 0} units</p>
              <p>Reason: ${adjustment?.adjustmentReason || 'N/A'}</p>
            </div>
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc2626',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Delete Adjustment',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      setAdjustments(prev => prev.filter(a => a.id !== adjustmentId));
      
      Swal.fire({
        title: 'Deleted!',
        text: 'Inventory adjustment has been deleted successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });
    }
  };

  const handleCreateAdjustment = (adjustmentData) => {
    const newAdjustment = {
      ...adjustmentData,
      id: `ADJ-${Date.now()}`,
      adjustmentId: `ADJ-${Date.now()}`,
      dateOfAdjustment: new Date().toISOString(),
      adjustedBy: 'Current User' // This would come from auth context
    };
    setAdjustments(prev => [newAdjustment, ...prev]);
    console.log('Adjustment created:', newAdjustment);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  // Calculate summary statistics
  const totalAdjustments = adjustments.length;
  const positiveAdjustments = adjustments.filter(a => a.difference > 0).length;
  const negativeAdjustments = adjustments.filter(a => a.difference < 0).length;
  const totalVariance = adjustments.reduce((sum, a) => sum + Math.abs(a.difference), 0);

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Inventory Adjustment / Reconciliation</h2>
          <span className="text-sm text-gray-300">
            {filteredAdjustments.length} of {adjustments.length} adjustments
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-calendar-dots font-normal text-lg" />
            Schedule Count
          </button>
          <button
            onClick={() => setIsCreateAdjustmentOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-normal text-lg" />
            New Adjustment
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-info-500/10 p-3 rounded-xl ">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-info-500/20 rounded-lg flex items-center justify-center">
              <span className="icon icon-list-bullets text-info-500 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-info-500">{totalAdjustments}</div>
              <div className="text-sm text-info-600">Total Adjustments</div>
            </div>
          </div>
        </div>
        
        <div className="bg-green-50 p-3 rounded-xl ">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-trend-up text-green-600 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">{positiveAdjustments}</div>
              <div className="text-sm text-green-700">Positive</div>
            </div>
          </div>
        </div>
        
        <div className="bg-red-50 p-3 rounded-xl ">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-trend-down text-red-600 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">{negativeAdjustments}</div>
              <div className="text-sm text-red-700">Negative</div>
            </div>
          </div>
        </div>
        
        <div className="bg-orange-50 p-3 rounded-xl ">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-squares-four text-orange-600 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">{totalVariance}</div>
              <div className="text-sm text-orange-700">Total Variance</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search"
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredAdjustments}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-wrench text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No adjustments found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={false}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <AdjustmentDetailModal
        isOpen={isAdjustmentDetailOpen}
        onClose={() => setIsAdjustmentDetailOpen(false)}
        adjustment={selectedAdjustment}
      />

      <CreateAdjustmentModal
        isOpen={isCreateAdjustmentOpen}
        onClose={() => setIsCreateAdjustmentOpen(false)}
        onCreateAdjustment={handleCreateAdjustment}
      />
    </div>
  );
};

export default InventoryAdjustmentTab;
