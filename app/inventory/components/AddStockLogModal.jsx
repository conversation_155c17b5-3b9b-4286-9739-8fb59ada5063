'use client';

import React, { useState, useEffect } from 'react';
// import BaseOffCanvas from '../../components/modals/BaseOffCanvas';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import InputField from '../../components/Inputs/InputField';
import SelectField from '../../components/Inputs/SelectField';

const AddStockLogModal = ({ isOpen, onClose, onAddLog }) => {
  const [formData, setFormData] = useState({
    productSku: '',
    productName: '',
    variant: '',
    warehouseId: '',
    stockInOut: 'In',
    quantity: '',
    reason: '',
    referenceType: 'Manual',
    referenceId: '',
    remarks: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample data - in real app this would come from API
  const products = [
    { sku: 'WBH-001', name: 'Wireless Bluetooth Headphones', variants: ['Black - Standard', 'White - Standard'] },
    { sku: 'CTS-002', name: 'Cotton T-Shirt', variants: ['Red - Small', 'Red - Medium', 'Red - Large', 'Blue - Small', 'Blue - Medium'] },
    { sku: 'OCB-003', name: 'Office Chair Bundle', variants: [] },
    { sku: 'SPC-004', name: 'Smartphone Case', variants: ['iPhone 14 - Clear', 'iPhone 14 - Black', 'iPhone 15 - Clear'] },
    { sku: 'GL-005', name: 'Gaming Laptop', variants: [] }
  ];

  const warehouses = [
    { id: 'WH-001', name: 'Main Distribution Center' },
    { id: 'WH-002', name: 'East Coast Fulfillment' },
    { id: 'WH-003', name: 'Vendor Storage Facility' },
    { id: 'WH-004', name: 'Seasonal Storage Hub' }
  ];

  const reasonOptions = {
    In: ['Purchase Inward', 'Return Processing', 'Stock Transfer', 'Found Stock', 'Manual Adjustment', 'Other'],
    Out: ['Order Fulfillment', 'Damage Out', 'Stock Transfer', 'Shrinkage', 'Manual Adjustment', 'Other']
  };

  const referenceTypes = ['Manual', 'Purchase Order', 'Sales Order', 'Return', 'Transfer', 'Adjustment'];

  // Convert data to SelectField format
  const productOptions = products.map(product => ({
    value: product.sku,
    label: `${product.sku} - ${product.name}`
  }));

  const warehouseOptions = warehouses.map(warehouse => ({
    value: warehouse.id,
    label: warehouse.name
  }));

  const referenceTypeOptions = referenceTypes.map(type => ({
    value: type,
    label: type
  }));

  useEffect(() => {
    if (isOpen) {
      setFormData({
        productSku: '',
        productName: '',
        variant: '',
        warehouseId: '',
        stockInOut: 'In',
        quantity: '',
        reason: '',
        referenceType: 'Manual',
        referenceId: '',
        remarks: ''
      });
      setErrors({});
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle product selection
    if (name === 'productSku') {
      const selectedProduct = products.find(p => p.sku === value);
      setFormData(prev => ({
        ...prev,
        productName: selectedProduct ? selectedProduct.name : '',
        variant: ''
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productSku) {
      newErrors.productSku = 'Please select a product';
    }

    if (!formData.warehouseId) {
      newErrors.warehouseId = 'Please select a warehouse';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Please enter a valid quantity';
    }

    if (!formData.reason) {
      newErrors.reason = 'Please select a reason';
    }

    if (formData.referenceType !== 'Manual' && !formData.referenceId.trim()) {
      newErrors.referenceId = 'Reference ID is required for this reference type';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedProduct = products.find(p => p.sku === formData.productSku);
      const selectedWarehouse = warehouses.find(w => w.id === formData.warehouseId);

      const logData = {
        productName: selectedProduct.name,
        sku: formData.productSku,
        variant: formData.variant || null,
        warehouseName: selectedWarehouse.name,
        stockInOut: formData.stockInOut,
        quantity: parseInt(formData.quantity),
        reason: formData.reason,
        referenceType: formData.referenceType,
        referenceId: formData.referenceId || `MAN-${Date.now()}`,
        remarks: formData.remarks,
        thumbnail: '/images/product-1.jpg' // Default thumbnail
      };

      onAddLog(logData);
      onClose();
    } catch (error) {
      console.error('Error adding stock log:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProduct = products.find(p => p.sku === formData.productSku);
  const availableReasons = reasonOptions[formData.stockInOut] || [];
  const reasonSelectOptions = availableReasons.map(reason => ({
    value: reason,
    label: reason
  }));

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Add Stock Log"
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          {/* Product Selection */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            <div className="flex flex-col">
              <span className="form-label">
                Product <span className="text-red-500">*</span>
              </span>
              <SelectField
                name="productSku"
                placeholder="Select a product"
                className="single-select"
                options={productOptions}
                value={formData.productSku}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'productSku',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
              />
              {errors.productSku && (
                <p className="text-red-500 text-xs mt-1">{errors.productSku}</p>
              )}
            </div>

            <div className="flex flex-col">
              <span className="form-label">
                Variant
              </span>
              <SelectField
                name="variant"
                placeholder="Select variant (if applicable)"
                className="single-select"
                options={selectedProduct?.variants.map(variant => ({
                  value: variant,
                  label: variant
                })) || []}
                value={formData.variant}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'variant',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
                isDisabled={!selectedProduct || selectedProduct.variants.length === 0}
              />
            </div>
          </div>

          {/* Warehouse and Transaction Type */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            <div className="flex flex-col">
              <span className="form-label">
                Warehouse <span className="text-red-500">*</span>
              </span>
              <SelectField
                name="warehouseId"
                placeholder="Select warehouse"
                className="single-select"
                options={warehouseOptions}
                value={formData.warehouseId}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'warehouseId',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
              />
              {errors.warehouseId && (
                <p className="text-red-500 text-xs mt-1">{errors.warehouseId}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Type <span className="text-red-500">*</span>
              </label>
              <div className="flex flex-col gap-1">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="stockInOut"
                    value="In"
                    checked={formData.stockInOut === 'In'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <span className="text-green-600 font-medium">Stock In</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="stockInOut"
                    value="Out"
                    checked={formData.stockInOut === 'Out'}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <span className="text-red-600 font-medium">Stock Out</span>
                </label>
              </div>
            </div>
          </div>

          {/* Quantity and Reason */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            <div>
              <InputField
                id="quantity"
                name="quantity"
                type="number"
                label="Quantity"
                required={true}
                placeholder="Enter quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                marginBottom="mb-0"
              />
              {errors.quantity && (
                <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
              )}
            </div>

            <div className="flex flex-col">
              <span className="form-label">
                Reason <span className="text-red-500">*</span>
              </span>
              <SelectField
                name="reason"
                placeholder="Select reason"
                className="single-select"
                options={reasonSelectOptions}
                value={formData.reason}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'reason',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
              />
              {errors.reason && (
                <p className="text-red-500 text-xs mt-1">{errors.reason}</p>
              )}
            </div>
          </div>

          {/* Reference Information */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            <div className="flex flex-col">
              <span className="form-label">
                Reference Type
              </span>
              <SelectField
                name="referenceType"
                placeholder="Select reference type"
                className="single-select"
                options={referenceTypeOptions}
                value={formData.referenceType}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'referenceType',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
              />
            </div>

            <div>
              <InputField
                id="referenceId"
                name="referenceId"
                type="text"
                label={`Reference ID ${formData.referenceType !== 'Manual' ? '*' : ''}`}
                required={formData.referenceType !== 'Manual'}
                placeholder={formData.referenceType === 'Manual' ? 'Auto-generated' : 'Enter reference ID'}
                value={formData.referenceId}
                onChange={handleInputChange}
                disabled={formData.referenceType === 'Manual'}
                marginBottom="mb-0"
              />
              {errors.referenceId && (
                <p className="text-red-500 text-xs mt-1">{errors.referenceId}</p>
              )}
            </div>
          </div>

          {/* Remarks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              rows={3}
              className="form-control"
              placeholder="Additional notes or comments..."
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 border-t border-border-color p-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Adding Log...' : 'Add Stock Log'}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default AddStockLogModal;
