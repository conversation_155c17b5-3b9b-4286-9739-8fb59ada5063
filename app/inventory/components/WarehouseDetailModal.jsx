'use client';

import React from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const WarehouseDetailModal = ({ isOpen, onClose, warehouse }) => {
  if (!warehouse) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTypeColor = (type) => {
    switch (type) {
      case 'Owned':
        return 'bg-green-100 text-green-800';
      case '3PL':
        return 'bg-blue-100 text-blue-800';
      case 'Vendor-managed':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status) => {
    return status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Warehouse Details"
      size="sm"
    >
      <div className="space-y-6 p-6">
        {/* Header Info */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">{warehouse.warehouseName}</h3>
            <p className="text-sm text-gray-500 mt-1">{warehouse.warehouseCode}</p>
          </div>
          <div className="flex gap-2">
            <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full ${getTypeColor(warehouse.warehouseType)}`}>
              {warehouse.warehouseType}
            </span>
            <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStatusColor(warehouse.status)}`}>
              {warehouse.status}
            </span>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Location Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Location Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Location</label>
                <p className="text-sm text-gray-900 mt-1">{warehouse.location}</p>
              </div>
              
              {warehouse.addressLine && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Address</label>
                  <p className="text-sm text-gray-900 mt-1">{warehouse.addressLine}</p>
                </div>
              )}
              
              {warehouse.capacity && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Capacity</label>
                  <p className="text-sm text-gray-900 mt-1">{warehouse.capacity.toLocaleString()} sq.ft</p>
                </div>
              )}
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Contact Information
            </h4>
            
            <div className="space-y-3">
              {warehouse.contactPerson && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Contact Person</label>
                  <p className="text-sm text-gray-900 mt-1">{warehouse.contactPerson}</p>
                </div>
              )}
              
              {warehouse.contactNumber && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Phone Number</label>
                  <p className="text-sm text-gray-900 mt-1">
                    <a href={`tel:${warehouse.contactNumber}`} className="text-primary-500 hover:text-primary-600">
                      {warehouse.contactNumber}
                    </a>
                  </p>
                </div>
              )}
              
              {warehouse.email && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <p className="text-sm text-gray-900 mt-1">
                    <a href={`mailto:${warehouse.email}`} className="text-primary-500 hover:text-primary-600">
                      {warehouse.email}
                    </a>
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">System Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Created At</label>
              <p className="text-sm text-gray-900 mt-1">{formatDate(warehouse.createdAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-sm text-gray-900 mt-1">{formatDate(warehouse.updatedAt)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end p-6 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default WarehouseDetailModal;
