'use client';

import React from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const InventoryDetailModal = ({ isOpen, onClose, inventory }) => {
  if (!inventory) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStockStatusColor = (status) => {
    switch (status) {
      case 'In Stock':
        return 'bg-green-100 text-green-800';
      case 'Low Stock':
        return 'bg-yellow-100 text-yellow-800';
      case 'Out of Stock':
        return 'bg-red-100 text-red-800';
      case 'Critical':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const totalStock = inventory.availableStock + inventory.reservedStock + inventory.inTransit;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Inventory Details"
    >
      <div className="space-y-6 p-6">
        {/* Header Info */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={inventory.thumbnail}
              alt={inventory.productName}
              width={64}
              height={64}
              className="object-cover w-16 h-16 rounded-lg"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900">{inventory.productName}</h3>
            <p className="text-sm text-gray-500 mt-1">SKU: {inventory.sku}</p>
            {inventory.variant && (
              <p className="text-sm text-gray-400 mt-1">Variant: {inventory.variant}</p>
            )}
            <div className="flex items-center gap-2 mt-2">
              <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getStockStatusColor(inventory.stockStatus)}`}>
                {inventory.stockStatus}
              </span>
              <span className="text-xs text-gray-500">Category: {inventory.category}</span>
            </div>
          </div>
        </div>

        {/* Stock Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <div className="text-2xl font-bold text-green-600">{inventory.availableStock}</div>
            <div className="text-sm text-green-700 font-medium">Available Stock</div>
          </div>
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <div className="text-2xl font-bold text-yellow-600">{inventory.reservedStock}</div>
            <div className="text-sm text-yellow-700 font-medium">Reserved Stock</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <div className="text-2xl font-bold text-blue-600">{inventory.inTransit}</div>
            <div className="text-sm text-blue-700 font-medium">In Transit</div>
          </div>
          <div className="bg-red-50 p-4 rounded-lg border border-red-200">
            <div className="text-2xl font-bold text-red-600">{inventory.damagedStock}</div>
            <div className="text-sm text-red-700 font-medium">Damaged/Blocked</div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Warehouse Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Warehouse Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Warehouse</label>
                <p className="text-sm text-gray-900 mt-1">{inventory.warehouseName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Warehouse ID</label>
                <p className="text-sm text-gray-900 mt-1">{inventory.warehouseId}</p>
              </div>
            </div>
          </div>

          {/* Stock Levels */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Stock Levels
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Total Stock</label>
                <p className="text-sm text-gray-900 mt-1">{totalStock} units</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Reorder Level</label>
                <p className="text-sm text-gray-900 mt-1">{inventory.reorderLevel} units</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Stock Health</label>
                <div className="mt-1">
                  {inventory.availableStock > inventory.reorderLevel * 2 ? (
                    <span className="text-sm text-green-600 font-medium">Healthy Stock Level</span>
                  ) : inventory.availableStock > inventory.reorderLevel ? (
                    <span className="text-sm text-yellow-600 font-medium">Approaching Reorder Level</span>
                  ) : inventory.availableStock > 0 ? (
                    <span className="text-sm text-red-600 font-medium">Below Reorder Level</span>
                  ) : (
                    <span className="text-sm text-red-600 font-medium">Out of Stock</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stock Breakdown Chart */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Stock Distribution
          </h4>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Available Stock</span>
              <span className="font-medium">{inventory.availableStock} units ({totalStock > 0 ? Math.round((inventory.availableStock / totalStock) * 100) : 0}%)</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full" 
                style={{ width: `${totalStock > 0 ? (inventory.availableStock / totalStock) * 100 : 0}%` }}
              ></div>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Reserved Stock</span>
              <span className="font-medium">{inventory.reservedStock} units ({totalStock > 0 ? Math.round((inventory.reservedStock / totalStock) * 100) : 0}%)</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-yellow-500 h-2 rounded-full" 
                style={{ width: `${totalStock > 0 ? (inventory.reservedStock / totalStock) * 100 : 0}%` }}
              ></div>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">In Transit</span>
              <span className="font-medium">{inventory.inTransit} units ({totalStock > 0 ? Math.round((inventory.inTransit / totalStock) * 100) : 0}%)</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full" 
                style={{ width: `${totalStock > 0 ? (inventory.inTransit / totalStock) * 100 : 0}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">System Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-sm text-gray-900 mt-1">{formatDate(inventory.lastUpdated)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Inventory ID</label>
              <p className="text-sm text-gray-900 mt-1">{inventory.id}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end gap-3 p-6 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
        <button
          type="button"
          className="btn btn-primary"
          onClick={() => {
            // Handle adjust stock action
            onClose();
          }}
        >
          Adjust Stock
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default InventoryDetailModal;
