'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AdjustStockModal = ({ isOpen, onClose, inventory }) => {
  const [formData, setFormData] = useState({
    adjustmentType: 'increase',
    quantity: '',
    reason: 'Manual Adjustment',
    remarks: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen && inventory) {
      setFormData({
        adjustmentType: 'increase',
        quantity: '',
        reason: 'Manual Adjustment',
        remarks: ''
      });
      setErrors({});
    }
  }, [isOpen, inventory]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Please enter a valid quantity';
    }

    if (!formData.reason.trim()) {
      newErrors.reason = 'Please select or enter a reason';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const adjustmentData = {
        inventoryId: inventory.id,
        productName: inventory.productName,
        sku: inventory.sku,
        warehouseName: inventory.warehouseName,
        adjustmentType: formData.adjustmentType,
        quantity: parseInt(formData.quantity),
        reason: formData.reason,
        remarks: formData.remarks,
        currentStock: inventory.availableStock,
        newStock: formData.adjustmentType === 'increase' 
          ? inventory.availableStock + parseInt(formData.quantity)
          : inventory.availableStock - parseInt(formData.quantity),
        adjustedBy: 'Current User', // This would come from auth context
        adjustedAt: new Date().toISOString()
      };

      console.log('Stock adjustment:', adjustmentData);
      
      // Here you would typically call an API to save the adjustment
      // await adjustStock(adjustmentData);
      
      onClose();
    } catch (error) {
      console.error('Error adjusting stock:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const reasonOptions = [
    'Manual Adjustment',
    'Audit Correction',
    'Damage/Shrinkage',
    'Found Stock',
    'Return Processing',
    'System Correction',
    'Other'
  ];

  if (!inventory) return null;

  const newStock = formData.quantity ? (
    formData.adjustmentType === 'increase' 
      ? inventory.availableStock + parseInt(formData.quantity)
      : inventory.availableStock - parseInt(formData.quantity)
  ) : inventory.availableStock;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Adjust Stock"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Product Info */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Image
                src={inventory.thumbnail}
                alt={inventory.productName}
                width={48}
                height={48}
                className="object-cover w-12 h-12 rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{inventory.productName}</h4>
              <p className="text-sm text-gray-500">SKU: {inventory.sku}</p>
              {inventory.variant && (
                <p className="text-sm text-gray-400">{inventory.variant}</p>
              )}
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Current Stock</div>
              <div className="text-lg font-semibold text-gray-900">{inventory.availableStock}</div>
            </div>
          </div>

          {/* Warehouse Info */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Warehouse
            </label>
            <div className="p-3 bg-gray-50 rounded-lg">
              <span className="text-sm text-gray-900">{inventory.warehouseName}</span>
            </div>
          </div>

          {/* Adjustment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Adjustment Type <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 gap-3">
              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="adjustmentType"
                  value="increase"
                  checked={formData.adjustmentType === 'increase'}
                  onChange={handleInputChange}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium text-green-600">Increase Stock</div>
                  <div className="text-xs text-gray-500">Add inventory</div>
                </div>
              </label>
              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                <input
                  type="radio"
                  name="adjustmentType"
                  value="decrease"
                  checked={formData.adjustmentType === 'decrease'}
                  onChange={handleInputChange}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium text-red-600">Decrease Stock</div>
                  <div className="text-xs text-gray-500">Remove inventory</div>
                </div>
              </label>
            </div>
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              name="quantity"
              value={formData.quantity}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.quantity ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter quantity to adjust"
              min="1"
            />
            {errors.quantity && (
              <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
            )}
          </div>

          {/* Stock Preview */}
          {formData.quantity && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-blue-700">Stock Preview:</span>
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-gray-600">{inventory.availableStock}</span>
                  <span className={`font-medium ${formData.adjustmentType === 'increase' ? 'text-green-600' : 'text-red-600'}`}>
                    {formData.adjustmentType === 'increase' ? '+' : '-'}{formData.quantity}
                  </span>
                  <span className="text-gray-400">→</span>
                  <span className={`font-semibold ${newStock < 0 ? 'text-red-600' : 'text-blue-600'}`}>
                    {newStock}
                  </span>
                </div>
              </div>
              {newStock < 0 && (
                <p className="text-red-600 text-xs mt-2">Warning: This adjustment will result in negative stock!</p>
              )}
            </div>
          )}

          {/* Reason */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason <span className="text-red-500">*</span>
            </label>
            <select
              name="reason"
              value={formData.reason}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.reason ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              {reasonOptions.map(reason => (
                <option key={reason} value={reason}>
                  {reason}
                </option>
              ))}
            </select>
            {errors.reason && (
              <p className="text-red-500 text-xs mt-1">{errors.reason}</p>
            )}
          </div>

          {/* Remarks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Additional notes or comments..."
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`btn ${formData.adjustmentType === 'increase' ? 'btn-success' : 'btn-danger'}`}
            disabled={isSubmitting || (formData.quantity && newStock < 0)}
          >
            {isSubmitting ? 'Processing...' : `${formData.adjustmentType === 'increase' ? 'Increase' : 'Decrease'} Stock`}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default AdjustStockModal;
