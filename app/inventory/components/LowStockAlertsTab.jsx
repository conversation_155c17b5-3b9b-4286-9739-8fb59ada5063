'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Image from 'next/image';
import FilterField from '../../components/table/FilterField';
import AlertDetailModal from './AlertDetailModal';
import CreatePurchaseRequestModal from './CreatePurchaseRequestModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const LowStockAlertsTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [isAlertDetailOpen, setIsAlertDetailOpen] = useState(false);
  const [isPurchaseRequestOpen, setIsPurchaseRequestOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);

  // Sample data for low stock alerts
  const [alerts, setAlerts] = useState([
    {
      id: 'ALERT-001',
      productName: 'Cotton T-Shirt',
      sku: 'CTS-002',
      variant: 'Red - Medium',
      warehouseName: 'East Coast Fulfillment',
      currentStock: 15,
      reorderLevel: 50,
      suggestedReorderQty: 200,
      lastReplenishmentDate: '2024-01-10T09:15:00Z',
      category: 'Clothing',
      alertLevel: 'Out of Stock',
      daysLow: 5,
      thumbnail: '/images/product-2.jpg'
    },
    {
      id: 'ALERT-002',
      productName: 'Gaming Laptop',
      sku: 'GL-005',
      variant: null,
      warehouseName: 'Main Distribution Center',
      currentStock: 8,
      reorderLevel: 15,
      suggestedReorderQty: 25,
      lastReplenishmentDate: '2024-01-05T16:45:00Z',
      category: 'Computers',
      alertLevel: 'Out of Stock',
      daysLow: 8,
      thumbnail: '/images/product-1.jpg'
    },
    {
      id: 'ALERT-003',
      productName: 'Smartphone Case',
      sku: 'SPC-004',
      variant: 'iPhone 14 - Clear',
      warehouseName: 'Vendor Storage Facility',
      currentStock: 45,
      reorderLevel: 100,
      suggestedReorderQty: 150,
      lastReplenishmentDate: '2024-01-15T11:30:00Z',
      category: 'Accessories',
      alertLevel: 'Low',
      daysLow: 2,
      thumbnail: '/images/product-3.jpg'
    },
    {
      id: 'ALERT-004',
      productName: 'Office Chair Bundle',
      sku: 'OCB-003',
      variant: null,
      warehouseName: 'Seasonal Storage Hub',
      currentStock: 0,
      reorderLevel: 10,
      suggestedReorderQty: 30,
      lastReplenishmentDate: '2023-12-20T14:20:00Z',
      category: 'Furniture',
      alertLevel: 'Out of Stock',
      daysLow: 12,
      thumbnail: '/images/product-1.jpg'
    },
    {
      id: 'ALERT-005',
      productName: 'Wireless Bluetooth Headphones',
      sku: 'WBH-001',
      variant: 'Black - Standard',
      warehouseName: 'East Coast Fulfillment',
      currentStock: 35,
      reorderLevel: 50,
      suggestedReorderQty: 100,
      lastReplenishmentDate: '2024-01-12T10:30:00Z',
      category: 'Electronics',
      alertLevel: 'Low',
      daysLow: 3,
      thumbnail: '/images/product-3.jpg'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Alerts', count: alerts.length },
    { id: 'Critical', label: 'Critical', count: alerts.filter(a => a.alertLevel === 'Critical').length },
    { id: 'Low', label: 'Low Stock', count: alerts.filter(a => a.alertLevel === 'Low').length },
    { id: 'Out of Stock', label: 'Out of Stock', count: alerts.filter(a => a.alertLevel === 'Out of Stock').length }
  ];

  const filteredAlerts = alerts.filter(alert => {
    const matchesSearch = alert.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         alert.sku.toLowerCase().includes(filterText.toLowerCase()) ||
                         alert.warehouseName.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || alert.alertLevel === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getAlertLevelColor = (level) => {
    switch (level) {
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'Low':
        return 'bg-yellow-100 text-yellow-800';
      case 'Out of Stock':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getAlertIcon = (level) => {
    switch (level) {
      case 'Critical':
        return 'icon-warning-circle';
      case 'Low':
        return 'icon-warning';
      case 'Out of Stock':
        return 'icon-x-circle';
      default:
        return 'icon-info';
    }
  };

  const columns = [
    {
      name: 'Product',
      selector: row => row.productName,
      sortable: true,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName}</div>
            <div className="text-xs text-gray-400">{row.sku}</div>
            {row.variant && (
              <div className="text-xs text-gray-300">{row.variant}</div>
            )}
          </div>
        </div>
      ),
      width: '280px',
    },
    {
      name: 'Warehouse',
      selector: row => row.warehouseName,
      sortable: true,
      width: '180px',
    },
    {
      name: 'Current Stock',
      selector: row => row.currentStock,
      sortable: true,
      cell: (row) => (
        <span className={`font-medium ${
          row.currentStock === 0 ? 'text-red-600' : 
          row.currentStock <= row.reorderLevel / 2 ? 'text-red-600' : 
          'text-yellow-600'
        }`}>
          {row.currentStock}
        </span>
      ),
      width: '120px',
    },
    {
      name: 'Reorder Level',
      selector: row => row.reorderLevel,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-gray-500">{row.reorderLevel}</span>
      ),
      width: '120px',
    },
    {
      name: 'Suggested Qty',
      selector: row => row.suggestedReorderQty,
      sortable: true,
      cell: (row) => (
        <span className="text-sm font-medium text-blue-600">{row.suggestedReorderQty}</span>
      ),
      width: '120px',
    },
    {
      name: 'Alert Level',
      selector: row => row.alertLevel,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex items-center gap-1 px-2 py-1 text-xs font-semibold rounded-full ${getAlertLevelColor(row.alertLevel)}`}>
          <span className={`icon ${getAlertIcon(row.alertLevel)} text-xs`} />
          {row.alertLevel}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Days Low',
      selector: row => row.daysLow,
      sortable: true,
      cell: (row) => (
        <span className={`text-sm font-medium ${
          row.daysLow > 7 ? 'text-red-600' : 
          row.daysLow > 3 ? 'text-yellow-600' : 
          'text-gray-400'
        }`}>
          {row.daysLow} days
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Last Replenishment',
      selector: row => row.lastReplenishmentDate,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-gray-400">{formatDate(row.lastReplenishmentDate)}</span>
      ),
      // width: '140px',
    },
    {
      name: 'Actions',
      // grow: 0,
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewAlert(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleCreatePurchaseRequest(row)}
            data-tooltip-id="purchase-tooltip"
            data-tooltip-content="Create Purchase Request"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-shopping-cart text-base" />
          </button>
          <button
            onClick={() => handleNotifyVendor(row)}
            data-tooltip-id="notify-tooltip"
            data-tooltip-content="Notify Vendor"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-bell text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="purchase-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="notify-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // width: '140px',
    },
  ];

  const handleViewAlert = (alert) => {
    setSelectedAlert(alert);
    setIsAlertDetailOpen(true);
  };

  const handleCreatePurchaseRequest = (alert) => {
    setSelectedAlert(alert);
    setIsPurchaseRequestOpen(true);
  };

  const handleNotifyVendor = (alert) => {
    console.log('Notify vendor for:', alert);
    // Here you would implement vendor notification logic
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4 relative">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Low Stock & Reorder Alerts</h2>
          <span className="text-sm text-gray-300">
            {filteredAlerts.length} of {alerts.length} alerts
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-bell font-normal text-lg" />
            Bulk Notify
          </button>
          <button className="btn btn-primary inline-flex items-center gap-2">
            <span className="icon icon-shopping-cart font-normal text-lg" />
            Bulk Purchase Request
          </button>
        </div>
      </div>

      {/* Alert Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-red-50 p-3 rounded-xl">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-product-lisiting text-red-600 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {alerts.filter(a => a.alertLevel === 'Out of Stock').length}
              </div>
              <div className="text-sm text-red-700">Out of Stock</div>
            </div>
          </div>
        </div>
        
        {/* <div className="bg-red-50 p-3 rounded-xl">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-warning-circle text-red-600 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-red-600">
                {alerts.filter(a => a.alertLevel === 'Critical').length}
              </div>
              <div className="text-sm text-red-700">Critical</div>
            </div>
          </div>
        </div> */}
        
        <div className="bg-yellow-50 p-3 rounded-xl">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-warning text-yellow-600 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {alerts.filter(a => a.alertLevel === 'Low').length}
              </div>
              <div className="text-sm text-yellow-700">Low Stock</div>
            </div>
          </div>
        </div>
        
        <div className="bg-blue-50 p-3 rounded-xl">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-info-500/20 rounded-lg flex items-center justify-center">
              <span className="icon icon-clock text-info-500 text-xl" />
            </div>
            <div>
              <div className="text-2xl font-bold text-info-500">
                {Math.round(alerts.reduce((sum, a) => sum + a.daysLow, 0) / alerts.length)}
              </div>
              <div className="text-sm text-info-600">Avg Days Low</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search"
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredAlerts}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-warning text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No low stock alerts found</p>
              <p className="text-gray-400 text-sm">All products are well stocked</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={false}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <AlertDetailModal
        isOpen={isAlertDetailOpen}
        onClose={() => setIsAlertDetailOpen(false)}
        alert={selectedAlert}
      />

      <CreatePurchaseRequestModal
        isOpen={isPurchaseRequestOpen}
        onClose={() => setIsPurchaseRequestOpen(false)}
        alert={selectedAlert}
      />
    </div>
  );
};

export default LowStockAlertsTab;
