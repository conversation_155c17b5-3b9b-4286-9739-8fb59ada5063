'use client';

import React from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AdjustmentDetailModal = ({ isOpen, onClose, adjustment }) => {
  if (!adjustment) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getReasonColor = (reason) => {
    switch (reason) {
      case 'Audit':
        return 'bg-blue-100 text-blue-800';
      case 'Shrinkage':
        return 'bg-red-100 text-red-800';
      case 'Damage':
        return 'bg-red-100 text-red-800';
      case 'Others':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifferenceColor = (difference) => {
    if (difference > 0) return 'text-green-600';
    if (difference < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getDifferenceIcon = (difference) => {
    if (difference > 0) return 'icon-arrow-up';
    if (difference < 0) return 'icon-arrow-down';
    return 'icon-minus';
  };

  const variancePercentage = adjustment.existingStock > 0 
    ? Math.abs((adjustment.difference / adjustment.existingStock) * 100) 
    : 0;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Inventory Adjustment Details"
    >
      <div className="space-y-6 p-6">
        {/* Header Info */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={adjustment.thumbnail}
              alt={adjustment.productName}
              width={64}
              height={64}
              className="object-cover w-16 h-16 rounded-lg"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900">{adjustment.productName}</h3>
            <p className="text-sm text-gray-500 mt-1">SKU: {adjustment.sku}</p>
            {adjustment.variant && (
              <p className="text-sm text-gray-400 mt-1">Variant: {adjustment.variant}</p>
            )}
            <div className="flex items-center gap-2 mt-2">
              <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full ${getReasonColor(adjustment.adjustmentReason)}`}>
                {adjustment.adjustmentReason}
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Adjustment ID</div>
            <div className="text-lg font-semibold text-gray-900">{adjustment.adjustmentId}</div>
          </div>
        </div>

        {/* Stock Comparison */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3">Stock Comparison</h4>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{adjustment.existingStock}</div>
              <div className="text-xs text-blue-700">System Stock</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold flex items-center justify-center gap-1 ${getDifferenceColor(adjustment.difference)}`}>
                <span className={`icon ${getDifferenceIcon(adjustment.difference)} text-lg`} />
                {Math.abs(adjustment.difference)}
              </div>
              <div className="text-xs text-gray-500">Difference</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{adjustment.actualStockCounted}</div>
              <div className="text-xs text-green-700">Actual Count</div>
            </div>
          </div>
        </div>

        {/* Variance Analysis */}
        <div className="space-y-4">
          <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
            Variance Analysis
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">Variance Amount</span>
                <span className={`text-lg font-bold ${getDifferenceColor(adjustment.difference)}`}>
                  {adjustment.difference > 0 ? '+' : ''}{adjustment.difference} units
                </span>
              </div>
              <div className="text-xs text-gray-500">
                {adjustment.difference > 0 ? 'Stock increase' : adjustment.difference < 0 ? 'Stock decrease' : 'No change'}
              </div>
            </div>
            
            <div className="bg-white p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">Variance Percentage</span>
                <span className={`text-lg font-bold ${getDifferenceColor(adjustment.difference)}`}>
                  {variancePercentage.toFixed(2)}%
                </span>
              </div>
              <div className="text-xs text-gray-500">
                Relative to system stock
              </div>
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Adjustment Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Adjustment Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Warehouse</label>
                <p className="text-sm text-gray-900 mt-1">{adjustment.warehouseName}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Adjustment Reason</label>
                <p className="text-sm text-gray-900 mt-1">{adjustment.adjustmentReason}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Date of Adjustment</label>
                <p className="text-sm text-gray-900 mt-1">{formatDate(adjustment.dateOfAdjustment)}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Adjusted By</label>
                <p className="text-sm text-gray-900 mt-1">{adjustment.adjustedBy}</p>
              </div>
            </div>
          </div>

          {/* Stock Details */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Stock Details
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">System Stock (Before)</label>
                <p className="text-sm text-gray-900 mt-1">{adjustment.existingStock} units</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Physical Count</label>
                <p className="text-sm text-gray-900 mt-1">{adjustment.actualStockCounted} units</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Adjustment Applied</label>
                <p className={`text-sm font-medium mt-1 ${getDifferenceColor(adjustment.difference)}`}>
                  {adjustment.difference > 0 ? '+' : ''}{adjustment.difference} units
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Final Stock (After)</label>
                <p className="text-sm text-gray-900 mt-1 font-medium">{adjustment.actualStockCounted} units</p>
              </div>
            </div>
          </div>
        </div>

        {/* Remarks */}
        {adjustment.remarks && (
          <div className="space-y-2">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Remarks
            </h4>
            <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">{adjustment.remarks}</p>
          </div>
        )}

        {/* Impact Summary */}
        <div className={`p-4 rounded-lg border ${
          adjustment.difference > 0 ? 'bg-green-50 border-green-200' : 
          adjustment.difference < 0 ? 'bg-red-50 border-red-200' : 
          'bg-gray-50 border-gray-200'
        }`}>
          <h5 className={`text-sm font-medium mb-2 ${
            adjustment.difference > 0 ? 'text-green-700' : 
            adjustment.difference < 0 ? 'text-red-700' : 
            'text-gray-700'
          }`}>
            Adjustment Impact:
          </h5>
          <p className={`text-sm ${
            adjustment.difference > 0 ? 'text-green-600' : 
            adjustment.difference < 0 ? 'text-red-600' : 
            'text-gray-600'
          }`}>
            This adjustment {adjustment.difference > 0 ? 'increased' : adjustment.difference < 0 ? 'decreased' : 'maintained'} the inventory of{' '}
            <strong>{adjustment.productName}</strong> at <strong>{adjustment.warehouseName}</strong>{' '}
            {adjustment.difference !== 0 && `by ${Math.abs(adjustment.difference)} units`} due to <strong>{adjustment.adjustmentReason.toLowerCase()}</strong>.
            {adjustment.difference !== 0 && ` The variance represents ${variancePercentage.toFixed(2)}% of the original system stock.`}
          </p>
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end p-6 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default AdjustmentDetailModal;
