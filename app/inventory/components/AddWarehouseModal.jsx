'use client';

import React, { useState, useEffect } from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import InputField from '../../components/Inputs/InputField';
import SelectField from '../../components/Inputs/SelectField';

const AddWarehouseModal = ({ isOpen, onClose, onAddWarehouse, warehouse }) => {
  const [formData, setFormData] = useState({
    warehouseName: '',
    warehouseCode: '',
    location: '',
    addressLine: '',
    contactPerson: '',
    contactNumber: '',
    email: '',
    warehouseType: 'Owned',
    capacity: '',
    status: 'Active'
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditMode = !!warehouse;

  useEffect(() => {
    if (isOpen) {
      if (warehouse) {
        // Edit mode - populate form with warehouse data
        setFormData({
          warehouseName: warehouse.warehouseName || '',
          warehouseCode: warehouse.warehouseCode || '',
          location: warehouse.location || '',
          addressLine: warehouse.addressLine || '',
          contactPerson: warehouse.contactPerson || '',
          contactNumber: warehouse.contactNumber || '',
          email: warehouse.email || '',
          warehouseType: warehouse.warehouseType || 'Owned',
          capacity: warehouse.capacity || '',
          status: warehouse.status || 'Active'
        });
      } else {
        // Add mode - reset form
        setFormData({
          warehouseName: '',
          warehouseCode: '',
          location: '',
          addressLine: '',
          contactPerson: '',
          contactNumber: '',
          email: '',
          warehouseType: 'Owned',
          capacity: '',
          status: 'Active'
        });
      }
      setErrors({});
    }
  }, [isOpen, warehouse]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.warehouseName.trim()) {
      newErrors.warehouseName = 'Warehouse name is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.capacity && (isNaN(formData.capacity) || formData.capacity < 0)) {
      newErrors.capacity = 'Please enter a valid capacity';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const warehouseData = {
        ...formData,
        capacity: formData.capacity ? parseInt(formData.capacity) : null
      };

      if (isEditMode) {
        // Update existing warehouse
        warehouseData.id = warehouse.id;
        warehouseData.createdAt = warehouse.createdAt;
        warehouseData.updatedAt = new Date().toISOString();
      }

      onAddWarehouse(warehouseData);
      onClose();
    } catch (error) {
      console.error('Error saving warehouse:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const warehouseTypes = [
    { value: 'Owned', label: 'Owned' },
    { value: '3PL', label: '3PL' },
    { value: 'Vendor-managed', label: 'Vendor-managed' }
  ];

  const statusOptions = [
    { value: 'Active', label: 'Active' },
    { value: 'Inactive', label: 'Inactive' }
  ];

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={isEditMode ? 'Edit Warehouse' : 'Add New Warehouse'}
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            <InputField
              id="warehouseName"
              name="warehouseName"
              type="text"
              label="Warehouse Name"
              required={true}
              placeholder="Enter warehouse name"
              value={formData.warehouseName}
              onChange={handleInputChange}
              marginBottom="mb-0"
            />
            {errors.warehouseName && (
              <p className="text-red-500 text-xs mt-1">{errors.warehouseName}</p>
            )}

            <InputField
              id="warehouseCode"
              name="warehouseCode"
              type="text"
              label="Warehouse Code"
              placeholder="Enter warehouse code"
              value={formData.warehouseCode}
              onChange={handleInputChange}
              marginBottom="mb-0"
            />
          </div>

          {/* Location Information */}
          <div className="space-y-4 border-t border-border-color pt-3">
            <h4 className="text-md font-semibold text-dark-500">
              Location Information
            </h4>

            <div>
              <InputField
                id="location"
                name="location"
                type="text"
                label="Location (City, State)"
                required={true}
                placeholder="e.g., Los Angeles, CA"
                value={formData.location}
                onChange={handleInputChange}
                marginBottom="mb-0"
              />
              {errors.location && (
                <p className="text-red-500 text-xs mt-1">{errors.location}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Address
              </label>
              <textarea
                name="addressLine"
                value={formData.addressLine}
                onChange={handleInputChange}
                rows={3}
                className="form-control"
                placeholder="Enter full address"
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4 border-t border-border-color pt-3">
            <h4 className="text-md font-semibold text-dark-500">
              Contact Information
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
              <InputField
                id="contactPerson"
                name="contactPerson"
                type="text"
                label="Contact Person"
                placeholder="Enter contact person name"
                value={formData.contactPerson}
                onChange={handleInputChange}
                marginBottom="mb-0"
              />

              <InputField
                id="contactNumber"
                name="contactNumber"
                type="tel"
                label="Contact Number"
                placeholder="Enter phone number"
                value={formData.contactNumber}
                onChange={handleInputChange}
                marginBottom="mb-0"
              />
            </div>

            <div>
              <InputField
                id="email"
                name="email"
                type="email"
                label="Email"
                placeholder="Enter email address"
                value={formData.email}
                onChange={handleInputChange}
                marginBottom="mb-0"
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>
          </div>

          {/* Warehouse Details */}
          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
            <div className="flex flex-col">
              <span className="form-label">
                Warehouse Type
              </span>
              <SelectField
                name="warehouseType"
                placeholder="Select warehouse type"
                className="single-select"
                options={warehouseTypes}
                value={formData.warehouseType}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'warehouseType',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
              />
            </div>

            <div>
              <InputField
                id="capacity"
                name="capacity"
                type="number"
                label="Capacity (sq.ft)"
                placeholder="Enter capacity"
                value={formData.capacity}
                onChange={handleInputChange}
                marginBottom="mb-0"
              />
              {errors.capacity && (
                <p className="text-red-500 text-xs mt-1">{errors.capacity}</p>
              )}
            </div>

            <div className="flex flex-col">
              <span className="form-label">
                Status
              </span>
              <SelectField
                name="status"
                placeholder="Select status"
                className="single-select"
                options={statusOptions}
                value={formData.status}
                onChange={(selected) => {
                  const event = {
                    target: {
                      name: 'status',
                      value: selected?.value || ''
                    }
                  };
                  handleInputChange(event);
                }}
              />
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 border-t border-border-color p-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : (isEditMode ? 'Update Warehouse' : 'Add Warehouse')}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default AddWarehouseModal;
