'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const TransferStockModal = ({ isOpen, onClose, inventory }) => {
  const [formData, setFormData] = useState({
    destinationWarehouse: '',
    quantity: '',
    transportDetails: '',
    remarks: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample warehouses data - in real app this would come from API
  const warehouses = [
    { id: 'WH-001', name: 'Main Distribution Center' },
    { id: 'WH-002', name: 'East Coast Fulfillment' },
    { id: 'WH-003', name: 'Vendor Storage Facility' },
    { id: 'WH-004', name: 'Seasonal Storage Hub' }
  ];

  useEffect(() => {
    if (isOpen && inventory) {
      setFormData({
        destinationWarehouse: '',
        quantity: '',
        transportDetails: '',
        remarks: ''
      });
      setErrors({});
    }
  }, [isOpen, inventory]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.destinationWarehouse) {
      newErrors.destinationWarehouse = 'Please select a destination warehouse';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Please enter a valid quantity';
    }

    if (formData.quantity && parseInt(formData.quantity) > inventory.availableStock) {
      newErrors.quantity = 'Quantity cannot exceed available stock';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const transferData = {
        transferId: `TRF-${Date.now()}`,
        productName: inventory.productName,
        sku: inventory.sku,
        variant: inventory.variant,
        sourceWarehouse: {
          id: inventory.warehouseId,
          name: inventory.warehouseName
        },
        destinationWarehouse: {
          id: formData.destinationWarehouse,
          name: warehouses.find(w => w.id === formData.destinationWarehouse)?.name
        },
        quantity: parseInt(formData.quantity),
        transportDetails: formData.transportDetails,
        remarks: formData.remarks,
        status: 'Pending',
        transferDate: new Date().toISOString(),
        processedBy: 'Current User' // This would come from auth context
      };

      console.log('Stock transfer:', transferData);
      
      // Here you would typically call an API to create the transfer
      // await createStockTransfer(transferData);
      
      onClose();
    } catch (error) {
      console.error('Error creating stock transfer:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!inventory) return null;

  const availableWarehouses = warehouses.filter(w => w.id !== inventory.warehouseId);

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Transfer Stock"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Product Info */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Image
                src={inventory.thumbnail}
                alt={inventory.productName}
                width={48}
                height={48}
                className="object-cover w-12 h-12 rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{inventory.productName}</h4>
              <p className="text-sm text-gray-500">SKU: {inventory.sku}</p>
              {inventory.variant && (
                <p className="text-sm text-gray-400">{inventory.variant}</p>
              )}
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Available Stock</div>
              <div className="text-lg font-semibold text-gray-900">{inventory.availableStock}</div>
            </div>
          </div>

          {/* Source Warehouse */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              From Warehouse
            </label>
            <div className="p-3 bg-gray-50 rounded-lg border">
              <div className="flex items-center gap-2">
                <span className="icon icon-buildings text-gray-400" />
                <span className="text-sm text-gray-900">{inventory.warehouseName}</span>
              </div>
            </div>
          </div>

          {/* Destination Warehouse */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              To Warehouse <span className="text-red-500">*</span>
            </label>
            <select
              name="destinationWarehouse"
              value={formData.destinationWarehouse}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.destinationWarehouse ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">Select destination warehouse</option>
              {availableWarehouses.map(warehouse => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </option>
              ))}
            </select>
            {errors.destinationWarehouse && (
              <p className="text-red-500 text-xs mt-1">{errors.destinationWarehouse}</p>
            )}
          </div>

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity to Transfer <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              name="quantity"
              value={formData.quantity}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.quantity ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter quantity to transfer"
              min="1"
              max={inventory.availableStock}
            />
            {errors.quantity && (
              <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              Maximum available: {inventory.availableStock} units
            </p>
          </div>

          {/* Stock Impact Preview */}
          {formData.quantity && parseInt(formData.quantity) <= inventory.availableStock && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h5 className="text-sm font-medium text-blue-700 mb-2">Transfer Impact:</h5>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Current stock at {inventory.warehouseName}:</span>
                  <span className="font-medium">{inventory.availableStock} units</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">After transfer:</span>
                  <span className="font-medium text-blue-600">
                    {inventory.availableStock - parseInt(formData.quantity)} units
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Will be in transit:</span>
                  <span className="font-medium text-orange-600">{formData.quantity} units</span>
                </div>
              </div>
            </div>
          )}

          {/* Transport Details */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transport Details
            </label>
            <input
              type="text"
              name="transportDetails"
              value={formData.transportDetails}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Vehicle info, courier details, etc."
            />
          </div>

          {/* Remarks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Additional notes or special instructions..."
            />
          </div>

          {/* Transfer Summary */}
          {formData.destinationWarehouse && formData.quantity && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <h5 className="text-sm font-medium text-green-700 mb-2">Transfer Summary:</h5>
              <div className="text-sm text-green-600">
                Transfer <strong>{formData.quantity} units</strong> of{' '}
                <strong>{inventory.productName}</strong> from{' '}
                <strong>{inventory.warehouseName}</strong> to{' '}
                <strong>{warehouses.find(w => w.id === formData.destinationWarehouse)?.name}</strong>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Transfer...' : 'Create Transfer'}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default TransferStockModal;
