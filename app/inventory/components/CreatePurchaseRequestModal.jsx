'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const CreatePurchaseRequestModal = ({ isOpen, onClose, alert }) => {
  const [formData, setFormData] = useState({
    quantity: '',
    urgency: 'Normal',
    preferredVendor: '',
    requestedDeliveryDate: '',
    justification: '',
    notes: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample vendors data - in real app this would come from API
  const vendors = [
    { id: 'VND-001', name: 'Tech Sound Electronics' },
    { id: 'VND-002', name: 'Comfort Wear Textiles' },
    { id: 'VND-003', name: 'Ergo Desk Furniture' },
    { id: 'VND-004', name: 'Protect Tech Accessories' }
  ];

  const urgencyOptions = [
    { value: 'Low', label: 'Low Priority', color: 'text-gray-600' },
    { value: 'Normal', label: 'Normal Priority', color: 'text-blue-600' },
    { value: 'High', label: 'High Priority', color: 'text-yellow-600' },
    { value: 'Urgent', label: 'Urgent', color: 'text-red-600' }
  ];

  useEffect(() => {
    if (isOpen && alert) {
      // Pre-populate form with alert data
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      
      setFormData({
        quantity: alert.suggestedReorderQty.toString(),
        urgency: alert.alertLevel === 'Critical' || alert.alertLevel === 'Out of Stock' ? 'Urgent' : 'Normal',
        preferredVendor: '',
        requestedDeliveryDate: tomorrow.toISOString().split('T')[0],
        justification: `Stock level is ${alert.alertLevel.toLowerCase()} for ${alert.productName}. Current stock: ${alert.currentStock} units, Reorder level: ${alert.reorderLevel} units.`,
        notes: ''
      });
      setErrors({});
    }
  }, [isOpen, alert]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Please enter a valid quantity';
    }

    if (!formData.requestedDeliveryDate) {
      newErrors.requestedDeliveryDate = 'Please select a delivery date';
    } else {
      const selectedDate = new Date(formData.requestedDeliveryDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (selectedDate < today) {
        newErrors.requestedDeliveryDate = 'Delivery date cannot be in the past';
      }
    }

    if (!formData.justification.trim()) {
      newErrors.justification = 'Please provide justification for this request';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedVendor = vendors.find(v => v.id === formData.preferredVendor);
      
      const purchaseRequestData = {
        requestId: `PR-${Date.now()}`,
        productName: alert.productName,
        sku: alert.sku,
        variant: alert.variant,
        warehouseName: alert.warehouseName,
        currentStock: alert.currentStock,
        reorderLevel: alert.reorderLevel,
        requestedQuantity: parseInt(formData.quantity),
        urgency: formData.urgency,
        preferredVendor: selectedVendor ? selectedVendor.name : 'Not specified',
        requestedDeliveryDate: formData.requestedDeliveryDate,
        justification: formData.justification,
        notes: formData.notes,
        requestedBy: 'Current User', // This would come from auth context
        requestDate: new Date().toISOString(),
        status: 'Pending Approval'
      };

      console.log('Purchase request created:', purchaseRequestData);
      
      // Here you would typically call an API to create the purchase request
      // await createPurchaseRequest(purchaseRequestData);
      
      onClose();
    } catch (error) {
      console.error('Error creating purchase request:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!alert) return null;

  const getUrgencyColor = (urgency) => {
    const option = urgencyOptions.find(opt => opt.value === urgency);
    return option ? option.color : 'text-gray-600';
  };

  const estimatedCost = parseInt(formData.quantity || 0) * 25; // Placeholder calculation

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Create Purchase Request"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Product Info */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Image
                src={alert.thumbnail}
                alt={alert.productName}
                width={48}
                height={48}
                className="object-cover w-12 h-12 rounded-lg"
              />
            </div>
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{alert.productName}</h4>
              <p className="text-sm text-gray-500">SKU: {alert.sku}</p>
              {alert.variant && (
                <p className="text-sm text-gray-400">{alert.variant}</p>
              )}
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500">Current Stock</div>
              <div className={`text-lg font-semibold ${
                alert.currentStock === 0 ? 'text-red-600' : 
                alert.currentStock <= alert.reorderLevel / 2 ? 'text-red-600' : 
                'text-yellow-600'
              }`}>
                {alert.currentStock}
              </div>
            </div>
          </div>

          {/* Stock Status */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{alert.currentStock}</div>
              <div className="text-xs text-blue-700">Current Stock</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{alert.reorderLevel}</div>
              <div className="text-xs text-blue-700">Reorder Level</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{alert.suggestedReorderQty}</div>
              <div className="text-xs text-blue-700">Suggested Qty</div>
            </div>
          </div>

          {/* Request Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantity to Order <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                name="quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.quantity ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter quantity"
                min="1"
              />
              {errors.quantity && (
                <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Urgency Level
              </label>
              <select
                name="urgency"
                value={formData.urgency}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {urgencyOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Vendor and Delivery */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preferred Vendor
              </label>
              <select
                name="preferredVendor"
                value={formData.preferredVendor}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="">Select preferred vendor (optional)</option>
                {vendors.map(vendor => (
                  <option key={vendor.id} value={vendor.id}>
                    {vendor.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Requested Delivery Date <span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="requestedDeliveryDate"
                value={formData.requestedDeliveryDate}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.requestedDeliveryDate ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.requestedDeliveryDate && (
                <p className="text-red-500 text-xs mt-1">{errors.requestedDeliveryDate}</p>
              )}
            </div>
          </div>

          {/* Justification */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Justification <span className="text-red-500">*</span>
            </label>
            <textarea
              name="justification"
              value={formData.justification}
              onChange={handleInputChange}
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.justification ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Explain why this purchase is needed..."
            />
            {errors.justification && (
              <p className="text-red-500 text-xs mt-1">{errors.justification}</p>
            )}
          </div>

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes
            </label>
            <textarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Any additional information or special requirements..."
            />
          </div>

          {/* Request Summary */}
          {formData.quantity && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <h5 className="text-sm font-medium text-green-700 mb-2">Purchase Request Summary:</h5>
              <div className="text-sm text-green-600 space-y-1">
                <p>• Product: <strong>{alert.productName}</strong> {alert.variant && `(${alert.variant})`}</p>
                <p>• Quantity: <strong>{formData.quantity} units</strong></p>
                <p>• Urgency: <strong className={getUrgencyColor(formData.urgency)}>{formData.urgency}</strong></p>
                <p>• Delivery: <strong>{formData.requestedDeliveryDate}</strong></p>
                <p>• Estimated Cost: <strong>${estimatedCost.toLocaleString()}</strong> (approximate)</p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Request...' : 'Create Purchase Request'}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default CreatePurchaseRequestModal;
