'use client';

import React from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const StockLogDetailModal = ({ isOpen, onClose, log }) => {
  if (!log) return null;

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getTypeColor = (type) => {
    return type === 'In' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getReferenceTypeColor = (type) => {
    switch (type) {
      case 'Purchase Order':
        return 'bg-blue-100 text-blue-800';
      case 'Sales Order':
        return 'bg-purple-100 text-purple-800';
      case 'Return':
        return 'bg-orange-100 text-orange-800';
      case 'Transfer':
        return 'bg-indigo-100 text-indigo-800';
      case 'Manual':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Stock Log Details"
    >
      <div className="space-y-6 p-6">
        {/* Header Info */}
        <div className="flex items-start gap-4">
          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={log.thumbnail}
              alt={log.productName}
              width={64}
              height={64}
              className="object-cover w-16 h-16 rounded-lg"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-semibold text-gray-900">{log.productName}</h3>
            <p className="text-sm text-gray-500 mt-1">SKU: {log.sku}</p>
            {log.variant && (
              <p className="text-sm text-gray-400 mt-1">Variant: {log.variant}</p>
            )}
            <div className="flex items-center gap-2 mt-2">
              <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getTypeColor(log.stockInOut)}`}>
                Stock {log.stockInOut}
              </span>
              <span className={`inline-flex px-3 py-1 text-xs font-medium rounded-full ${getReferenceTypeColor(log.referenceType)}`}>
                {log.referenceType}
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Quantity</div>
            <div className={`text-2xl font-bold ${log.stockInOut === 'In' ? 'text-green-600' : 'text-red-600'}`}>
              {log.stockInOut === 'In' ? '+' : '-'}{log.quantity}
            </div>
          </div>
        </div>

        {/* Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Transaction Details */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Transaction Details
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Log ID</label>
                <p className="text-sm text-gray-900 mt-1">{log.logId}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Transaction Type</label>
                <p className="text-sm text-gray-900 mt-1">Stock {log.stockInOut}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Quantity</label>
                <p className={`text-sm font-medium mt-1 ${log.stockInOut === 'In' ? 'text-green-600' : 'text-red-600'}`}>
                  {log.stockInOut === 'In' ? '+' : '-'}{log.quantity} units
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Reason</label>
                <p className="text-sm text-gray-900 mt-1">{log.reason}</p>
              </div>
            </div>
          </div>

          {/* Reference Information */}
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900 border-b border-gray-200 pb-2">
              Reference Information
            </h4>
            
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Reference Type</label>
                <p className="text-sm text-gray-900 mt-1">{log.referenceType}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Reference ID</label>
                <p className="text-sm text-gray-900 mt-1 font-mono">{log.referenceId}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Warehouse</label>
                <p className="text-sm text-gray-900 mt-1">{log.warehouseName}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Process Information */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-lg font-medium text-gray-900 mb-4">Process Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Processed By</label>
              <p className="text-sm text-gray-900 mt-1">{log.processedBy}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Date & Time</label>
              <p className="text-sm text-gray-900 mt-1">{formatDate(log.dateTime)}</p>
            </div>
          </div>
        </div>

        {/* Impact Summary */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h5 className="text-sm font-medium text-gray-700 mb-2">Transaction Summary</h5>
          <p className="text-sm text-gray-600">
            This transaction {log.stockInOut === 'In' ? 'added' : 'removed'}{' '}
            <strong>{log.quantity} units</strong> of{' '}
            <strong>{log.productName}</strong>{' '}
            {log.stockInOut === 'In' ? 'to' : 'from'}{' '}
            <strong>{log.warehouseName}</strong>{' '}
            due to <strong>{log.reason}</strong>.
          </p>
          {log.referenceType !== 'Manual' && (
            <p className="text-sm text-gray-600 mt-1">
              Reference: <strong>{log.referenceType}</strong> - <strong>{log.referenceId}</strong>
            </p>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="flex justify-end p-6 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default StockLogDetailModal;
