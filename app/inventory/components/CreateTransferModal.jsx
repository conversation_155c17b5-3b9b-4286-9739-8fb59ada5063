'use client';

import React, { useState, useEffect } from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const CreateTransferModal = ({ isOpen, onClose, onCreateTransfer }) => {
  const [formData, setFormData] = useState({
    productSku: '',
    productName: '',
    variant: '',
    sourceWarehouse: '',
    destinationWarehouse: '',
    quantity: '',
    transportDetails: '',
    remarks: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Sample data - in real app this would come from API
  const products = [
    { sku: 'WBH-001', name: 'Wireless Bluetooth Headphones', variants: ['Black - Standard', 'White - Standard'] },
    { sku: 'CTS-002', name: 'Cotton T-Shirt', variants: ['Red - Small', 'Red - Medium', 'Red - Large', 'Blue - Small', 'Blue - Medium'] },
    { sku: 'OCB-003', name: 'Office Chair Bundle', variants: [] },
    { sku: 'SPC-004', name: 'Smartphone Case', variants: ['iPhone 14 - Clear', 'iPhone 14 - Black', 'iPhone 15 - Clear'] },
    { sku: 'GL-005', name: 'Gaming Laptop', variants: [] }
  ];

  const warehouses = [
    { id: 'WH-001', name: 'Main Distribution Center' },
    { id: 'WH-002', name: 'East Coast Fulfillment' },
    { id: 'WH-003', name: 'Vendor Storage Facility' },
    { id: 'WH-004', name: 'Seasonal Storage Hub' }
  ];

  useEffect(() => {
    if (isOpen) {
      setFormData({
        productSku: '',
        productName: '',
        variant: '',
        sourceWarehouse: '',
        destinationWarehouse: '',
        quantity: '',
        transportDetails: '',
        remarks: ''
      });
      setErrors({});
    }
  }, [isOpen]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Handle product selection
    if (name === 'productSku') {
      const selectedProduct = products.find(p => p.sku === value);
      setFormData(prev => ({
        ...prev,
        productName: selectedProduct ? selectedProduct.name : '',
        variant: ''
      }));
    }

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.productSku) {
      newErrors.productSku = 'Please select a product';
    }

    if (!formData.sourceWarehouse) {
      newErrors.sourceWarehouse = 'Please select source warehouse';
    }

    if (!formData.destinationWarehouse) {
      newErrors.destinationWarehouse = 'Please select destination warehouse';
    }

    if (formData.sourceWarehouse === formData.destinationWarehouse) {
      newErrors.destinationWarehouse = 'Destination must be different from source';
    }

    if (!formData.quantity || formData.quantity <= 0) {
      newErrors.quantity = 'Please enter a valid quantity';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedProduct = products.find(p => p.sku === formData.productSku);
      const sourceWarehouse = warehouses.find(w => w.id === formData.sourceWarehouse);
      const destinationWarehouse = warehouses.find(w => w.id === formData.destinationWarehouse);

      const transferData = {
        productName: selectedProduct.name,
        sku: formData.productSku,
        variant: formData.variant || null,
        sourceWarehouse: sourceWarehouse.name,
        destinationWarehouse: destinationWarehouse.name,
        quantity: parseInt(formData.quantity),
        transportDetails: formData.transportDetails,
        remarks: formData.remarks,
        thumbnail: '/images/product-1.jpg' // Default thumbnail
      };

      onCreateTransfer(transferData);
      onClose();
    } catch (error) {
      console.error('Error creating transfer:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProduct = products.find(p => p.sku === formData.productSku);
  const availableDestinations = warehouses.filter(w => w.id !== formData.sourceWarehouse);

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Create Stock Transfer"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-6 p-6">
          {/* Product Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product <span className="text-red-500">*</span>
              </label>
              <select
                name="productSku"
                value={formData.productSku}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.productSku ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select a product</option>
                {products.map(product => (
                  <option key={product.sku} value={product.sku}>
                    {product.sku} - {product.name}
                  </option>
                ))}
              </select>
              {errors.productSku && (
                <p className="text-red-500 text-xs mt-1">{errors.productSku}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Variant
              </label>
              <select
                name="variant"
                value={formData.variant}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                disabled={!selectedProduct || selectedProduct.variants.length === 0}
              >
                <option value="">Select variant (if applicable)</option>
                {selectedProduct?.variants.map(variant => (
                  <option key={variant} value={variant}>
                    {variant}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Warehouse Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                From Warehouse <span className="text-red-500">*</span>
              </label>
              <select
                name="sourceWarehouse"
                value={formData.sourceWarehouse}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.sourceWarehouse ? 'border-red-500' : 'border-gray-300'
                }`}
              >
                <option value="">Select source warehouse</option>
                {warehouses.map(warehouse => (
                  <option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name}
                  </option>
                ))}
              </select>
              {errors.sourceWarehouse && (
                <p className="text-red-500 text-xs mt-1">{errors.sourceWarehouse}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                To Warehouse <span className="text-red-500">*</span>
              </label>
              <select
                name="destinationWarehouse"
                value={formData.destinationWarehouse}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                  errors.destinationWarehouse ? 'border-red-500' : 'border-gray-300'
                }`}
                disabled={!formData.sourceWarehouse}
              >
                <option value="">Select destination warehouse</option>
                {availableDestinations.map(warehouse => (
                  <option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name}
                  </option>
                ))}
              </select>
              {errors.destinationWarehouse && (
                <p className="text-red-500 text-xs mt-1">{errors.destinationWarehouse}</p>
              )}
            </div>
          </div>

          {/* Transfer Route Preview */}
          {formData.sourceWarehouse && formData.destinationWarehouse && (
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h5 className="text-sm font-medium text-blue-700 mb-2">Transfer Route:</h5>
              <div className="flex items-center justify-between text-sm">
                <span className="text-blue-600 font-medium">
                  {warehouses.find(w => w.id === formData.sourceWarehouse)?.name}
                </span>
                <div className="flex items-center gap-2">
                  <span className="icon icon-arrow-right text-blue-400" />
                  <span className="text-blue-500 font-medium">
                    {formData.quantity || '0'} units
                  </span>
                  <span className="icon icon-arrow-right text-blue-400" />
                </div>
                <span className="text-blue-600 font-medium">
                  {warehouses.find(w => w.id === formData.destinationWarehouse)?.name}
                </span>
              </div>
            </div>
          )}

          {/* Quantity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Quantity to Transfer <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              name="quantity"
              value={formData.quantity}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${
                errors.quantity ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter quantity to transfer"
              min="1"
            />
            {errors.quantity && (
              <p className="text-red-500 text-xs mt-1">{errors.quantity}</p>
            )}
          </div>

          {/* Transport Details */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transport Details
            </label>
            <input
              type="text"
              name="transportDetails"
              value={formData.transportDetails}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Vehicle info, courier details, tracking number, etc."
            />
          </div>

          {/* Remarks */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remarks
            </label>
            <textarea
              name="remarks"
              value={formData.remarks}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Additional notes or special instructions..."
            />
          </div>

          {/* Transfer Summary */}
          {formData.productSku && formData.sourceWarehouse && formData.destinationWarehouse && formData.quantity && (
            <div className="p-4 bg-green-50 rounded-lg border border-green-200">
              <h5 className="text-sm font-medium text-green-700 mb-2">Transfer Summary:</h5>
              <div className="text-sm text-green-600">
                Transfer <strong>{formData.quantity} units</strong> of{' '}
                <strong>{selectedProduct?.name}</strong>{' '}
                {formData.variant && `(${formData.variant}) `}
                from <strong>{warehouses.find(w => w.id === formData.sourceWarehouse)?.name}</strong> to{' '}
                <strong>{warehouses.find(w => w.id === formData.destinationWarehouse)?.name}</strong>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating Transfer...' : 'Create Transfer'}
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default CreateTransferModal;
