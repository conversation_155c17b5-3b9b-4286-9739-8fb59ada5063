'use client';

import React, { useState, useRef } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import ProfileTab from '@/app/components/settings/ProfileTab';
import PasswordTab from '@/app/components/settings/PasswordTab';
import NotificationTab from '@/app/components/settings/NotificationTab';

export default function Page() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [avatarPreview, setAvatarPreview] = useState('/images/default-Image.jpg');
  const fileInputRef = useRef(null);

  const tabs = [
    { id: 'profile', label: 'Profile' },
    { id: 'password', label: 'Password' },
    { id: 'notification', label: 'Notification' },
    { id: 'integration', label: 'Integration' },
    { id: 'billing', label: 'Billing' },
  ];
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'General Settings' },
  ];

  const handleAvatarChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      if (!file.type.startsWith('image/')) {
        alert('Please upload an image file');
        return;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);

      // Here you would typically upload the file to your server
      // For now, we'll just update the preview
      // uploadAvatarToServer(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <ProfileTab
            avatarPreview={avatarPreview}
            fileInputRef={fileInputRef}
            triggerFileInput={triggerFileInput}
            handleAvatarChange={handleAvatarChange}
          />
        );
      case 'password':
        return <PasswordTab />;
      case 'notification':
        return <NotificationTab />;
      // Add other tab contents as needed
      default:
        return <div className="py-6">Content for {activeTab} tab</div>;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          {/* Header with Tabs */}
          <div className="flex items-center justify-between w-full py-6 px-4 2xl:px-8">
            <div className="flex flex-col gap-1">
              <div className="flex flex-col gap-1">
                <h1 className="text-xl font-bold">General Settings</h1>
                <Breadcrumb items={breadcrumbItems} />
              </div>
            </div>
          </div>
          <div className="w-full h-[calc(100dvh-137px)] overflow-y-auto pb-6 px-4 2xl:px-8">
            <div className="w-full rounded-xl">
              <nav className="flex gap-5 py-4 pb-0 border-b border-border-color sticky top-0 bg-surface-100 z-10">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`pb-3 px-4 text-sm font-semibold border-b-2 ${
                      tab.id === activeTab
                        ? 'border-primary-500 text-primary-500'
                        : 'border-transparent text-gray-400 hover:text-primary-500'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>

              <div className="py-4">
                {renderTabContent()}
              </div>
            </div>
          </div>

        </div>
      </div>
    </PrivateLayout>
  );
}
