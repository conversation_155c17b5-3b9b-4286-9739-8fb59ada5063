'use client';

import React, { useState, useRef } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import ProfileTab from '@/app/components/settings/ProfileTab';
import PasswordTab from '@/app/components/settings/PasswordTab';
import NotificationTab from '@/app/components/settings/NotificationTab';
import BillingTab from '@/app/components/settings/BillingTab';
import IntegrationTab from '@/app/components/settings/IntegrationTab';

export default function Page() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [avatarPreview, setAvatarPreview] = useState('/images/default-Image.jpg');
  const fileInputRef = useRef(null);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: 'icon-user', description: 'Manage your personal information and preferences' },
    { id: 'password', label: 'Security', icon: 'icon-shield-check', description: 'Password and security settings' },
    { id: 'notification', label: 'Notifications', icon: 'icon-bell', description: 'Configure notification preferences' },
    { id: 'billing', label: 'Billing', icon: 'icon-payout', description: 'Manage billing and subscription' },
    { id: 'integration', label: 'Integrations', icon: 'icon-plugs', description: 'API keys and third-party integrations' },
  ];
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'General Settings' },
  ];

  const handleAvatarChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      if (!file.type.startsWith('image/')) {
        alert('Please upload an image file');
        return;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);

      // Here you would typically upload the file to your server
      // For now, we'll just update the preview
      // uploadAvatarToServer(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <ProfileTab
            avatarPreview={avatarPreview}
            fileInputRef={fileInputRef}
            triggerFileInput={triggerFileInput}
            handleAvatarChange={handleAvatarChange}
          />
        );
      case 'password':
        return <PasswordTab />;
      case 'notification':
        return <NotificationTab />;
      case 'billing':
        return <BillingTab />;
      case 'integration':
        return <IntegrationTab />;
      default:
        return <div className="py-6">Content for {activeTab} tab</div>;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex bg-surface-100 rounded-xl w-[calc(100% - 12px)] mt-[60px]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}>
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">General Settings</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex items-center gap-2">
              <div className="text-right">
                <p className="text-sm font-medium text-dark-500">Last updated</p>
                <p className="text-xs text-gray-400">2 hours ago</p>
              </div>
              <div className="w-8 h-8 rounded-full bg-success-100 flex items-center justify-center">
                <span className="icon icon-check text-success-600 text-sm" />
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="bg-white shadow-sm rounded-xl w-[calc(100% - 12px)]">
            {/* Enhanced Tab Navigation */}
            <div className=" mb-6">
              <nav className="flex justify-between overflow-x-auto border-b border-border-color">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-3 px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200 ${
                      tab.id === activeTab
                        ? 'border-primary-500 text-primary-500 bg-primary-50'
                        : 'border-transparent text-gray-300 hover:text-dark-500 hover:border-gray-300'
                    }`}
                  >
                    <span className={`icon ${tab.icon} text-xl w-8 h-8 items-center justify-center rounded-lg hidden xl:flex
                      ${tab.id === activeTab ? 'bg-primary-500/10' : 'bg-gray-500/10'}
                    `} />
                    <div className="text-left">
                      <div className="font-semibold">{tab.label}</div>
                      <div className={`text-xs hidden xl:block
                        ${tab.id === activeTab ? 'text-gray-500' : 'text-gray-300'}
                        `}>{tab.description}</div>
                    </div>
                  </button>
                ))}
              </nav>
            </div>

            {/* Tab Content */}
            <div className="h-[calc(100dvh-275px)] overflow-y-auto px-4 pb-4">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
