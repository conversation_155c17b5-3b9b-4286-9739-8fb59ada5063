'use client';

import React, { useState, useRef } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import ProfileTab from '@/app/components/settings/ProfileTab';
import PasswordTab from '@/app/components/settings/PasswordTab';
import NotificationTab from '@/app/components/settings/NotificationTab';
import BillingTab from '@/app/components/settings/BillingTab';
import IntegrationTab from '@/app/components/settings/IntegrationTab';

export default function Page() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [avatarPreview, setAvatarPreview] = useState('/images/default-Image.jpg');
  const fileInputRef = useRef(null);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: 'icon-user', description: 'Manage your personal information and preferences' },
    { id: 'password', label: 'Security', icon: 'icon-shield-check', description: 'Password and security settings' },
    { id: 'notification', label: 'Notifications', icon: 'icon-bell', description: 'Configure notification preferences' },
    { id: 'billing', label: 'Billing', icon: 'icon-payout', description: 'Manage billing and subscription' },
    { id: 'integration', label: 'Integrations', icon: 'icon-plugs', description: 'API keys and third-party integrations' },
  ];
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'General Settings' },
  ];

  const handleAvatarChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Check file type
      if (!file.type.startsWith('image/')) {
        alert('Please upload an image file');
        return;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size should be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result);
      };
      reader.readAsDataURL(file);

      // Here you would typically upload the file to your server
      // For now, we'll just update the preview
      // uploadAvatarToServer(file);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current.click();
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return (
          <ProfileTab
            avatarPreview={avatarPreview}
            fileInputRef={fileInputRef}
            triggerFileInput={triggerFileInput}
            handleAvatarChange={handleAvatarChange}
          />
        );
      case 'password':
        return <PasswordTab />;
      case 'notification':
        return <NotificationTab />;
      case 'billing':
        return <BillingTab />;
      case 'integration':
        return <IntegrationTab />;
      default:
        return <div className="py-6">Content for {activeTab} tab</div>;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          {/* Enhanced Header */}
          <div className="flex items-center justify-between w-full py-6 px-4 2xl:px-8 bg-white border-b border-border-color mb-4">
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-primary-500/10">
                  <span className="icon icon-gear text-primary-500 text-xl" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-dark-500">General Settings</h1>
                  <p className="text-sm text-gray-500">Manage your account settings and preferences</p>
                </div>
              </div>
              <div className="mt-2">
                <Breadcrumb items={breadcrumbItems} />
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="text-right">
                <p className="text-sm font-medium text-dark-500">Last updated</p>
                <p className="text-xs text-gray-400">2 hours ago</p>
              </div>
              <div className="w-8 h-8 rounded-full bg-success-100 flex items-center justify-center">
                <span className="icon icon-check text-success-600 text-sm" />
              </div>
            </div>
          </div>
          <div className="w-full h-[calc(100dvh-137px)] overflow-y-auto pb-6 px-4 2xl:px-8">
            <div className="w-full rounded-xl">
              {/* Enhanced Tab Navigation */}
              <div className="bg-white rounded-lg shadow-sm border border-border-color mb-6">
                <nav className="flex overflow-x-auto">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center gap-3 px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200 ${
                        tab.id === activeTab
                          ? 'border-primary-500 text-primary-500 bg-primary-50'
                          : 'border-transparent text-gray-500 hover:text-primary-500 hover:bg-gray-50'
                      }`}
                    >
                      <span className={`icon ${tab.icon} text-xl w-8 h-8 flex items-center justify-center rounded-lg
                        ${tab.id === activeTab ? 'bg-primary-500/10' : 'bg-gray-500/10'}
                      `} />
                      <div className="text-left">
                        <div className="font-semibold">{tab.label}</div>
                        <div className="text-xs text-gray-400 hidden lg:block">{tab.description}</div>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="min-h-[400px]">
                {renderTabContent()}
              </div>
            </div>
          </div>

        </div>
      </div>
    </PrivateLayout>
  );
}
