'use client';

import React, { useState, useRef, useEffect } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import ProductStyle from '../components/master-management/ProductStyle';
import FilterField from '../components/table/FilterField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableItem from '../components/table/SortableItem';

export default function Page() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const [displayProperties, setDisplayProperties] = useState({
    'UoM Code': true,
    'Full Name': true,
    'Status': true,
  });
  const [items, setItems] = useState(Object.keys(displayProperties)); 

  // Update the click outside effect to handle both dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        displayMenuRef.current &&
        !displayMenuRef.current.contains(event.target)
      ) {
        setIsDisplayMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  // Add this new handler
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);

        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);

        return newItems;
      });
    }
  };

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Product Masters' },
    { label: 'Product Style' }
  ];

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Product Style</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex justify-end gap-3">
              <button type="button" className="btn btn-primary flex items-center gap-2" onClick={() => setIsModalOpen(true)}>
                <span className="icon icon-plus text-base" />
                Add Product Style
              </button>
            </div>
          </div>

          <div className="rounded-xl">
            {/* Filter filed */}
            <FilterField
              filterText={filterText}
              setFilterText={setFilterText}
              isSearchFilterOpen={isSearchFilterOpen}
              setIsSearchFilterOpen={setIsSearchFilterOpen}
              isDisplayMenuOpen={isDisplayMenuOpen}
              setIsDisplayMenuOpen={setIsDisplayMenuOpen}
              displayMenuRef={displayMenuRef}
            >
              <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
                <DndContext
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext
                    items={items}
                    strategy={verticalListSortingStrategy}
                  >
                    <ul className="space-y-1">
                      {items.map((key) => (
                        <SortableItem
                          key={key}
                          id={key}
                          value={key}
                          checked={displayProperties[key]}
                          onChange={() =>
                            setDisplayProperties((prev) => ({
                              ...prev,
                              [key]: !prev[key],
                            }))
                          }
                        />
                      ))}
                    </ul>
                  </SortableContext>
                </DndContext>
              </div>
            </FilterField>

            <ProductStyle isModalOpen={isModalOpen} setIsModalOpen={setIsModalOpen} />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
