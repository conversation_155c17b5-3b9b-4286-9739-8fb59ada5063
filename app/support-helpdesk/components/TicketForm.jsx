'use client';

import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import { format } from 'date-fns';
import FileUpload from '@/app/components/Inputs/FileUpload';

const TicketForm = ({ ticket, mode = 'create', onSubmit, onClose }) => {
  const [attachments, setAttachments] = useState([]);
  const isViewMode = mode === 'view';

  const formik = useFormik({
    initialValues: {
      category: ticket?.category || '',
      summary: ticket?.summary || '',
      description: ticket?.description || '',
      priority: ticket?.priority || 'medium',
      reply: '',
    },
    validationSchema: Yup.object({
      category: Yup.string().required('Category is required'),
      summary: Yup.string().required('Summary is required'),
      description: Yup.string().required('Description is required'),
      priority: Yup.string().required('Priority is required'),
      reply: isViewMode ? Yup.string() : Yup.string().notRequired(),
    }),
    onSubmit: async (values) => {
      const formData = new FormData();
      Object.keys(values).forEach(key => {
        formData.append(key, values[key]);
      });
      attachments.forEach(file => {
        formData.append('attachments', file);
      });
      onSubmit(formData);
    },
  });

  const categoryOptions = [
    { value: 'technical', label: 'Technical' },
    { value: 'billing', label: 'Billing' },
    { value: 'account', label: 'Account' },
    { value: 'product', label: 'Product' },
    { value: 'other', label: 'Other' },
  ];

  const priorityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ];

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setAttachments([...attachments, ...files]);
  };

  const handleRemoveFile = (index) => {
    setAttachments(attachments.filter((_, i) => i !== index));
  };

  return (
    <form onSubmit={formik.handleSubmit}>
      <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {isViewMode && (
          <div className="grid grid-cols-2 gap-4 p-4 bg-surface-100 rounded-lg mb-4">
            <div>
              <div className="text-sm text-gray-500">Ticket ID</div>
              <div className="font-medium">{ticket.id}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Created By</div>
              <div className="font-medium">{ticket.userName}</div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Created At</div>
              <div className="font-medium">
                {format(new Date(ticket.createdAt), 'MMM dd, yyyy HH:mm')}
              </div>
            </div>
            <div>
              <div className="text-sm text-gray-500">Last Updated</div>
              <div className="font-medium">
                {format(new Date(ticket.lastUpdated), 'MMM dd, yyyy HH:mm')}
              </div>
            </div>
          </div>
        )}


        <div className="flex flex-col mb-4">
          <span className="form-label">
            Industry Type
          </span>
          <SelectField
            label="Category"
            name="category"
            placeholder="Select category"
            className="single-select"
            value={formik.values.category}
            onChange={(selected) => formik.setFieldValue('category', selected.value)}
            error={formik.touched.category && formik.errors.category}
            options={categoryOptions}
            disabled={isViewMode}
          />
        </div>

        <div className="flex flex-col mb-4">
          <span className="form-label">
            Priority
          </span>
          <SelectField
            label="Priority"
            name="priority"
            placeholder="Select priority"
            className="single-select"
            value={formik.values.priority}
            onChange={(selected) => formik.setFieldValue('priority', selected.value)}
            error={formik.touched.priority && formik.errors.priority}
            options={priorityOptions}
            disabled={isViewMode}
          />
        </div>

        <InputField
          id="summary"
          label="Summary"
          name="summary"
          placeholder="Brief summary of the issue"
          value={formik.values.summary}
          onChange={formik.handleChange}
          error={formik.touched.summary && formik.errors.summary}
          disabled={isViewMode}
        />

        <div className="flex flex-col mb-4">
          <label className="form-label">Description</label>
          <textarea
            id="description"
            name="description"
            className="form-control"
            rows={6}
            placeholder="Detailed description of the issue"
            value={formik.values.description}
            onChange={formik.handleChange}
            disabled={isViewMode}
          />
          {formik.touched.description && formik.errors.description && (
            <div className="text-danger-500 text-sm mt-1">{formik.errors.description}</div>
          )}
        </div>

        {!isViewMode && (
          <div className="flex flex-col mb-4">
            <FileUpload
              label="Attachments"
              accept=".pdf,.jpg,.png"
              maxFileSizeMB={5}
              tooltipContent="Upload supporting documents (PDF, DOC, Images)"
              name="attachments"
              formik={formik}
              onChange={(files) => {
                setAttachments(files);
                formik.setFieldValue('attachments', files);
              }}
              id="attachments"
            />
            {attachments.length > 0 && (
              <div className="mt-2 space-y-2">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-surface-100 rounded">
                    <div className="text-sm truncate">{file.name}</div>
                    <button
                      type="button"
                      className="text-danger-500 hover:text-danger-600"
                      onClick={() => handleRemoveFile(index)}
                    >
                      <span className="icon icon-x" />
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {isViewMode && (
          <div className="flex flex-col mb-4">
            <label className="form-label">Reply</label>
            <textarea
              id="reply"
              name="reply"
              className="form-control"
              rows={6}
              placeholder="Type your reply..."
              value={formik.values.reply}
              onChange={formik.handleChange}
            />
          </div>
        )}
      </div>

      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn btn-primary"
          disabled={formik.isSubmitting}
        >
          {isViewMode ? 'Send Reply' : 'Create Ticket'}
        </button>
      </div>
    </form>
  );
};

export default TicketForm;
