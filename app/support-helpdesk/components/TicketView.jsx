'use client';

import React, { useState } from 'react';
import { format, isValid, parseISO } from 'date-fns';
import Badge from '@/app/components/Inputs/Badge';

const TicketView = ({ ticket, onReply, onClose }) => {
  const [reply, setReply] = useState('');

  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = parseISO(dateString);
    return isValid(date) ? format(date, 'MMM dd, yyyy HH:mm') : '-';
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      open: { color: 'warning', label: 'Open' },
      in_progress: { color: 'info', label: 'In Progress' },
      escalated: { color: 'danger', label: 'Escalated' },
      resolved: { color: 'success', label: 'Resolved' },
      closed: { color: 'gray', label: 'Closed' },
    };

    const { color, label } = statusMap[status] || statusMap.open;
    return <Badge color={color} label={label} />;
  };

  const handleSubmitReply = () => {
    if (reply.trim()) {
      onReply(reply);
      setReply('');
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Ticket Details */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-surface-100 rounded-xl mb-4">
          <div className="flex flex-col gap-1">
            <span className="text-sm text-gray-400">Ticket ID</span>
            <span className="font-semibold text-sm">{ticket.id}</span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-sm text-gray-400">Status</span>
            <span className="text-sm">{getStatusBadge(ticket.status)}</span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-sm text-gray-400">Created By</span>
            <span className="font-semibold text-sm">{ticket.userName}</span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-sm text-gray-400">Category</span>
            <span className="font-semibold capitalize text-sm">{ticket.category}</span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-sm text-gray-400">Created At</span>
            <span className="font-semibold text-sm">
              {formatDate(ticket.createdAt)}
            </span>
          </div>
          <div className="flex flex-col gap-1">
            <span className="text-sm text-gray-400">Last Updated</span>
            <span className="font-semibold text-sm">
              {formatDate(ticket.lastUpdated)}
            </span>
          </div>
        </div>

        {/* Issue Summary */}
        <div className="mb-4">
          <h4 className="text-base font-medium mb-2">Issue Summary</h4>
          <div className="p-4 bg-surface-100/50 rounded-xl">
            <p className="text-sm">{ticket.summary || '-'}</p>
          </div>
        </div>

        {/* Description */}
        <div className="mb-4">
          <h4 className="text-base font-medium mb-2">Description</h4>
          <div className="p-4 bg-surface-100/50 rounded-xl whitespace-pre-wrap">
            <p className="text-sm">{ticket.description || '-'}</p>
          </div>
        </div>

        {/* Attachments */}
        {ticket.attachments && ticket.attachments.length > 0 && (
          <div className="mb-4">
            <h4 className="text-base font-medium mb-2">Attachments</h4>
            <div className="space-y-2">
              {ticket.attachments.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-surface-100 rounded"
                >
                  <div className="flex items-center gap-2">
                    <span className="icon icon-paperclip text-gray-500" />
                    <span className="text-sm">{file.name}</span>
                  </div>
                  <a
                    href={file.url}
                    download
                    className="text-primary-500 hover:text-primary-600"
                  >
                    <span className="icon icon-download" />
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Conversation Thread */}
        {ticket.thread && ticket.thread.length > 0 && (
          <div className="mb-4 flex-1 overflow-y-auto">
            <h4 className="text-base font-medium mb-2">Conversation</h4>
            <div className="space-y-4">
              {ticket.thread.map((message, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-xl ${
                    message.isAdmin ? 'bg-primary-500/5 ml-8' : 'bg-surface-100 mr-8'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="font-medium">{message.userName}</div>
                    <div className="text-sm text-gray-500">
                      {formatDate(message.timestamp)}
                    </div>
                  </div>
                  <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Reply Box */}
        {ticket.status !== 'closed' && (
          <div className="mt-auto border-t border-border-color pt-4">
            <div className="flex flex-col mb-3">
              <label className="form-label">Reply</label>
              <textarea
                id="reply"
                name="reply"
                className="form-control"
                rows={5}
                placeholder="Type your reply..."
                value={reply}
                onChange={(e) => setReply(e.target.value)}
              />
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
        <button
          type="button"
          className="btn btn-primary"
          onClick={handleSubmitReply}
          disabled={!reply.trim()}
        >
          Send Reply
        </button>
      </div>
    </div>
  );
};

export default TicketView;
