'use client';

import React, { useMemo, useState } from 'react';
import { format, isValid, parseISO } from 'date-fns';
import { Tooltip } from 'react-tooltip';
import DataTable from 'react-data-table-component';
import Badge from '@/app/components/Inputs/Badge';
import SortIcon from '@/app/components/table/SortIcon';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const TicketTable = ({ onViewTicket, onEscalate, onUpdateStatus }) => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState('');
  const [sortDirection, setSortDirection] = useState('asc');
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = parseISO(dateString);
    return isValid(date) ? format(date, 'MMM dd, yyyy HH:mm') : '-';
  };

  const getStatusBadge = (status) => {
    const statusMap = {
      open: { color: 'warning', label: 'Open' },
      in_progress: { color: 'info', label: 'In Progress' },
      escalated: { color: 'danger', label: 'Escalated' },
      resolved: { color: 'success', label: 'Resolved' },
      closed: { color: 'gray', label: 'Closed' },
    };

    const { color, label } = statusMap[status] || statusMap.open;
    return <Badge color={color} label={label} />;
  };

  const columns = useMemo(
    () => [
      {
        name: 'Ticket ID',
        selector: row => row.id,
        sortable: true,
        width: '120px',
      },
      {
        name: 'User Name',
        selector: row => row.userName,
        sortable: true,
      },
      {
        name: 'Category',
        selector: row => row.category,
        sortable: true,
      },
      {
        name: 'Issue Summary',
        selector: row => row.summary,
        grow: 2,
        wrap: true,
      },
      {
        name: 'Status',
        selector: row => row.status,
        sortable: true,
        cell: row => getStatusBadge(row.status),
      },
      {
        name: 'Last Updated',
        selector: row => row.lastUpdated,
        sortable: true,
        cell: row => formatDate(row.lastUpdated),
      },
      {
        name: 'Actions',
        grow: 0,
        cell: row => (
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
              onClick={() => onViewTicket(row)}
              data-tooltip-id="ticket-action"
              data-tooltip-content="View Details"
            >
              <span className="icon icon-eye" />
            </button>

            {/* {row.status !== 'escalated' && (
              <button
                type="button"
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
                onClick={() => onEscalate(row.id)}
                data-tooltip-id="ticket-action"
                data-tooltip-content="Escalate"
              >
                <span className="icon icon-info" />
              </button>
            )} */}

            {/* <SelectField
              label="Status"
              name="status"
              className="single-select"
              placeholder="Select status"
              value={row.status}
              onChange={(selected) => onUpdateStatus(row.id, selected.value)}
              options={[
                { value: 'open', label: 'Open' },
                { value: 'in_progress', label: 'In Progress' },
                { value: 'resolved', label: 'Resolved' },
                { value: 'closed', label: 'Closed' },
              ]}
            /> */}
          </div>
        ),
      },
    ],
    [onViewTicket, onEscalate, onUpdateStatus]
  );

  // Sample data - replace with API data
  const data = [
    {
      id: 'TKT-001',
      userName: 'John Doe',
      category: 'Technical',
      summary: 'Unable to access dashboard',
      status: 'open',
      lastUpdated: '2025-05-30T10:30:00',
    },
    // Add more sample tickets...
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  
  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={data}
        pagination
        selectableRows
        onSelectedRowsChange={handleSelectedRowsChange}
        responsive
        customStyles={customStyles}
        selectableRowsComponent={CustomCheckbox}
        selectableRowsComponentProps={{
          indeterminate: (isIndeterminate) => isIndeterminate,
        }}
        selectableRowsHighlight
        fixedHeader={true}
        highlightOnHover
        pointerOnHover
        className="custom-table"
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={handleSort}
        sortField={sortedField}
        defaultSortAsc={true}
        paginationPerPage={8}
        paginationRowsPerPageOptions={[8]}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
          noRowsPerPage: true,
        }}
        paginationComponent={(props) => (
          <div className="flex items-center justify-between w-full pt-6">
            <div className="text-sm text-dark-500/60 font-medium">
              {selectedRows.length} of {data.length} row(s) selected
            </div>

            <div className="flex items-center gap-3 ml-auto">
              <div className="text-sm text-dark-500 font-medium">
                <span className="text-gray-500">Showing</span>{' '}
                {(props.currentPage - 1) * props.rowsPerPage + 1}-
                {Math.min(props.currentPage * props.rowsPerPage, data.length)}{' '}
                <span className="text-gray-500">of</span> {data.length}
              </div>
              <div className="flex items-center gap-2 bg-dark-500/5 rounded-lg overflow-hidden">
                <button
                  onClick={() => props.onChangePage(1)}
                  disabled={props.currentPage === 1}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-double-left text-base align-middle" />
                </button>
                <button
                  onClick={() => props.onChangePage(props.currentPage - 1)}
                  disabled={props.currentPage === 1}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-left text-base align-middle" />
                </button>
                <button
                  onClick={() => props.onChangePage(props.currentPage + 1)}
                  disabled={props.currentPage === props.totalPages}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-right text-base align-middle" />
                </button>
                <button
                  onClick={() => props.onChangePage(props.totalPages)}
                  disabled={props.currentPage === props.totalPages}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-double-right text-base align-middle" />
                </button>
              </div>
            </div>
          </div>
        )}
        />
        <Tooltip
          id="ticket-action"
          className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
        />
    </>
  );
};

export default TicketTable;
