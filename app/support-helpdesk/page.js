'use client';

import React, { useState, useRef } from 'react';
import BaseModal from '@/app/components/modals/BaseModal';
import BaseOffCanvas from '@/app/components/offCanvas/BaseOffCanvas';
import TicketTable from './components/TicketTable';
import TicketForm from './components/TicketForm';
import TicketView from './components/TicketView';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import FilterField from '../components/table/FilterField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableItem from '../components/table/SortableItem';

const SupportHelpdesk = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const [displayProperties, setDisplayProperties] = useState({
    TicketId: true,
    UserName: true,
    Category: true,
    Status: true,
    LastUpdated: true,
  });
  const [items, setItems] = useState(Object.keys(displayProperties));
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isTicketFormOpen, setIsTicketFormOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [filters, setFilters] = useState({
    category: '',
    status: '',
    dateRange: [null, null],
  });

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Support & Helpdesk' }
  ];

  const handleCreateTicket = (ticketData) => {
    // API call to create ticket
    console.log('Creating ticket:', ticketData);
    setIsTicketFormOpen(false);
  };

  const handleViewTicket = (ticket) => {
    setSelectedTicket(ticket);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);

        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);

        return newItems;
      });
    }
  };

  const handleEscalateTicket = (ticketId) => {
    // API call to escalate ticket
    console.log('Escalating ticket:', ticketId);
  };

  const handleUpdateStatus = (ticketId, status) => {
    // API call to update ticket status
    console.log('Updating ticket status:', ticketId, status);
  };

  const handleReplyToTicket = (reply) => {
    // API call to add reply
    console.log('Adding reply to ticket:', selectedTicket?.id, reply);
    setSelectedTicket(null);
  };

  const handleGlobalFiltersChange = (newFilters) => {
    setFilters(newFilters);
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />
      </div>

      <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col gap-1">
            <h1 className="text-xl font-bold">Support & Helpdesk</h1>
            <Breadcrumb items={breadcrumbItems} />
          </div>
          <div className="flex gap-3">
            <button onClick={() => setIsTicketFormOpen(true)} className="btn flex items-center gap-2">
              <span className="icon icon-plus text-base" />
              Create Ticket
            </button>
          </div>
        </div>

        <div className="rounded-xl">
          <FilterField
            filterText={filterText}
            setFilterText={setFilterText}
            isSearchFilterOpen={isSearchFilterOpen}
            setIsSearchFilterOpen={setIsSearchFilterOpen}
            isDisplayMenuOpen={isDisplayMenuOpen}
            setIsDisplayMenuOpen={setIsDisplayMenuOpen}
            displayMenuRef={displayMenuRef}
            filters={filters}
            setGlobalFilters={handleGlobalFiltersChange}
          >
            <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
              <DndContext
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={items}
                  strategy={verticalListSortingStrategy}
                >
                  <ul className="space-y-1">
                    {items.map((key) => (
                      <SortableItem
                        key={key}
                        id={key}
                        value={key}
                        checked={displayProperties[key]}
                        onChange={() =>
                          setDisplayProperties((prev) => ({
                            ...prev,
                            [key]: !prev[key],
                          }))
                        }
                      />
                    ))}
                  </ul>
                </SortableContext>
              </DndContext>
            </div>
          </FilterField>

          <TicketTable
            onViewTicket={handleViewTicket}
            onEscalate={handleEscalateTicket}
            onUpdateStatus={handleUpdateStatus}
          />
        </div>

        {/* Create Ticket Modal */}
        <BaseOffCanvas
          isOpen={isTicketFormOpen}
          onClose={() => setIsTicketFormOpen(false)}
          title="Create Support Ticket"
          size="sm"
        >
          <TicketForm
            onSubmit={handleCreateTicket}
            onClose={() => setIsTicketFormOpen(false)}
          />
        </BaseOffCanvas>

        {/* View Ticket Modal */}
        <BaseOffCanvas
          isOpen={!!selectedTicket}
          onClose={() => setSelectedTicket(null)}
          title={`Ticket - #${selectedTicket?.id || ''}`}
          size="sm"
        >
          {selectedTicket && (
            <TicketView
              ticket={selectedTicket}
              onReply={handleReplyToTicket}
              onClose={() => setSelectedTicket(null)}
            />
          )}
        </BaseOffCanvas>
      </div>
    </PrivateLayout>
  );
};

export default SupportHelpdesk;