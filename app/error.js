// app/error.js
'use client';

import Link from 'next/link';

/************************************   
   @purpose : error page
   <AUTHOR> INIC
  **********************************/
export default function ErrorPage() {
  return (
    // <div className="d-flex align-items-center justify-content-center text-center h-100 error-wrap">
    //   <div className="content-404">
    //     <div className="content-404-title">Error</div>
    //     <div className="content-404-link mt-3">
    //       <Link href="/">
    //         <button
    //           // eslint-disable-next-line react/no-unknown-property
    //           as="button"
    //           href="window.location"
    //           className="btn btn-primary mt-40"
    //           title="Back To Home"
    //         >
    //           <span className="icon-left-caret" />
    //           Back To Home{' '}
    //         </button>
    //       </Link>
    //     </div>
    //   </div>
    // </div>

    <main className="grid min-h-dvh place-items-center bg-white px-6 py-24 sm:py-32 lg:px-8">
      <div className="text-center">
        <p className="text-primary-500 text-5xl font-bold">Oops!</p>
        <h2 className="mt-4 text-5xl font-semibold tracking-tight text-balance text-gray-900 sm:text-5xl">
          Something went wrong
        </h2>
        {/* <p className="mt-6 text-lg font-medium text-pretty text-gray-500 sm:text-xl/8">Sorry, we couldn’t find the page you’re looking for.</p> */}
        <div className="mt-10 flex items-center justify-center gap-x-4">
          <Link
            href="/"
            className="!inline-flex items-center gap-2 btn btn-primary"
          >
            <span className="icon icon-caret-left" />
            Back To Home{' '}
          </Link>
        </div>
      </div>
    </main>
  );
}
