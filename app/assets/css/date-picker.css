/* CUSTOM DATEPICKER */

.custom-datepicker {
  .form-control.calendar-icon {
    @apply pr-11 bg-no-repeat bg-[length:14px_14px] bg-[position:right_16px_center] cursor-pointer bg-white;
    background-image: url("data:image/svg+xml,%3Csvg width='24' height='26' viewBox='0 0 24 26' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M22 2H19V1C19 0.734784 18.8946 0.48043 18.7071 0.292893C18.5196 0.105357 18.2652 0 18 0C17.7348 0 17.4804 0.105357 17.2929 0.292893C17.1054 0.48043 17 0.734784 17 1V2H7V1C7 0.734784 6.89464 0.48043 6.70711 0.292893C6.51957 0.105357 6.26522 0 6 0C5.73478 0 5.48043 0.105357 5.29289 0.292893C5.10536 0.48043 5 0.734784 5 1V2H2C1.46957 2 0.960859 2.21071 0.585786 2.58579C0.210714 2.96086 0 3.46957 0 4V24C0 24.5304 0.210714 25.0391 0.585786 25.4142C0.960859 25.7893 1.46957 26 2 26H22C22.5304 26 23.0391 25.7893 23.4142 25.4142C23.7893 25.0391 24 24.5304 24 24V4C24 3.46957 23.7893 2.96086 23.4142 2.58579C23.0391 2.21071 22.5304 2 22 2ZM5 4V5C5 5.26522 5.10536 5.51957 5.29289 5.70711C5.48043 5.89464 5.73478 6 6 6C6.26522 6 6.51957 5.89464 6.70711 5.70711C6.89464 5.51957 7 5.26522 7 5V4H17V5C17 5.26522 17.1054 5.51957 17.2929 5.70711C17.4804 5.89464 17.7348 6 18 6C18.2652 6 18.5196 5.89464 18.7071 5.70711C18.8946 5.51957 19 5.26522 19 5V4H22V8H2V4H5ZM22 24H2V10H22V24ZM13.5 14.5C13.5 14.7967 13.412 15.0867 13.2472 15.3334C13.0824 15.58 12.8481 15.7723 12.574 15.8858C12.2999 15.9993 11.9983 16.0291 11.7074 15.9712C11.4164 15.9133 11.1491 15.7704 10.9393 15.5607C10.7296 15.3509 10.5867 15.0836 10.5288 14.7926C10.4709 14.5017 10.5006 14.2001 10.6142 13.926C10.7277 13.6519 10.92 13.4176 11.1666 13.2528C11.4133 13.088 11.7033 13 12 13C12.3978 13 12.7794 13.158 13.0607 13.4393C13.342 13.7206 13.5 14.1022 13.5 14.5ZM19 14.5C19 14.7967 18.912 15.0867 18.7472 15.3334C18.5824 15.58 18.3481 15.7723 18.074 15.8858C17.7999 15.9993 17.4983 16.0291 17.2074 15.9712C16.9164 15.9133 16.6491 15.7704 16.4393 15.5607C16.2296 15.3509 16.0867 15.0836 16.0288 14.7926C15.9709 14.5017 16.0007 14.2001 16.1142 13.926C16.2277 13.6519 16.42 13.4176 16.6666 13.2528C16.9133 13.088 17.2033 13 17.5 13C17.8978 13 18.2794 13.158 18.5607 13.4393C18.842 13.7206 19 14.1022 19 14.5ZM8 19.5C8 19.7967 7.91203 20.0867 7.7472 20.3334C7.58238 20.58 7.34811 20.7723 7.07403 20.8858C6.79994 20.9993 6.49834 21.0291 6.20736 20.9712C5.91639 20.9133 5.64912 20.7704 5.43934 20.5607C5.22956 20.3509 5.0867 20.0836 5.02882 19.7926C4.97094 19.5017 5.00065 19.2001 5.11418 18.926C5.22771 18.6519 5.41997 18.4176 5.66665 18.2528C5.91332 18.088 6.20333 18 6.5 18C6.89782 18 7.27936 18.158 7.56066 18.4393C7.84196 18.7206 8 19.1022 8 19.5ZM13.5 19.5C13.5 19.7967 13.412 20.0867 13.2472 20.3334C13.0824 20.58 12.8481 20.7723 12.574 20.8858C12.2999 20.9993 11.9983 21.0291 11.7074 20.9712C11.4164 20.9133 11.1491 20.7704 10.9393 20.5607C10.7296 20.3509 10.5867 20.0836 10.5288 19.7926C10.4709 19.5017 10.5006 19.2001 10.6142 18.926C10.7277 18.6519 10.92 18.4176 11.1666 18.2528C11.4133 18.088 11.7033 18 12 18C12.3978 18 12.7794 18.158 13.0607 18.4393C13.342 18.7206 13.5 19.1022 13.5 19.5ZM19 19.5C19 19.7967 18.912 20.0867 18.7472 20.3334C18.5824 20.58 18.3481 20.7723 18.074 20.8858C17.7999 20.9993 17.4983 21.0291 17.2074 20.9712C16.9164 20.9133 16.6491 20.7704 16.4393 20.5607C16.2296 20.3509 16.0867 20.0836 16.0288 19.7926C15.9709 19.5017 16.0007 19.2001 16.1142 18.926C16.2277 18.6519 16.42 18.4176 16.6666 18.2528C16.9133 18.088 17.2033 18 17.5 18C17.8978 18 18.2794 18.158 18.5607 18.4393C18.842 18.7206 19 19.1022 19 19.5Z' fill='%23686D6B'/%3E%3C/svg%3E%0A");
  }

  .react-datepicker {
    @apply bg-white border border-[#EBEBEB] rounded-[10px] p-[15px] text-dark-500 shadow-[0px_0px_30px_0px_rgba(41,45,52,0.15)];
  }

  .react-datepicker-wrapper {
    @apply w-full;
  }
  .react-datepicker__header {
    @apply p-0 bg-transparent border-none;
  }
  .react-datepicker__current-month {
    @apply text-base text-left mb-3;
  }
  .react-datepicker__day-names {
    @apply flex justify-between mt-[10px] pt-[5px] border-t border-border-color;
  }
  .react-datepicker-popper .react-datepicker__triangle {
    @apply stroke-white !text-white !fill-white;
  }
  .react-datepicker__day-name {
    @apply inline-flex items-center justify-center w-10 h-10 m-0 text-xs font-semibold text-dark-500 uppercase;
  }
  .react-datepicker__month {
    @apply flex flex-col justify-between max-h-[262px] h-full m-0 2xl:max-h-[225px];
  }
  .react-datepicker__day--outside-month {
    @apply text-dark-500;
  }
  .react-datepicker__day {
    @apply inline-flex items-center justify-center w-8 h-8 m-0.5 leading-[1.5] text-xs font-normal rounded-xl hover:bg-primary-500 hover:text-white focus:bg-primary-500 focus:text-white;
  }

  .react-datepicker__day--in-range,
  .react-datepicker__day--selected,
  .react-datepicker__day--keyboard-selected {
    @apply bg-primary-500 text-white font-semibold rounded-xl hover:bg-primary-600;
  }
  .react-datepicker__day--keyboard-selected:not([aria-disabled='true']) {
    @apply hover:bg-primary-600;
  }
  .react-datepicker__navigation-icon {
    @apply hidden;
  }
  .react-datepicker__day:not([aria-disabled='true']) {
    @apply hover:bg-primary-500 hover:text-white hover:rounded-xl;
  }
  .react-datepicker__day--in-selecting-range:not(
      .react-datepicker__day--in-range,
      .react-datepicker__month-text--in-range,
      .react-datepicker__quarter-text--in-range,
      .react-datepicker__year-text--in-range
    ) {
    @apply bg-primary-500/10 text-primary-500 rounded-xl hover:bg-primary-500;
  }
  /* .react-datepicker__day--keyboard-selected {
      @apply !bg-primary-500 !text-white !rounded-xl hover:!bg-primary-600;
    } */
  .react-datepicker__week {
    @apply flex justify-between;
  }
  .react-datepicker__navigation--previous {
    @apply left-auto right-[38px] top-[18px];
  }
  .react-datepicker__navigation {
    @apply w-[25px] h-[25px];
  }
  .react-datepicker__navigation:hover
    .react-datepicker__navigation-icon::before,
  .react-datepicker__navigation:hover
    .react-datepicker__navigation-icon::after {
    @apply border-primary-500;
  }
  .react-datepicker__navigation {
    text-indent: unset;
    @apply -mt-[5px];
    @apply before:content-['\e903'] before:font-['icomoon'] before:text-sm before:text-black before:transition-colors before:duration-300 before:ease-in-out;
    @apply hover:before:text-primary-500;
  }
  .react-datepicker__navigation--next {
    @apply right-[6px] top-[18px] rotate-180;
  }
  .react-datepicker__year-read-view--down-arrow,
  .react-datepicker__month-read-view--down-arrow {
    @apply right-0 -top-[2px] w-auto h-auto border-none transform rotate-0;
    @apply before:content-['\e902'] before:font-['icomoon'] before:text-sm before:text-black before:transition-colors before:duration-300 before:ease-in-out;
  }
  .react-datepicker__year-dropdown,
  .react-datepicker__month-dropdown {
    @apply left-0 bg-white border border-border-color rounded-xl p-2 text-dark-500 shadow-[0px_0px_30px_0px_rgba(41,45,52,0.15)] text-left;
  }
  .react-datepicker__year-dropdown {
    @apply right-0 left-auto text-center;
  }
  .react-datepicker__year-option,
  .react-datepicker__month-option {
    @apply py-0.5 px-2 rounded-md hover:bg-primary-500/10 hover:text-primary-500 my-0.5;
  }
  .react-datepicker__year-option.react-datepicker__year-option--selected_year,
  .react-datepicker__month-option.react-datepicker__month-option--selected_month {
    @apply bg-primary-500/10 text-primary-500 font-medium;
  }
  .react-datepicker__year-option .react-datepicker__year-option--selected,
  .react-datepicker__month-option .react-datepicker__month-option--selected {
    @apply hidden;
  }

  .react-datepicker__navigation.react-datepicker__navigation--years {
    @apply mt-0 top-0;
  }
  .react-datepicker__navigation.react-datepicker__navigation--years-previous {
    @apply -rotate-90;
  }
  .react-datepicker__navigation.react-datepicker__navigation--years-upcoming {
    @apply rotate-90;
  }

  .react-datepicker--time-only {
    @apply p-0;
  }

  .react-datepicker__header--time {
    @apply px-[10px] py-[8px] mb-[8px] border-b border-gray-200;
  }

  .react-datepicker__time-list-item {
    @apply text-[16px] h-auto;
  }

  .react-datepicker__time-list-item--selected {
    @apply !bg-primary-500 text-white;
  }

  .react-datepicker-time__header {
    @apply text-[16px];
  }

  .react-datepicker-popper {
    @apply z-[30];
  }

  .react-datepicker-popper[data-placement^='bottom']
    .react-datepicker__triangle::before,
  .react-datepicker-popper[data-placement^='bottom']
    .react-datepicker__triangle::after {
    @apply border-b-white;
  }
  .react-datepicker-popper[data-placement^="bottom"] .react-datepicker__triangle,
  .react-datepicker-popper[data-placement^="top"] .react-datepicker__triangle {
    @apply !left-[180px];
  }

  .react-datepicker__time .react-datepicker__time-box {
    @apply w-[90px];
  }

  .react-datepicker__close-icon {
    @apply pr-10;
    @apply after:hidden;
    @apply before:content-['\e91c'] before:font-['icomoon'] before:text-danger-500 before:text-xs before:transition-colors before:duration-300 before:ease-in-out;
  }
  .react-datepicker__close-icon:hover:after {
    @apply bg-danger-500;
  }
  .react-datepicker__close-icon::after {
    @apply bg-danger-500/80 transition-all ease-in-out duration-300;
  }

  .react-datepicker__header__dropdown {
    @apply mt-[5px] flex gap-[8px];
  }

  .react-datepicker__header__dropdown.react-datepicker__header__dropdown--scroll
    > div {
    @apply w-1/2 px-[10px] py-[6px] m-0 border border-border-color rounded-[6px] text-left;
  }
  .react-datepicker-year-header {
    @apply text-left;
  }
  .react-datepicker__year {
    @apply mt-4 mx-0 mb-0;
  }
  .react-datepicker__year-wrapper {
    @apply grid grid-cols-3 gap-2;
  }
  .react-datepicker__year .react-datepicker__year-text {
    @apply inline-flex items-center justify-center w-14 h-6 m-0 leading-[1.5] text-xs font-normal rounded-md bg-transparent text-dark-500 border border-border-color hover:bg-primary-500 hover:text-white focus:bg-primary-500 hover:border-primary-500 focus:text-white;
  }
  .react-datepicker__year-text--selected {
    @apply !bg-primary-500 !text-white !border-primary-500;
  }
}
