@layer utilities {
  .kbd-shadow {
    box-shadow: 0px 3px 1px 0px #1c273126;
  }

  .transition-base {
    @apply transition-all duration-300 ease-in-out;
  }

  .text-gradient {
    background: linear-gradient( 90deg, #ef2672 0%, #dd2f7b 34%, #3a7fc9 62%, #129cfb 100% );
    @apply bg-clip-text text-transparent;
  }

  .shadow-custom {
    box-shadow: 1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset, 0px 8px 16px -4px rgba(26, 26, 26, 0.22);
  }

  .react-tooltip {
    @apply bg-white text-dark-500 rounded-xl p-3;
  }
  .rtl {
    direction: rtl
  }
  .text-gradient-2 {
    background: linear-gradient( 75.32deg, #C31A5A 10.38%, #016CB5 89.62% );
    @apply bg-clip-text text-transparent;
  }

  .hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none; /* WebKit */
  }
}

hr {
  @apply border-border-color;
}
/* BUTTON STYLE */
button {
  @apply cursor-pointer;
}
.btn {
  @apply bg-primary-500 rounded-lg hover:bg-primary-600 transition-all duration-200 ease-in-out;
  @apply py-2 px-4;
  @apply text-sm text-white font-semibold text-nowrap;
  @apply cursor-pointer;
}

.btn:disabled {
  @apply bg-dark-500/10 text-dark-500/20 cursor-not-allowed shadow-none;
}

.btn.btn-sm {
  @apply py-1.5 px-3 text-xs font-medium;
}


.btn.btn-gray {
  @apply bg-gray-100 text-dark-500 hover:bg-gray-200 transition-all duration-300 ease-in-out cursor-pointer shadow-none;
}
.btn.btn-danger {
  @apply bg-danger-500 text-white hover:bg-danger-600;
}

.btn.btn-outline-secondary {
  @apply bg-transparent border border-dark-500 text-dark-500;
  @apply hover:bg-dark-500 hover:text-white;
}
.btn.btn-outline-primary {
  @apply bg-transparent border border-primary-500 text-primary-500;
  @apply hover:bg-primary-500 hover:text-white;
}

.btn.btn-outline-danger {
  @apply bg-transparent border border-danger-500 text-danger-500;
  @apply hover:bg-danger-500 hover:text-white hover:border-danger-500;
}

.btn.btn-outline-secondary:disabled {
  @apply border-dark-500/10 text-dark-500/20;
  @apply hover:bg-transparent hover:text-dark-500/20;
}

.btn.btn-outline-gray {
  @apply bg-white text-dark-500 border-1 border-gray-100;
  @apply hover:bg-gray-500/10;
}
.btn.btn-outline-gray:disabled {
  @apply opacity-40;
}
.btn.btn-success {
  @apply bg-success-500 text-white hover:bg-success-600;
}
.btn.btn-outline-success {
  @apply bg-transparent border border-success-500 text-success-500;
  @apply hover:bg-success-500 hover:text-white;
}

/* FORM STYLE */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  @apply m-0;
  -webkit-appearance: none;
}

input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.form-control {
  @apply border border-border-color rounded-lg;
  @apply py-1 px-3;
  @apply text-dark-500 text-sm font-medium;
  @apply focus:outline-none focus:border-dark-500 placeholder:text-gray-200 placeholder:text-sm;
  @apply w-full min-h-[38px];
}

.form-text-bold {
  @apply font-semibold text-sm;
}

.form-label {
  @apply font-medium text-sm text-gray-500;
  @apply mb-1;
}

.form-check-input {
  @apply w-5 h-5 rounded-sm;
}

/* AUTHENTICATION PAGE CARD */
.card {
  @apply bg-white rounded-xl border border-border-color p-4;
  /* box-shadow: 0 1px 2px 0 rgb(0 0 0/0.05); */

}

.opt-fill .form-control {
  @apply w-[50px] h-[38px] rounded-xl border-border-color text-sm;
  @apply focus:outline-none focus:border-gray-100;
}

/* REACT SELECT */
.single-select .custom_select__control {
  @apply bg-transparent rounded-lg border border-border-color;
  @apply py-1 px-3;
  @apply text-dark-500 text-sm font-medium;
  @apply focus:outline-none focus:border-gray-100 hover:border-gray-100;
  @apply min-h-[38px];
}
.single-select.sm-select .custom_select__control {
  @apply min-h-8;
}

.single-select .custom_select__single-value {
  @apply text-sm leading-normal;
}

.multi-select.single-select .custom_select__control {
  @apply py-1;
}

.input-group-select.single-select .custom_select__control {
  @apply border-0;
}

.input-group-select.single-select .custom_select__single-value {
  @apply text-right mr-2;
}

.single-select .custom_select__placeholder {
  @apply text-gray-200 text-sm;
}

.single-select .custom_select__control--is-disabled {
  @apply bg-gray-500/5 text-gray-200 border-border-color cursor-not-allowed;
}
.single-select.custom_select--is-disabled {
  @apply cursor-not-allowed;
}

.single-select .custom_select__control--is-focused {
  @apply shadow-none border-dark-500 hover:border-dark-500;
}

.single-select .custom_select__input-container {
  @apply m-0 p-0;
}

.single-select .custom_select__value-container {
  @apply p-0 leading-normal;
}

.single-select .custom_select__indicator {
  @apply relative p-0;
  @apply after:content-['\e94f'] after:text-dark-500 after:text-base after:font-['icomoon'];
}

.single-select .custom_select__indicator svg {
  @apply hidden;
}

.single-select .custom_select__indicator-separator {
  @apply hidden;
}

.select-fill .custom_select__control {
  @apply bg-surface border-transparent focus:outline-none focus:border-gray-100 hover:border-gray-100;
}

.select-fill .custom_select__placeholder {
  @apply text-dark-500/20;
}

.select-fill .custom_select__control--is-focused {
  @apply shadow-none border-gray-100 hover:border-gray-100;
}

.single-select .custom_select__multi-value {
  @apply rounded-lg bg-dark-500/20 overflow-hidden;
}

.single-select .custom_select__multi-value__label {
  @apply font-medium text-xs text-dark-500 py-0.5 px-1.5;
}

.single-select .custom_select__multi-value__remove {
  @apply after:content-['\e91b'] after:text-sm after:text-dark-500 after:font-['icomoon'] after:transition-all after:ease-in-out after:duration-300;
  @apply p-0 mr-1.5 hover:after:text-danger-500 hover:bg-transparent cursor-pointer after:transition-all after:ease-in-out;
}

.single-select .custom_select__multi-value__remove svg {
  @apply hidden;
}

.custom_select__menu-portal {
  @apply !z-50;
}

.single-select .custom_select__menu,
.custom-menu-portal .custom_select__menu {
  @apply p-1 rounded-xl shadow-md border border-border-color/50 bg-white;
}
.custom_select__menu .custom_select__menu-list,
.custom-menu-portal .custom_select__menu-list {
  @apply flex flex-col gap-y-1 max-h-[242px];
}

.custom_select__menu .custom_select__option,
.custom-menu-portal .custom_select__option {
  @apply flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-[13px] font-medium text-gray-800 hover:bg-lighter-200 focus:outline-hidden focus:bg-lighter-200 cursor-pointer;
}

.multiselect-menu-portal .custom_select__option--is-selected,
.custom_select__menu .custom_select__option--is-selected,
.custom_select__menu .custom_select__option--is-selected, 
.custom-menu-portal .custom_select__option--is-selected:active,
.custom-menu-portal .custom_select__option--is-selected {
  @apply bg-primary-500/5 hover:bg-primary-500/5 text-primary-500;
}

.multiselect-menu-portal .custom_select__option--is-focused,
.custom_select__menu .custom_select__option--is-focused,
.custom-menu-portal .custom_select__option--is-focused {
  @apply bg-primary-500/5;
}

.custom_select__menu .custom_select__option--is-disabled,
.custom-menu-portal .custom_select__option--is-disabled {
  @apply text-gray-400 cursor-not-allowed;
}

.custom_select__menu .custom_select__menu-notice,
.custom-menu-portal .custom_select__menu-notice {
  @apply text-gray-400 cursor-not-allowed text-sm;
}

/* WITH CHECKBOX SELECT */
.multiselect-menu-portal .custom_select__menu {
  @apply p-1 rounded-xl shadow-md border border-border-color/50 bg-white;
}

.multiselect-menu-portal .custom_select__menu-list {
  @apply flex flex-col p-0;
}

.multiselect-menu-portal .custom_select__group-heading {
  @apply font-bold text-sm text-dark-500;
  @apply px-2 py-1 mb-1.5 capitalize;
}

.multiselect-menu-portal .custom_select__group {
  @apply py-0 [&:not(:first-child)]:border-t [&:not(:first-child)]:pt-2 [&:not(:first-child)]:mt-2 [&:not(:last-child)]:pb-1 border-border-color;
}

.multiselect-menu-portal .custom_select__option {
  @apply relative px-4 py-2 pr-8 [&:not(:last-child)]:mb-1;
  @apply flex items-center gap-x-3.5 rounded-lg text-sm text-gray-800 hover:bg-lighter-200 focus:outline-hidden focus:bg-lighter-200 cursor-pointer;
  @apply text-sm font-medium;
  @apply hover:bg-lighter-200 focus:outline-hidden focus:bg-lighter-200;
  @apply before:content-['\e91d'] before:font-['icomoon'] before:text-transparent;
  @apply before:absolute before:top-[12px] right-0 before:right-4 before:font-bold;
  @apply before:text-[8px] before:font-bold before:transition-all before:duration-300 before:ease-in-out;
}

.multiselect-menu-portal .custom_select__option--is-selected {
  @apply before:text-primary-500;
}

/* PHONE INPUT */
.phone-input {
  @apply !w-auto;
}

.react-tel-input.phone-input .form-control {
  @apply bg-white rounded-lg border border-border-color;
  @apply py-1.5 px-4 pl-10;
  @apply text-dark-500 text-sm font-medium font-inter-theme;
  @apply focus:outline-none focus:border-gray-100;
  @apply min-h-[40px];
  @apply placeholder:text-dark-500/20;
}

.react-tel-input.phone-input .flag-dropdown {
  @apply bg-transparent border-0;
}

.react-tel-input.phone-input .selected-flag {
  @apply pl-4 hover:bg-transparent;
}

.react-tel-input.phone-input .selected-flag .arrow {
  @apply hidden;
}

@layer utilities {
  .bg-custom-gradient::after {
    background-image: linear-gradient(180deg, #5cb75c 0%, #8e9031 100%);
  }
}

@layer utilities {
  .border-gradient-image {
    border-image: linear-gradient( 90deg, #ef2672 0%, #dd2f7b 34%, #3a7fc9 62%, #129cfb 100% );
    border-image-slice: 1;
  }
}

.search-global {
  @apply bg-white;

  input {
    @apply border-0;
    @apply px-0;
  }
}

.search-result {
  input {
    @apply border-0;
    @apply h-12;
  }
}

/* TOUR GUIDE */
.driver-popover * {
  @apply !font-inter-theme;
}

.driver-popover-custom {
  @apply max-w-sm;
}

.driver-popover-custom .driver-popover-title {
  @apply text-base font-bold mb-1 text-dark-500;
}

.driver-popover-custom .driver-popover-description {
  @apply text-sm text-gray-500 mb-0 font-normal;
}

.driver-popover-custom .driver-popover-footer {
  @apply flex flex-col;
}

.driver-popover-custom .driver-popover-arrow {
  @apply border-8;
}

.driver-popover-custom .driver-popover-navigation-btns {
  @apply !hidden;
  @apply flex justify-between items-center w-full;
}

.driver-popover-custom .driver-popover-progress-text {
  @apply !hidden;
}

.driver-popover-custom .driver-popover-btn-next {
  @apply bg-primary-500 text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors;
}

.driver-popover-custom .driver-popover-btn-skip,
.driver-popover-custom .driver-popover-btn-previous {
  @apply text-gray-600 px-4 py-2 hover:text-gray-800 transition-colors;
}

.driver-popover-custom .driver-popover-close-btn {
  @apply !hidden;
  @apply absolute top-2 right-2 text-gray-400 hover:text-gray-600;
}

.collapsed-false {
  @apply 2xl:max-w-[calc(100%-280px)] ml-[280px];
}

.collapsed-true {
  @apply lg:max-w-[calc(100%-80px)] ml-[80px];
}

/* TOOLTIP */
.react-tooltip {
  @apply z-[1];
}

/* CUSTOM TABLE */
.custom-table {
  @apply !rounded-none;

  .rdt_Table {
    @apply h-[calc(100dvh-280px)] overflow-y-auto border border-border-color border-t-0 rounded-bl-lg rounded-br-lg;
    /* box-shadow: 1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, 0px 1px 0px 0px rgba(204, 204, 204, 0.50) inset, 0px 1px 0px 0px rgba(26, 26, 26, 0.07); */
  }

  .rdt_TableHeadRow {
    @apply min-h-[37px];
  }

  .rdt_TableCol {
    @apply text-xs font-semibold text-gray-500;
  }

  .rdt_TableCell {
    @apply text-sm font-medium;
  }

  .rdt_TableRow {
    @apply hover:bg-gray-50;
  }

  .hHCHrf:nth-of-type(n) {
    @apply bg-gray-50;
  }

  .rdt_TableRow {
    @apply bg-transparent;
  }

  .nested-custom-table {
    .rdt_Table {
      @apply h-auto;
    }
  }

  .__rdt_custom_sort_icon__ svg {
    @apply  !transform-none !h-auto !w-auto;
  }
  .rdt_TableCol_Sortable:hover * {  
    @apply !opacity-90;
  }
}
.border-none-table {
  .rdt_Table {
    @apply border-0;
  }
}
.auto-height-table {
  .rdt_Table {
    @apply h-auto;
  }
}
.configure-table {
  .rdt_Table {
    @apply max-h-[calc(100dvh-275px)] 2xl:max-h-[calc(100dvh-303px)] border-t-1 rounded-lg overflow-y-auto;
  }
}
.upload-columns-table,
.product-selection-table {
  .rdt_Table {
    @apply max-h-[calc(100dvh-365px)] border-t-1 rounded-lg overflow-y-auto;
  }
  .nested-product-variants-table {
    .rdt_Table {
      @apply border-0 rounded-none;
    }
  }
}
.transaction-details-table {
  .rdt_Table {
    @apply max-h-[calc(100dvh-396px)] overflow-y-auto;
  }
}

.asc .icon-caret-up {
  @apply text-primary-500;
}

.desc .icon-caret-down {
  @apply text-primary-500;
}

.custom-table.order-table {
  .rdt_Table {
    @apply h-[calc(100dvh-510px)] lg:h-[calc(100dvh-375px)] 2xl:h-[calc(100dvh-393px)];
  }
}

.order-items-table.custom-table {
  .rdt_Table {
    @apply h-[calc(100dvh-537px)];
  }
}

.variations-table.custom-table {
  .rdt_Table {
    @apply h-auto;
  }
}
.custom-table.ongoing-orders-table {
  .rdt_Table {
    @apply h-auto;
  }
}

/* IMAGE MODAL ANIMATION */
.fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.scaleUp {
  animation: scaleUp 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes scaleUp {
  from {
    transform: scale(0.95);
    opacity: 0;
  }

  to {
    transform: scale(1);
    opacity: 1;
  }
}

.is-playing .play-icon {
  @apply hidden;
}

.is-playing .pause-icon {
  @apply block;
}

.custom-btn-shadow-sm {
  box-shadow: 0px 1px 0px 0px rgba(26, 26, 26, 0.07), 0px 1px 0px 0px rgba(204, 204, 204, 0.5) inset, 0px -1px 0px 0px rgba(0, 0, 0, 0.17) inset, -1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset, 1px 0px 0px 0px rgba(0, 0, 0, 0.13) inset;
}

.loader {
  aspect-ratio: 1;
  animation: l2 1.5s infinite;
  @apply w-7 rounded-full bg-primary-500;
}
@keyframes l2 {
  0%,
  100%{transform:translate(-35px);box-shadow:  0     0 #006CB5, 0     0 #322B6A}
  40% {transform:translate( 35px);box-shadow: -15px  0 #006CB5,-30px  0 #322B6A}
  50% {transform:translate( 35px);box-shadow:  0     0 #006CB5, 0     0 #322B6A}
  90% {transform:translate(-35px);box-shadow:  15px  0 #006CB5, 30px  0 #322B6A}
}
