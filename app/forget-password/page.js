'use client';
import React, { useEffect } from 'react';
import ForgotPassword from '../components/auth/ForgotPassword';
import { useDispatch, useSelector } from 'react-redux';
import OtpVerify from '../components/auth/OtpVerify';
import { resetForgetOtpAction } from '@/store/actions/authActions';
import PublicLayout from '../components/layout/PublicLayout';

const ForgetPassword = () => {
  const [isOtpSent, setIsOtpSent] = React.useState(false);
  const handleOtp = () => {
    setIsOtpSent(!isOtpSent);
  };

  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(resetForgetOtpAction());
  }, []);
  return (
    <PublicLayout>
      {isOtpSent ? (
        <OtpVerify otpType={'forget-password-otp'} handleOtp={handleOtp} />
      ) : (
        <ForgotPassword handleOtp={handleOtp} />
      )}
    </PublicLayout>
  );
};

export default ForgetPassword;
