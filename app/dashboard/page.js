'use client';

import React, { useEffect, useState } from 'react';
import Sidebar from '../components/sidebar/Sidebar';

import DashboardStats from '../components/dashboard-items/DashboardStats';
import RetailActivity from '../components/dashboard-items/RetailActivity';
import QuoteActivity from '../components/dashboard-items/QuoteActivity';
import PrivateLayout from '../components/layout/PrivateLayout';
import OngoingOrdersTable from '../components/table/OngoingOrdersTable';

const Dashboard = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [paginationPerPage, setPaginationPerPage] = useState(5);
  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);
  
  // Table data
  const ongoingOrdersData = [
    {
      id: '#01000',
      shipDate: '2024-03-15',
      orderType: 'Retail',
      paymentStatus: 'Paid',
      shippingMethod: 'Hubsups, Vendor',
      status: 'Processing',
      total: '249',
      quantity: '15',
    },
    {
      id: '#01001',
      shipDate: '2024-03-15',
      orderType: 'Bulk',
      paymentStatus: 'Paid',
      shippingMethod: 'Hubsups',
      status: 'Processing',
      total: '249',
      quantity: '15',
    },
    // ... more orders
  ];

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        {/* Update the sidebar component to pass the state */}
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Dashboard</h1>
            </div>
            <button className="btn btn-gray">Export</button>
          </div>

          <div className="space-y-6 pb-6">
            <DashboardStats />

            <div className="grid grid-cols-1 xl:grid-cols-2 items-start gap-3 mb-3">
              <RetailActivity />
              <QuoteActivity />
            </div>

            <div className="rounded-xl">
              <div className="flex items-center justify-between gap-2.5 bg-white p-4 rounded-tl-xl rounded-tr-xl border border-border-color ">
                <h3 className="text-sm font-semibold">Ongoing Orders</h3>
              </div>
              <OngoingOrdersTable data={ongoingOrdersData} isLoading={isLoading} paginationPerPage={paginationPerPage} />
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Dashboard;
