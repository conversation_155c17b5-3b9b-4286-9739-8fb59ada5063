import StoreProvider from './StoreProvider';
import { ToastContainer } from 'react-toastify';
import NextTopLoader from 'nextjs-toploader';
import Head from 'next/head';
import './assets/css/style.css';
import { getLocale, getMessages } from 'next-intl/server';
import { NextIntlClientProvider } from 'next-intl';

export const metadata = {
  title: 'Hubsups',
  description: 'Hubsups.com Marketplace:',
};

export default async function RootLayout({ children }) {
  const locale = await getLocale();

  const messages = await getMessages();
  return (
    <html lang={locale}>
      <Head>
        <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet"></link>
      </Head>
      <body>
        <StoreProvider>
          {/* <NextTopLoader
            color="#BB3043"
            initialPosition={0.3}
            height={5}
            showSpinner={true}
            easing="ease"
            crawlSpeed={200}
          /> */}
          <ToastContainer />
          <main>
            <NextIntlClientProvider messages={messages}>
              {children}
            </NextIntlClientProvider>
          </main>
        </StoreProvider>
      </body>
    </html>
  );
}
