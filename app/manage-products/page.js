'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import ProductsTab from './components/ProductsTab';
import ValueCombosTab from './components/ValueCombosTab';
import ProductSetsTab from './components/ProductSetsTab';


const ProductManagement = () => {
  const [activeTab, setActiveTab] = useState('products');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Product Management' }
  ];

  const tabs = [
    { id: 'products', label: 'Products', icon: 'icon-package' },
    { id: 'value-combos', label: 'Value Combos', icon: 'icon-layers' },
    { id: 'product-sets', label: 'Product Sets', icon: 'icon-grid-3x3' }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'products':
        return <ProductsTab />;
      case 'value-combos':
        return <ValueCombosTab />;
      case 'product-sets':
        return <ProductSetsTab />;
      default:
        return <ProductsTab />;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex bg-surface-100 rounded-xl w-[calc(100% - 12px)] mt-[60px]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Product Management</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          <div className="bg-white rounded-xl w-[calc(100% - 12px)]">
            {/* Tab Navigation */}
            <div className="border-b border-border-color">
              <div className="flex overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-3 xl:px-6 py-3 xl:py-4 text-sm font-semibold whitespace-nowrap border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-500'
                        : 'border-transparent text-gray-300 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {/* <span className={`${tab.icon} text-base`} /> */}
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-4 xl:p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default ProductManagement;
