'use client';
import React, { useState } from 'react';
import { Formik } from 'formik';
import { PRODUCT_SCHEMA } from '@/utils/schema';
import Link from 'next/link';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import ProductStepper from '@/app/components/create-product-listing/ProductStepper'
import BaseModal from '@/app/components/modals/BaseModal';
import UploadAndMapFields from '@/app/components/create-product-listing/UploadAndMapFields';
import ValidateAndSubmit from '@/app/components/create-product-listing/ValidateAndSubmit';
import UploadResult from '@/app/components/create-product-listing/UploadResult';

const BulkUpload = () => {
  const initialValues = {
    name: '',
    description: '',
    price: '',
    stock: '',
    category: '',
    tags: '',
    images: [],
    status: 'active'
  };
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showSubmittedModal, setShowSubmittedModal] = useState(false);

  const steps = [
    { title: 'Upload and Map Fields', component: <UploadAndMapFields /> },
    { title: 'Validate and Submit', component: <ValidateAndSubmit /> },
    { title: 'Upload Result', component: <UploadResult /> },
    ];

  const handleNext = () => {
    if (!completedSteps.includes(activeStep)) {
      setCompletedSteps([...completedSteps, activeStep]);
    }
    setActiveStep((prev) => Math.min(prev + 1, steps.length - 1));
    if (activeStep === steps.length - 1) {
      setShowSubmittedModal(true);
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => Math.max(prev - 1, 0));
  };

  const handleStepClick = (stepIndex) => {
    if (completedSteps.includes(stepIndex) || stepIndex <= activeStep) {
      setActiveStep(stepIndex);
    }
  };

  const handleSubmit = async (values) => {
  };

  const handleSaveAsDraft = () => {
  };

  return (
    <PrivateLayout>
      <Formik
        initialValues={initialValues}
        validationSchema={PRODUCT_SCHEMA}
        validateOnBlur
        validateOnMount
        onSubmit={handleSubmit}
      >
        {(formik) => (
          <div className="flex bg-white mt-[58px] w-full relative">
            {/* Sidebar */}
            <div className="fixed top-16 transition-base z-50 left-3 h-[calc(100dvh-58px)] max-w-[250px] 2xl:max-w-[352px] p-6 2xl:p-[55] 2xl:pb-5 flex flex-col gap-y-5 2xl:gap-y-9">
              <div className="flex flex-col gap-y-1">
                <h1 className="text-lg 2xl:text-xl font-semibold">Create Product</h1>
                <p className="text-sm text-gray-500">
                  Add detailed product information, set pricing, upload media, and configure inventory to start showcasing your product to potential buyers on Hubsups.
                </p>
              </div>
              <ProductStepper
                steps={steps}
                activeStep={activeStep}
                completedSteps={completedSteps}
                onStepClick={handleStepClick}
              />
              <ul className="flex gap-3 mt-auto text-xs text-gray-500">
                <li className="relative">
                  <Link
                    href=""
                    title="NeeD help?"
                    className="text-xs font-semibold hover:text-primary-500 transition-base"
                  >
                    Need help?
                  </Link>
                </li>
              </ul>
            </div>

            {/* Main content */}
            <div className="flex-1 w-full bg-surface-200 rounded-tl-xl rounded-bl-xl pt-8 2xl:pt-[52px] sm:ml-[250px] 2xl:ml-[352px] overflow-auto h-[calc(100vh-58px)]">
              <div className="flex justify-between items-center mb-4 xl:mb-6 max-w-[1152px] px-6 mx-auto">
                <button 
                  className="btn btn-gray"
                  onClick={() => handleBack()}
                  disabled={activeStep === 0}
                >
                  Cancel
                </button>
                <div className="flex gap-2">
                  {/* <button 
                    className="btn btn-gray"
                    onClick={() => handleSaveAsDraft()}
                  >
                    Save as draft
                  </button> */}
                  <button 
                    className="btn btn-outline-gray"
                    onClick={() => handleBack()}
                    disabled={activeStep === 0}
                  >
                    Back
                  </button>

                  <button 
                    type="button"
                    className={`btn btn-outline-gray ${activeStep !== 2 ? 'hidden' : ''}`}
                  >
                    Download Error Sheet
                  </button>
                  <button 
                    className="btn"
                    onClick={() => handleNext()}
                  >
                    {activeStep === 2 ? 'Upload valid entries' : 'Save and continue'}
                  </button>
                </div>
              </div>

              {/* Step content */}
              <div className='w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto'>
                <div className='max-w-[1152px] mx-auto px-6 pb-8'>
                  {steps[activeStep].component}
                </div>
              </div>
            </div>
          </div>
        )}
      </Formik>


      {/* Product Submitted Successfully */}
      <BaseModal
        isOpen={showSubmittedModal}
        onClose={() => setShowSubmittedModal(false)}
        size="md"
        title="Product import in progress"
      >
        <div className="p-4">
          <p className="text-sm text-dark-500 font-semibold block mb-4 2xl:mb-6">We’re currently importing your products into your store.</p>
          <p className="text-sm text-gray-500">
            This could take some time to complete. If you’d like, you can close this dialog box and continue working.
          </p>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={() => setShowSubmittedModal(false)}
          >
            Close
          </button>
          {/* <button
            type="button"
            className="btn"
            onClick={() => {
              setShowSubmittedModal(false);
              setNewCategoryName('');
            }}
          >
            Return to Product List
          </button> */}
          <button
            type="button"
            className="w-8 h-8 hover:bg-gray-500/20 rounded-lg flex items-center justify-center transition-base"
          >
            <span className="icon icon-arrows-clockwise" />
          </button>
        </div>
      </BaseModal>
    </PrivateLayout>
  );
};

export default BulkUpload;