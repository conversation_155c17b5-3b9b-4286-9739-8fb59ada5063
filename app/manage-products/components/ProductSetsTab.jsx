'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import Image from 'next/image';
import Swal from 'sweetalert2';
import FilterField from '../../components/table/FilterField';
import ProductSetDetailModal from './ProductSetDetailModal';
import AddProductSetModal from './AddProductSetModal';
import CommonPagination from '../../components/table/CommonPagination';
import SortIcon from '../../components/table/SortIcon';

// Product Hover Dropdown Component
const ProductHoverDropdown = ({ product, isVisible, position }) => {
  if (!isVisible || !product) return null;

  return (
    <div
      className="absolute z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4 w-64"
      style={{
        top: position.y + 10,
        left: position.x - 128, // Center the dropdown
        transform: 'translateX(-50%)'
      }}
    >
      <div className="space-y-3">
        <div className="flex items-start gap-3">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="icon icon-package text-gray-400 text-lg" />
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm text-gray-900 truncate">{product.name}</h4>
            <p className="text-xs text-gray-500 mt-1">ID: {product.id}</p>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">Category:</span>
            <span className="text-xs font-medium text-gray-900">{product.category}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-500">Price:</span>
            <span className="text-xs font-medium text-green-600">${product.price}</span>
          </div>
        </div>

        {product.variants && product.variants.length > 0 && (
          <div className="border-t pt-3">
            <p className="text-xs text-gray-500 mb-2">Variants ({product.variants.length}):</p>
            <div className="space-y-1 max-h-20 overflow-y-auto">
              {product.variants.slice(0, 3).map((variant, index) => (
                <div key={index} className="flex justify-between items-center text-xs">
                  <span className="text-gray-600 truncate">{variant.name}</span>
                  <span className="text-gray-500 ml-2">${variant.price}</span>
                </div>
              ))}
              {product.variants.length > 3 && (
                <p className="text-xs text-gray-400 italic">+{product.variants.length - 3} more variants</p>
              )}
            </div>
          </div>
        )}

        {product.stock !== undefined && (
          <div className="border-t pt-2">
            <div className="flex justify-between items-center">
              <span className="text-xs text-gray-500">Stock:</span>
              <span className={`text-xs font-medium ${
                product.stock > 10 ? 'text-green-600' :
                product.stock > 0 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {product.stock > 0 ? `${product.stock} units` : 'Out of stock'}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ProductSetsTab = () => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedSet, setSelectedSet] = useState(null);
  const [isSetDetailOpen, setIsSetDetailOpen] = useState(false);
  const [isAddSetOpen, setIsAddSetOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [activeStatusTab, setActiveStatusTab] = useState('All');
  const displayMenuRef = useRef(null);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);

  // Hover dropdown state
  const [hoveredProduct, setHoveredProduct] = useState(null);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const [isHoverDropdownVisible, setIsHoverDropdownVisible] = useState(false);
  const hoverTimeoutRef = useRef(null);

  // Sample data for product sets
  const [productSets, setProductSets] = useState([
    {
      id: 'PS-001',
      setName: 'Complete Bedroom Set',
      setCode: 'CBS-001',
      Vendor: 'Comfort Living',
      setType: 'Related Products',
      status: 'Published',
      totalProducts: 5,
      displayType: 'Frequently Bought Together',
      products: [
        { id: 'PRD-022', name: 'Queen Size Bed Frame', includesImage: '/images/product-1.jpg', category: 'Furniture', price: 399.99 },
        { id: 'PRD-023', name: 'Memory Foam Mattress', includesImage: '/images/product-1.jpg', category: 'Furniture', price: 299.99 },
        { id: 'PRD-024', name: 'Bedside Table Set', includesImage: '/images/product-1.jpg', category: 'Furniture', price: 149.99 },
        { id: 'PRD-025', name: 'Table Lamp', includesImage: '/images/product-1.jpg', category: 'Lighting', price: 59.99 },
        { id: 'PRD-026', name: 'Bed Sheet Set', includesImage: '/images/product-1.jpg', category: 'Bedding', price: 79.99 }
      ],
      tags: ['bedroom', 'furniture', 'complete-set'],
      thumbnail: '/images/product-1.jpg',
      shortDescription: 'Everything you need for a complete bedroom setup',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-20T14:20:00Z'
    },
    {
      id: 'PS-002',
      setName: 'Smart Home Starter Kit',
      setCode: 'SHSK-002',
      Vendor: 'Tech Living',
      setType: 'Cross-sell',
      status: 'Published',
      totalProducts: 6,
      displayType: 'You May Also Like',
      products: [
        { id: 'PRD-027', name: 'Smart Hub', category: 'Electronics', price: 99.99 },
        { id: 'PRD-028', name: 'Smart Light Bulbs (4-pack)', category: 'Lighting', price: 49.99 },
        { id: 'PRD-029', name: 'Smart Thermostat', category: 'Electronics', price: 199.99 },
        { id: 'PRD-030', name: 'Smart Door Lock', category: 'Security', price: 149.99 },
        { id: 'PRD-031', name: 'Smart Security Camera', category: 'Security', price: 89.99 },
        { id: 'PRD-032', name: 'Smart Plug (2-pack)', category: 'Electronics', price: 24.99 }
      ],
      tags: ['smart-home', 'electronics', 'automation'],
      thumbnail: '/images/product-2.jpg',
      shortDescription: 'Start your smart home journey with these essentials',
      createdAt: '2024-01-12T11:00:00Z',
      updatedAt: '2024-01-19T13:30:00Z'
    },
    {
      id: 'PS-003',
      setName: 'Fitness Enthusiast Collection',
      setCode: 'FEC-003',
      Vendor: 'FitLife',
      setType: 'Upsell',
      status: 'Draft',
      totalProducts: 4,
      displayType: 'Complete Your Workout',
      products: [
        { id: 'PRD-033', name: 'Adjustable Dumbbells', category: 'Fitness', price: 199.99 },
        { id: 'PRD-034', name: 'Yoga Mat Premium', category: 'Fitness', price: 49.99 },
        { id: 'PRD-035', name: 'Resistance Bands Set', category: 'Fitness', price: 29.99 },
        { id: 'PRD-036', name: 'Protein Shaker Bottle', category: 'Accessories', price: 14.99 }
      ],
      tags: ['fitness', 'workout', 'health'],
      thumbnail: '/images/product-3.jpg',
      shortDescription: 'Complete fitness equipment for home workouts',
      createdAt: '2024-01-14T14:20:00Z',
      updatedAt: '2024-01-21T10:15:00Z'
    },
    {
      id: 'PS-004',
      setName: 'Professional Photography Kit',
      setCode: 'PPK-004',
      Vendor: 'ProShot',
      setType: 'Accessory Bundle',
      status: 'Pending Approval',
      totalProducts: 7,
      displayType: 'Professional Essentials',
      products: [
        { id: 'PRD-037', name: 'DSLR Camera Body', category: 'Photography', price: 899.99 },
        { id: 'PRD-038', name: '50mm Prime Lens', category: 'Photography', price: 299.99 },
        { id: 'PRD-039', name: 'Camera Tripod', category: 'Photography', price: 149.99 },
        { id: 'PRD-040', name: 'Camera Bag', category: 'Accessories', price: 79.99 },
        { id: 'PRD-041', name: 'Memory Card 64GB', category: 'Storage', price: 39.99 },
        { id: 'PRD-042', name: 'Extra Battery', category: 'Accessories', price: 49.99 },
        { id: 'PRD-043', name: 'Lens Cleaning Kit', category: 'Accessories', price: 19.99 }
      ],
      tags: ['photography', 'professional', 'camera'],
      thumbnail: '/images/product-1.jpg',
      shortDescription: 'Professional photography equipment bundle',
      createdAt: '2024-01-16T09:45:00Z',
      updatedAt: '2024-01-22T15:30:00Z'
    },
    {
      id: 'PS-005',
      setName: 'Outdoor Adventure Gear',
      setCode: 'OAG-005',
      Vendor: 'Adventure Pro',
      setType: 'Seasonal Collection',
      status: 'Archive',
      totalProducts: 5,
      displayType: 'Adventure Ready',
      products: [
        { id: 'PRD-044', name: 'Hiking Backpack', category: 'Outdoor', price: 129.99 },
        { id: 'PRD-045', name: 'Camping Tent 2-Person', category: 'Outdoor', price: 199.99 },
        { id: 'PRD-046', name: 'Sleeping Bag', category: 'Outdoor', price: 89.99 },
        { id: 'PRD-047', name: 'Portable Water Filter', category: 'Outdoor', price: 49.99 },
        { id: 'PRD-048', name: 'LED Headlamp', category: 'Outdoor', price: 29.99 }
      ],
      tags: ['outdoor', 'camping', 'adventure'],
      thumbnail: '/images/product-2.jpg',
      shortDescription: 'Essential gear for outdoor adventures',
      createdAt: '2024-01-08T16:45:00Z',
      updatedAt: '2024-01-16T12:00:00Z'
    }
  ]);

  const statusTabs = [
    { id: 'All', label: 'All Sets', count: productSets.length },
    { id: 'Published', label: 'Published', count: productSets.filter(s => s.status === 'Published').length },
    { id: 'Draft', label: 'Draft', count: productSets.filter(s => s.status === 'Draft').length },
    { id: 'Pending Approval', label: 'Pending Approval', count: productSets.filter(s => s.status === 'Pending Approval').length },
    { id: 'Archive', label: 'Archive', count: productSets.filter(s => s.status === 'Archive').length }
  ];

  const filteredSets = productSets.filter(set => {
    const matchesSearch = set.setName.toLowerCase().includes(filterText.toLowerCase()) ||
                         set.setCode.toLowerCase().includes(filterText.toLowerCase());
    const matchesStatus = activeStatusTab === 'All' || set.status === activeStatusTab;
    return matchesSearch && matchesStatus;
  });

  const columns = [
    {
      name: 'Set Details',
      selector: row => row.setName,
      sortable: true,
      width: '250px',
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            {/* <span className="icon icon-grid-3x3 text-gray-400" /> */}
            <Image
              src={row.thumbnail}
              alt={row.setName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.setName}</div>
            <div className="text-xs text-gray-500">{row.setCode}</div>
          </div>
        </div>
      ),
    },
    {
      name: 'Vendor',
      selector: row => row.Vendor,
      sortable: true,
      // width: '120px',
    },
    {
      name: 'Type',
      selector: row => row.setType,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full text-nowrap ${
          row.setType === 'Related Products' ? 'bg-info-500/20 text-info-500' :
          row.setType === 'Cross-sell' ? 'bg-purple-100 text-purple-800' :
          row.setType === 'Upsell' ? 'bg-green-100 text-green-800' :
          row.setType === 'Accessory Bundle' ? 'bg-orange-100 text-orange-800' :
          'bg-gray-100 text-gray-800'
        }`}>
          {row.setType}
        </span>
      ),
      width: '160px',
    },
    {
      name: 'Display Type',
      selector: row => row.displayType,
      sortable: true,
      cell: (row) => (
        <span className="text-sm text-gray-400">
          {row.displayType}
        </span>
      ),
      // width: '180px',
    },
    {
      name: 'Products',
      selector: row => row.totalProducts,
      sortable: true,
      cell: (row) => (
        <span className="text-sm font-medium">
          {row.totalProducts} items
        </span>
      ),
      // width: '100px',
    },
    {
      name: 'Categories',
      selector: row => row.products,
      cell: (row) => {
        const categories = [...new Set(row.products.map(p => p.category))];
        return (
          <div className="flex flex-wrap gap-1">
            {categories.slice(0, 2).map((category, index) => (
              <span
                key={index}
                className="inline-flex px-1.5 py-0.5 text-xs font-medium bg-gray-100/50 text-gray-700 rounded-md"
              >
                {category}
              </span>
            ))}
            {categories.length > 2 && (
              <span className="text-xs text-gray-500 block w-full">
                +{categories.length - 2} more
              </span>
            )}
          </div>
        );
      },
      // width: '150px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full text-nowrap ${
          row.status === 'Published' ? 'bg-green-100 text-green-800' :
          row.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
          row.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
          'bg-red-100 text-red-800'
        }`}>
          {row.status}
        </span>
      ),
      width: '140px',
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewSet(row)}
            data-tooltip-id="view-set-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleEditSet(row)}
            data-tooltip-id="edit-set-tooltip"
            data-tooltip-content="Edit Set"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            onClick={() => handleCloneSet(row)}
            data-tooltip-id="clone-set-tooltip"
            data-tooltip-content="Clone Set"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-info-500/10 hover:text-info-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-copy-simple text-base" />
          </button>
          <button
            onClick={() => handleDeleteSet(row.id)}
            data-tooltip-id="delete-set-tooltip"
            data-tooltip-content="Delete Set"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-set-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-set-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="clone-set-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-set-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      width: '160px',
    },
  ];

  const handleViewSet = (set) => {
    setSelectedSet(set);
    setIsSetDetailOpen(true);
  };

  const handleEditSet = (set) => {
    console.log('Edit set:', set);
    // Implement edit functionality
  };

  const handleCloneSet = (set) => {
    const clonedSet = {
      ...set,
      id: `PS-${Date.now()}`,
      setName: `${set.setName} (Copy)`,
      setCode: `${set.setCode}-COPY`,
      status: 'Draft'
    };
    setProductSets(prev => [...prev, clonedSet]);
    console.log('Set cloned:', clonedSet);
  };

  const handleDeleteSet = async (setId) => {
    const productSet = productSets.find(s => s.id === setId);

    const result = await Swal.fire({
      title: 'Delete Product Set',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-gray-600">Are you sure you want to delete this product set? This action cannot be undone.</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="flex items-center gap-3 mb-2">
              <img src="${productSet?.thumbnail || '/images/default-product.jpg'}" alt="${productSet?.setName || 'Product Set'}" class="w-10 h-10 rounded-lg object-cover" />
              <div>
                <p class="text-sm font-medium text-gray-900">${productSet?.setName || 'Unknown Set'}</p>
                <p class="text-xs text-gray-500">${productSet?.setCode || 'N/A'}</p>
              </div>
            </div>
            <div class="text-xs text-gray-500">
              <p>Vendor: ${productSet?.Vendor || 'N/A'}</p>
              <p>Products: ${productSet?.totalProducts || 0} items</p>
              <p>Status: ${productSet?.status || 'N/A'}</p>
            </div>
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#dc2626',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, Delete Set',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      setProductSets(prev => prev.filter(s => s.id !== setId));

      // Show success message
      Swal.fire({
        title: 'Deleted!',
        text: 'Product set has been deleted successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });

      console.log('Set deleted:', setId);
    }
  };

  const handleAddSet = (setData) => {
    const newSet = {
      ...setData,
      id: `PS-${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setProductSets(prev => [...prev, newSet]);
    console.log('Set added:', newSet);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Product Sets</h2>
          <span className="text-sm text-gray-300">
            {filteredSets.length} of {productSets.length} sets
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-bold text-lg" />
            Export
          </button>
          <button
            onClick={() => setIsAddSetOpen(true)}
            className="btn btn-primary inline-flex items-center gap-2"
          >
            <span className="icon icon-plus font-bold text-lg" />
            Create Product Set
          </button>
        </div>
      </div>

      {/* Filters and Table */}
      <div className="rounded-xl">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
          placeholder="Search product sets..."
          statusTabs={statusTabs}
          activeStatusTab={activeStatusTab}
          setActiveStatusTab={setActiveStatusTab}
        />

        <DataTable
          columns={columns}
          data={filteredSets}
          pagination
          paginationPerPage={10}
          selectableRows
          fixedHeader={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          className="custom-table auto-height-table"
          noDataComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <span className="icon icon-grid-3x3 text-4xl text-gray-300 mb-4" />
              <p className="text-gray-500 text-lg font-medium">No product sets found</p>
              <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
            </div>
          }
          selectableRowsComponent={CustomCheckbox}
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <CommonPagination
              selectedCount={props.selectedRows?.length}
              total={props.totalRows}
              page={props.currentPage}
              perPage={props.rowsPerPage}
              onPageChange={props.onChangePage}
            />
          )}
        />
      </div>

      {/* Modals */}
      <ProductSetDetailModal
        isOpen={isSetDetailOpen}
        onClose={() => setIsSetDetailOpen(false)}
        productSet={selectedSet}
      />

      <AddProductSetModal
        isOpen={isAddSetOpen}
        onClose={() => setIsAddSetOpen(false)}
        onAddSet={handleAddSet}
      />
    </div>
  );
};

export default ProductSetsTab;
