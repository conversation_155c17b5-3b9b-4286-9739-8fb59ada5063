'use client';

import React, { useState } from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AddProductSetModal = ({ isOpen, onClose, onAddSet }) => {
  const [formData, setFormData] = useState({
    setName: '',
    setCode: '',
    setType: 'Related Products',
    displayType: 'Frequently Bought Together',
    status: 'Draft',
    shortDescription: '',
    tags: [],
    products: [],
    displayRules: {
      showOnProductPages: true,
      showInCart: true,
      showAtCheckout: false,
      showOnHomepage: false,
      mobileDisplay: true
    },
    targeting: {
      customerSegments: 'All customers',
      geographicTargeting: 'All regions',
      seasonalDisplay: 'Year-round'
    },
    thumbnail: null
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: type === 'checkbox' ? checked : value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleTagAdd = (tag) => {
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }));
    }
  };

  const handleTagRemove = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    const setData = {
      ...formData,
      totalProducts: formData.products.length
    };

    onAddSet(setData);
    onClose();
    
    // Reset form
    setFormData({
      setName: '',
      setCode: '',
      setType: 'Related Products',
      displayType: 'Frequently Bought Together',
      status: 'Draft',
      shortDescription: '',
      tags: [],
      products: [],
      displayRules: {
        showOnProductPages: true,
        showInCart: true,
        showAtCheckout: false,
        showOnHomepage: false,
        mobileDisplay: true
      },
      targeting: {
        customerSegments: 'All customers',
        geographicTargeting: 'All regions',
        seasonalDisplay: 'Year-round'
      },
      thumbnail: null
    });
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Create Product Set"
      size="lg"
    >
      <form onSubmit={handleSubmit} className="h-[calc(100dvh_-_117px)] overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Set Name *
                </label>
                <input
                  type="text"
                  name="setName"
                  value={formData.setName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Set Code *
                </label>
                <input
                  type="text"
                  name="setCode"
                  value={formData.setCode}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Set Type *
                </label>
                <select
                  name="setType"
                  value={formData.setType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="Related Products">Related Products</option>
                  <option value="Cross-sell">Cross-sell</option>
                  <option value="Upsell">Upsell</option>
                  <option value="Accessory Bundle">Accessory Bundle</option>
                  <option value="Seasonal Collection">Seasonal Collection</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Display Type *
                </label>
                <select
                  name="displayType"
                  value={formData.displayType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="Frequently Bought Together">Frequently Bought Together</option>
                  <option value="You May Also Like">You May Also Like</option>
                  <option value="Complete Your Workout">Complete Your Workout</option>
                  <option value="Professional Essentials">Professional Essentials</option>
                  <option value="Adventure Ready">Adventure Ready</option>
                  <option value="Custom Display Text">Custom Display Text</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="Draft">Draft</option>
                  <option value="Published">Published</option>
                  <option value="Pending Approval">Pending Approval</option>
                </select>
              </div>
              <div></div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Short Description
              </label>
              <textarea
                name="shortDescription"
                value={formData.shortDescription}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief description of the product set"
              />
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <div className="flex flex-wrap gap-2 mb-2">
                {formData.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-info-500/20 text-info-500 rounded-full"
                  >
                    {tag}
                    <button
                      type="button"
                      onClick={() => handleTagRemove(tag)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
              <input
                type="text"
                placeholder="Add tag and press Enter"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleTagAdd(e.target.value.trim());
                    e.target.value = '';
                  }
                }}
              />
            </div>
          </div>

          {/* Product Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Selection</h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <span className="icon icon-grid-3x3 text-3xl text-gray-400 mb-4 block" />
              <p className="text-gray-500 mb-2">Add products to this set</p>
              <p className="text-sm text-gray-400 mb-4">
                Search and select products to include in this product set
              </p>
              <button
                type="button"
                className="btn btn-outline-primary"
                onClick={() => {
                  // This would open a product selection modal
                  alert('Product selection modal would open here');
                }}
              >
                <span className="icon icon-plus mr-2" />
                Add Products
              </button>
            </div>
            
            {formData.products.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="font-medium text-gray-900">Selected Products:</h4>
                {formData.products.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="icon icon-package text-gray-400" />
                      <div>
                        <div className="font-medium text-sm">{product.name}</div>
                        <div className="text-xs text-gray-500">{product.category}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">${product.price}</span>
                      <button
                        type="button"
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            products: prev.products.filter((_, i) => i !== index)
                          }));
                        }}
                        className="text-red-600 hover:text-red-800"
                      >
                        <span className="icon icon-trash text-sm" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Display Rules */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Display Rules</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Show on Product Pages</label>
                <input
                  type="checkbox"
                  name="displayRules.showOnProductPages"
                  checked={formData.displayRules.showOnProductPages}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Show in Cart</label>
                <input
                  type="checkbox"
                  name="displayRules.showInCart"
                  checked={formData.displayRules.showInCart}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Show at Checkout</label>
                <input
                  type="checkbox"
                  name="displayRules.showAtCheckout"
                  checked={formData.displayRules.showAtCheckout}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Show on Homepage</label>
                <input
                  type="checkbox"
                  name="displayRules.showOnHomepage"
                  checked={formData.displayRules.showOnHomepage}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-gray-700">Mobile Display</label>
                <input
                  type="checkbox"
                  name="displayRules.mobileDisplay"
                  checked={formData.displayRules.mobileDisplay}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>

          {/* Targeting */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Targeting & Personalization</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer Segments
                </label>
                <select
                  name="targeting.customerSegments"
                  value={formData.targeting.customerSegments}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="All customers">All customers</option>
                  <option value="New customers">New customers</option>
                  <option value="Returning customers">Returning customers</option>
                  <option value="VIP customers">VIP customers</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Geographic Targeting
                </label>
                <select
                  name="targeting.geographicTargeting"
                  value={formData.targeting.geographicTargeting}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="All regions">All regions</option>
                  <option value="North America">North America</option>
                  <option value="Europe">Europe</option>
                  <option value="Asia Pacific">Asia Pacific</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Seasonal Display
              </label>
              <select
                name="targeting.seasonalDisplay"
                value={formData.targeting.seasonalDisplay}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="Year-round">Year-round</option>
                <option value="Spring">Spring</option>
                <option value="Summer">Summer</option>
                <option value="Fall">Fall</option>
                <option value="Winter">Winter</option>
                <option value="Holiday Season">Holiday Season</option>
              </select>
            </div>
          </div>
        </div>
      </form>

      {/* Action Buttons */}
      <div className="flex justify-between border-t border-border-color p-4">
        <button
          type="button"
          onClick={onClose}
          className="btn btn-outline-gray"
        >
          Cancel
        </button>
        <div className="flex gap-3">
          <button
            type="button"
            onClick={() => {
              const draftData = { ...formData, status: 'Draft' };
              // Handle draft save
              console.log('Save as draft:', draftData);
            }}
            className="btn btn-gray"
          >
            Save as Draft
          </button>
          <button
            type="submit"
            form="set-form"
            className="btn btn-primary"
            onClick={handleSubmit}
          >
            Create Set
          </button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

export default AddProductSetModal;
