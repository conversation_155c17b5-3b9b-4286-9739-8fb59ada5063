'use client';

import React, { useState } from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const AddValueComboModal = ({ isOpen, onClose, onAddCombo }) => {
  const [formData, setFormData] = useState({
    comboName: '',
    comboCode: '',
    comboType: 'Fixed Bundle',
    status: 'Draft',
    shortDescription: '',
    tags: [],
    products: [],
    discountType: 'percentage',
    discountValue: '',
    thumbnail: null
  });

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Calculate pricing
    const originalPrice = formData.products.reduce((sum, product) => sum + (product.price * product.quantity), 0);
    const discountAmount = formData.discountType === 'percentage' 
      ? (originalPrice * parseFloat(formData.discountValue)) / 100
      : parseFloat(formData.discountValue);
    const comboPrice = originalPrice - discountAmount;
    const discount = ((discountAmount / originalPrice) * 100).toFixed(1);

    const comboData = {
      ...formData,
      totalProducts: formData.products.length,
      originalPrice,
      comboPrice,
      discount: parseFloat(discount),
      inventory: 0 // Default inventory
    };

    onAddCombo(comboData);
    onClose();
    
    // Reset form
    setFormData({
      comboName: '',
      comboCode: '',
      comboType: 'Fixed Bundle',
      status: 'Draft',
      shortDescription: '',
      tags: [],
      products: [],
      discountType: 'percentage',
      discountValue: '',
      thumbnail: null
    });
  };

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title="Create Value Combo"
      size="lg"
    >
      <form onSubmit={handleSubmit} className="h-[calc(100dvh_-_117px)] overflow-y-auto p-6">
        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Combo Name *
                </label>
                <input
                  type="text"
                  name="comboName"
                  value={formData.comboName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Combo Code *
                </label>
                <input
                  type="text"
                  name="comboCode"
                  value={formData.comboCode}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Combo Type *
                </label>
                <select
                  name="comboType"
                  value={formData.comboType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  required
                >
                  <option value="Fixed Bundle">Fixed Bundle</option>
                  <option value="Mix & Match">Mix & Match</option>
                  <option value="Variable Bundle">Variable Bundle</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="Draft">Draft</option>
                  <option value="Published">Published</option>
                  <option value="Pending Approval">Pending Approval</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Short Description
              </label>
              <textarea
                name="shortDescription"
                value={formData.shortDescription}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief description of the value combo"
              />
            </div>
          </div>

          {/* Product Selection */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Selection</h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <span className="icon icon-package text-3xl text-gray-400 mb-4 block" />
              <p className="text-gray-500 mb-2">Add products to this combo</p>
              <p className="text-sm text-gray-400 mb-4">
                Search and select products to include in this value combo
              </p>
              <button
                type="button"
                className="btn btn-outline-primary"
                onClick={() => {
                  // This would open a product selection modal
                  alert('Product selection modal would open here');
                }}
              >
                <span className="icon icon-plus mr-2" />
                Add Products
              </button>
            </div>
            
            {formData.products.length > 0 && (
              <div className="mt-4 space-y-2">
                <h4 className="font-medium text-gray-900">Selected Products:</h4>
                {formData.products.map((product, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="icon icon-package text-gray-400" />
                      <div>
                        <div className="font-medium text-sm">{product.name}</div>
                        <div className="text-xs text-gray-500">Qty: {product.quantity}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium">${product.price}</span>
                      <button
                        type="button"
                        onClick={() => {
                          setFormData(prev => ({
                            ...prev,
                            products: prev.products.filter((_, i) => i !== index)
                          }));
                        }}
                        className="text-red-600 hover:text-red-800"
                      >
                        <span className="icon icon-trash text-sm" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Pricing Configuration */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Pricing Configuration</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Discount Type
                </label>
                <select
                  name="discountType"
                  value={formData.discountType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="percentage">Percentage (%)</option>
                  <option value="fixed">Fixed Amount ($)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Discount Value *
                </label>
                <input
                  type="number"
                  name="discountValue"
                  value={formData.discountValue}
                  onChange={handleInputChange}
                  step="0.01"
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder={formData.discountType === 'percentage' ? 'Enter percentage' : 'Enter amount'}
                  required
                />
              </div>
            </div>
          </div>

          {/* Combo Image */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Combo Image</h3>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <span className="icon icon-image text-2xl text-gray-400 mb-2 block" />
              <p className="text-sm text-gray-500">
                Click to upload or drag and drop
              </p>
              <p className="text-xs text-gray-400">
                PNG, JPG up to 2MB (16:9 ratio recommended)
              </p>
              <input
                type="file"
                name="thumbnail"
                accept="image/*"
                className="hidden"
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>
      </form>

      {/* Action Buttons */}
      <div className="flex justify-between border-t border-border-color p-4">
        <button
          type="button"
          onClick={onClose}
          className="btn btn-outline-gray"
        >
          Cancel
        </button>
        <div className="flex gap-3">
          <button
            type="button"
            onClick={() => {
              const draftData = { ...formData, status: 'Draft' };
              // Handle draft save
              console.log('Save as draft:', draftData);
            }}
            className="btn btn-gray"
          >
            Save as Draft
          </button>
          <button
            type="submit"
            form="combo-form"
            className="btn btn-primary"
            onClick={handleSubmit}
          >
            Create Combo
          </button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

export default AddValueComboModal;
