'use client';

import React from 'react';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';

const InfoRow = ({ label, value }) => (
  <div className="flex flex-col gap-1">
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm font-medium">{value || 'N/A'}</span>
  </div>
);

const ProductDetailModal = ({ isOpen, onClose, product }) => {
  if (!product) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Product Details - ${product.productCode}`}
      size="md"
    >
      <div className="space-y-6 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* General Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">General Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Product Name" value={product.productName} />
            <InfoRow label="Product Code/SKU" value={product.productCode} />
            <InfoRow label="Product Type" value={product.productType} />
            <InfoRow label="Category" value={product.category?.name} />
            <InfoRow label="Brand" value={product.brand?.name} />
            <InfoRow label="Status" value={
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                product.status === 'Published' ? 'bg-green-100 text-green-800' :
                product.status === 'Draft' ? 'bg-gray-100 text-gray-800' :
                product.status === 'Pending Approval' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {product.status}
              </span>
            } />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Product Description */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Description</h4>
          <div className="space-y-3">
            <InfoRow label="Short Description" value={product.shortDescription} />
            {product.tags && product.tags.length > 0 && (
              <div className="flex flex-col gap-1">
                <span className="text-sm text-gray-500/60 font-normal">Product Tags</span>
                <div className="flex flex-wrap gap-2">
                  {product.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex px-2 py-1 text-xs font-medium bg-info-500/20 text-info-500 rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Pricing Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Pricing & Inventory</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="MRP/Original Price" value={`$${product.mrp}`} />
            <InfoRow label="Selling Price" value={`$${product.sellingPrice}`} />
            <InfoRow label="Cost Price" value={`$${product.costPrice}`} />
            <InfoRow label="Inventory Quantity" value={`${product.inventory} units`} />
            <InfoRow label="Minimum Order Quantity" value={`${product.minOrderQty} unit(s)`} />
            <InfoRow 
              label="Inventory Status" 
              value={
                <span className={`font-medium ${
                  product.inventory < 10 ? 'text-red-600' : 
                  product.inventory < 50 ? 'text-yellow-600' : 
                  'text-green-600'
                }`}>
                  {product.inventory < 10 ? 'Low Stock' : 
                   product.inventory < 50 ? 'Medium Stock' : 
                   'In Stock'}
                </span>
              } 
            />
          </div>
        </div>

        <hr className="border-gray-200" />

        {/* Variants Information */}
        {product.productType === 'Variant' && (
          <>
            <div>
              <h4 className="font-semibold mb-4 text-gray-900">Variants</h4>
              <div className="grid grid-cols-2 gap-4">
                <InfoRow label="Total Variants" value={`${product.variants} variants`} />
                <InfoRow label="Variant Type" value="Color + Size" />
              </div>
              
              {/* Sample variant display */}
              <div className="mt-4">
                <span className="text-sm text-gray-500/60 font-normal mb-2 block">Available Variants</span>
                <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                  {Array.from({ length: Math.min(product.variants, 6) }, (_, i) => (
                    <div key={i} className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm">
                        {i % 2 === 0 ? 'Red - XL' : 'Blue - M'}
                      </span>
                      <span className="text-sm font-medium">${product.sellingPrice}</span>
                    </div>
                  ))}
                  {product.variants > 6 && (
                    <div className="text-center text-sm text-gray-500 py-2">
                      +{product.variants - 6} more variants
                    </div>
                  )}
                </div>
              </div>
            </div>
            <hr className="border-gray-200" />
          </>
        )}

        {/* Bundle Information */}
        {product.productType === 'Bundle' && (
          <>
            <div>
              <h4 className="font-semibold mb-4 text-gray-900">Bundle Information</h4>
              <div className="space-y-3">
                <InfoRow label="Bundle Type" value="Product Bundle" />
                <div className="flex flex-col gap-1">
                  <span className="text-sm text-gray-500/60 font-normal">Included Products</span>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm">Ergonomic Office Chair</span>
                      <span className="text-sm text-gray-500">1x</span>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm">Lumbar Support Cushion</span>
                      <span className="text-sm text-gray-500">1x</span>
                    </div>
                    <div className="flex justify-between items-center p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm">Armrest Pads</span>
                      <span className="text-sm text-gray-500">2x</span>
                    </div>
                  </div>
                </div>
                <InfoRow label="Bundle Discount" value="12.5% off individual prices" />
              </div>
            </div>
            <hr className="border-gray-200" />
          </>
        )}

        {/* Meta Information */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Meta Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow 
              label="Created Date" 
              value={new Date(product.createdAt).toLocaleDateString()} 
            />
            <InfoRow 
              label="Last Updated" 
              value={new Date(product.updatedAt).toLocaleDateString()} 
            />
            <InfoRow label="SEO Title" value={product.productName} />
            <InfoRow label="URL Slug" value={product.productCode.toLowerCase()} />
          </div>
        </div>

        {/* Shipping & Logistics */}
        <div>
          <h4 className="font-semibold mb-4 text-gray-900">Shipping & Logistics</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Weight" value="0.5 kg" />
            <InfoRow label="Dimensions" value="25 x 20 x 5 cm" />
            <InfoRow label="Delivery Time" value="2-5 business days" />
            <InfoRow label="Returnable" value="Yes (30 days)" />
            <InfoRow label="Replaceable" value="Yes" />
            <InfoRow label="Warranty" value="1 year manufacturer warranty" />
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between gap-3 border-t border-border-color p-4">
        <button
          onClick={onClose}
          className="btn btn-outline-gray"
        >
          Close
        </button>
        <div className="flex gap-3">
          <button className="btn btn-gray">
            Preview on Storefront
          </button>
          <button className="btn btn-primary">
            Edit Product
          </button>
        </div>
      </div>
    </BaseOffCanvas>
  );
};

export default ProductDetailModal;
