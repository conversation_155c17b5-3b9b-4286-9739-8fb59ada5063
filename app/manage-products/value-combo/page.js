'use client';
import React, { useEffect, useRef, useState } from 'react';
import Link from 'next/link';
import ProductStepper from '../../components/create-product-listing/ProductStepper';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import BaseModal from '../../components/modals/BaseModal';
import ComboGeneralInformation from '../../components/create-product-listing/ComboGeneralInformation';
import ComboBuilder from '../../components/create-product-listing/ComboBuilder';
import { useDispatch, useSelector } from 'react-redux';
import {
  addProdCombosGenInfo,
  getProdComboDetails,
  updateProdComboDetails,
} from '@/store/api/productApi';
import { resetProductComboState } from '@/store/actions/userActions';
import { useRouter } from 'next/navigation';
import { showErrorToast } from '@/utils/function';

const ValueCombo = () => {
  const router = useRouter();
  const dispatch = useDispatch();

  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [showSubmittedModal, setShowSubmittedModal] = useState(false);
  const [formData, setFormData] = useState({});
  const [valueComboData, setvalueComboData] = useState([]);
  const [saveAndDraft, setSaveAndDraft] = useState(false);
  const [showPopup, setShowPopup] = useState(false);

  const generalInfoRef = useRef();
  const comboBuilderRef = useRef();
  const allowNavRef = useRef(false);
  const nextUrlRef = useRef(null);
  const currentUrlRef = useRef(null);

  const { productComboId } = useSelector((state) => state.product);
  const fetchProductCombosData = async () => {
    const { payload } = await dispatch(
      getProdComboDetails(productComboId || productId)
    );
    setvalueComboData(payload?.data);
    setCompletedSteps(payload?.data?.steps.map((e) => e - 1));
    setActiveStep(payload?.data?.steps.includes(1) == true ? 1 : 0);
  };

  useEffect(() => {
    // dispatch(resetProductComboState())
    if (productComboId) {
      fetchProductCombosData();
    }
  }, [productComboId]);

  useEffect(() => {
    // Set the current URL when component mounts
    currentUrlRef.current = window.location.pathname + window.location.search;

    // Push a dummy state to trap browser back button
    history.pushState(null, '', currentUrlRef.current);

    const handlePopState = () => {
      if (!allowNavRef.current) {
        nextUrlRef.current = document.referrer || '/';
        setShowPopup(true);

        // Push current URL again to prevent navigation
        history.pushState(null, '', currentUrlRef.current);
      }
    };

    const originalPush = router.push;
    const customPush = (url, options) => {
      if (!allowNavRef.current) {
        nextUrlRef.current = url;
        setShowPopup(true);
        return;
      }
      return originalPush(url, options);
    };

    // Override router.push with custom behavior
    router.push = customPush;

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
      router.push = originalPush; // Restore original push method
    };
  }, [router]);

  const handleSaveAsDraft = () => {
    setSaveAndDraft(true);
    if (activeStep === 0) {
      generalInfoRef.current?.submitForm();
    } else if (activeStep === 1) {
      comboBuilderRef.current?.submitForm();
    }
  };

  const handleStepSuccess = async (data) => {
    let payload;

    if (saveAndDraft) {
      if (activeStep === 0) {
        const response = await dispatch(addProdCombosGenInfo({ ...data }));
        payload = response?.payload;
      } else if (activeStep === 1) {
        if (data?.products?.length > 1) {
          const response = await dispatch(
            updateProdComboDetails({
              ...data,
              send_for_approval: false, // 0 or false, depending on API spec
              product_combo_id: productComboId,
            })
          );
          payload = response?.payload;
        } else {
          showErrorToast('Please add at least two products to create a combo');
        }
      }

      setSaveAndDraft(false); // ✅ Reset flag after handling
      return;
    }

    // Normal Save and Next (approval: true)
    if (activeStep === 0) {
      const response = await dispatch(addProdCombosGenInfo({ ...data }));
      payload = response?.payload;
    } else if (activeStep === 1) {
      if (data?.products?.length > 1) {
        const response = await dispatch(
          updateProdComboDetails({
            ...data,
            send_for_approval: true, // 1 or true
            product_combo_id: productComboId,
          })
        );
        payload = response?.payload;
      } else {
        showErrorToast('Please add at least two products to create a combo');
      }
    }

    if (payload?.status) {
      if (!completedSteps.includes(activeStep)) {
        setCompletedSteps((prev) => [...prev, activeStep]);
      }

      if (activeStep === steps.length - 1) {
        setShowSubmittedModal(true);
      } else {
        setActiveStep((prev) => prev + 1);
      }
    }
  };

  const handleNextClick = () => {
    if (activeStep === 0) {
      generalInfoRef.current?.submitForm();
    } else if (activeStep === 1) {
      comboBuilderRef.current?.submitForm();
    }
  };

  const handleBack = () => {
    setActiveStep((prev) => Math.max(prev - 1, 0));
  };

  const handleReset = async () => {
    setShowSubmittedModal(false);
    setActiveStep(0);
    setCompletedSteps([]);
    setFormData({});

    // ✅ First clear productComboId & related state
    await dispatch(resetProductComboState());

    // ✅ Then clear local valueComboData (optional safety)
    setvalueComboData([]);

    // ✅ Then reset both forms (after components re-evaluate props)
    setTimeout(() => {
      generalInfoRef?.current?.resetForm();
      comboBuilderRef?.current?.resetForm();
    }, 0); // slight delay allows rerender with cleared props
  };

  const handleReturnToList = async () => {
    allowNavRef.current = true;
    await handleReset();
    router.push('/value-combos');
    setTimeout(() => {
      allowNavRef.current = false;
    }, 1000); // optional safety delay
  };

  const steps = [
    {
      title: 'General Information',
      component: (
        <ComboGeneralInformation
          ref={generalInfoRef}
          onSuccess={handleStepSuccess}
          valueComboData={valueComboData}
        />
      ),
    },
    {
      title: 'Combo Builder & Pricing',
      component: (
        <ComboBuilder
          ref={comboBuilderRef}
          onSuccess={handleStepSuccess}
          valueComboData={valueComboData}
        />
      ),
    },
  ];

  return (
    <PrivateLayout>
      <div className="flex bg-white mt-[58px] w-full relative">
        {/* Sidebar */}
        <div className="fixed top-16 transition-base z-50 left-3 h-[calc(100dvh-58px)] max-w-[250px] 2xl:max-w-[352px] p-6 2xl:p-[50] 2xl:pb-5 flex flex-col gap-y-5 2xl:gap-y-9">
          <div className="flex flex-col gap-y-1">
            <h1 className="text-lg font-semibold">Create Product</h1>
            <p className="text-sm text-gray-500">
              Add product information, pricing, and more to list your product on
              Hubsups.
            </p>
          </div>
          <ProductStepper
            steps={steps}
            activeStep={activeStep}
            completedSteps={completedSteps}
            onStepClick={(stepIndex) => {
              if (
                completedSteps.includes(stepIndex) ||
                stepIndex <= activeStep
              ) {
                setActiveStep(stepIndex);
              }
            }}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 w-full bg-surface-200 rounded-tl-xl rounded-bl-xl pt-8 2xl:pt-[52px] sm:ml-[250px] 2xl:ml-[352px] overflow-auto h-[calc(100vh-58px)]">
          <div className="flex justify-between items-center mb-4 xl:mb-6 max-w-[1152px] px-6 mx-auto">
            {/*  <button
              className="btn btn-gray"
              onClick={handleBack}
              disabled={activeStep === 0}
            >
              Cancel
            </button> */}
            <button className="btn btn-gray" onClick={handleSaveAsDraft}>
              Save as draft
            </button>
            <div className="flex gap-2">
              <button
                className="btn btn-outline-gray"
                onClick={handleBack}
                disabled={activeStep === 0}
              >
                Back
              </button>
              <button className="btn" onClick={handleNextClick}>
                {activeStep === steps.length - 1
                  ? 'Send for Approval'
                  : 'Save and Next'}
              </button>
            </div>
          </div>

          <div className="w-full h-[calc(100vh-170px)] overflow-y-auto">
            <div className="max-w-[1152px] mx-auto px-6 pb-8">
              {steps[activeStep]?.component}
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      <BaseModal
        isOpen={showSubmittedModal}
        onClose={() => setShowSubmittedModal(false)}
        size="md"
        title="Product Submitted Successfully"
      >
        <div className="p-4">
          <p className="text-sm text-gray-500">
            Your listing was submitted for review. We will notify you when it’s
            approved.
          </p>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t pt-4">
          <button
            className="btn btn-outline-gray"
            onClick={(e) => {
              e?.preventDefault();
              handleReset();
            }}
          >
            Add Another
          </button>
          <button className="btn" onClick={handleReturnToList}>
            Return to List
          </button>
        </div>
      </BaseModal>

      <BaseModal
        isOpen={showPopup}
        onClose={() => setShowPopup(false)}
        title="Leave page with unsaved changes?"
        paragraph=""
        size="md"
      >
        <div className="flex flex-col p-4">
          <p className="text-sm text-gray-500">
            Leaving this page will delete all the unsaved changes.
          </p>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={async () => {
              setShowPopup(false);
              allowNavRef.current = true;
              if (nextUrlRef.current) {
                await router.push(nextUrlRef.current);
                await dispatch(resetProductComboState());
              }
            }}
          >
            Continue later
          </button>

          <button
            type="button"
            className="btn btn-primary"
            onClick={() => setShowPopup(false)}
          >
            Resume now
          </button>
        </div>
      </BaseModal>
    </PrivateLayout>
  );
};

export default ValueCombo;
