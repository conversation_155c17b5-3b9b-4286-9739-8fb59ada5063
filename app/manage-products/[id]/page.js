'use client';

import React from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import ProductListingOptions from '@/app/components/create-product-listing/CreateProductListing';

export default function Page() {
  return (
    <PrivateLayout>
      <div className="flex flex-col w-[calc(100dvw-12px)] mx-auto ml-3 mt-[60px] bg-surface-200 rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100dvh-60px)] transition-base" >
        <ProductListingOptions />
      </div>
    </PrivateLayout>
  );
}
