'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import ComingSoon from '../components/coming-soon/ComingSoon';

const Profile = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  return (
    <PrivateLayout>
      <div className="sm:flex bg-lighter-100 rounded-xl ml-3 w-[calc(100% - 12px)]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />
        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} w-full sm:max-w-[calc(100%-${isSidebarCollapsed ? '80px' : '292px'})] p-6 ml-[${isSidebarCollapsed ? '80px' : '292px'}] mx-auto bg-white rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          <div className="text-center w-full max-w-6xl mx-auto h-[calc(100vh-108px)] flex items-center justify-center flex-col gap-3">
            <ComingSoon />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default Profile;
