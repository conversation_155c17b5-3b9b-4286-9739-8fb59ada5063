'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';

const ActivityLog = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [showMentionedOnly, setShowMentionedOnly] = useState(false);
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Activity Log' }
  ];

  const activities = [
    {
      date: 'SUNDAY, 06 MARCH',
      items: [
        {
          avatar: 'AG',
          name: '<PERSON>',
          action: 'has change',
          ticketId: 'PD-979',
          status: 'Completed',
          time: '08:50 PM'
        },
        {
          avatar: 'MA',
          name: '<PERSON>',
          action: 'comment on your',
          type: 'Post',
          time: '08:23 PM',
          comment: 'Fine, Java MIGHT be a good example of what a programming language should be like...'
        },
        {
          name: '<PERSON>',
          action: 'added tags',
          tags: ['Live issue', 'Backend'],
          time: '07:10 PM'
        }
      ]
    },
    {
      date: 'SATURDAY, 05 MARCH',
      items: [
        {
          avatar: 'JW',
          name: '<PERSON> <PERSON>',
          action: 'comment on your',
          type: 'Post',
          time: '11:19 AM',
          comment: 'The trouble with programmers is that you can never tell what a programmer is doing until it\'s too late.'
        }
      ]
    }
  ];

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />
        
        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          {/* Header with Tabs */}
          <div className="flex items-center justify-between w-full py-6 px-4 2xl:px-8">
            <div className="flex flex-col gap-1">
              <div className="flex flex-col gap-1">
                <h1 className="text-xl font-bold">Activity Log</h1>
                <Breadcrumb items={breadcrumbItems} />
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <label className="flex items-center gap-2 text-sm text-gray-600">
                  <input
                    type="checkbox"
                    checked={showMentionedOnly}
                    onChange={(e) => setShowMentionedOnly(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  Show mentioned only
                </label>
              </div>
              <button className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 border rounded-lg hover:bg-gray-50">
                <span className="icon icon-funnel" />
                Filter
              </button>
            </div>
          </div>

          <div className="w-full h-[calc(100dvh-137px)] overflow-y-auto pb-6 px-4 2xl:px-8">
            <div className="w-full rounded-xl">
              {/* Activity Log Content */}
              <div className="space-y-8 relative">
                {activities.map((day, dayIndex) => (
                  <div key={dayIndex} className="relative bg-white rounded-lg shadow-sm p-4">
                    <h3 className="text-sm font-medium text-gray-500 mb-4">{day.date}</h3>
                    <div className="space-y-6 relative pl-6">
                      {/* Vertical timeline line */}
                      <div className="absolute left-[11px] top-0 bottom-0 w-[2px] bg-gray-200" />
                      
                      {day.items.map((activity, index) => (
                        <div key={index} className="relative">
                          {/* Timeline dot */}
                          <div className="absolute left-[-24px] top-2 w-6 h-6 rounded-full border-2 border-white bg-gray-200 z-10" />
                          
                          <div className="flex items-start gap-3">
                            {activity.avatar && (
                              <div className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center text-sm font-medium">
                                {activity.avatar}
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-1.5 text-sm flex-wrap">
                                <span className="font-medium">{activity.name}</span>
                                <span className="text-gray-600">{activity.action}</span>
                                {activity.ticketId && (
                                  <span className="font-medium text-blue-600">{activity.ticketId}</span>
                                )}
                                {activity.type && (
                                  <span className="font-medium">{activity.type}</span>
                                )}
                                {activity.status && (
                                  <span className="px-2 py-0.5 text-xs font-medium text-green-600 bg-green-50 rounded-full">
                                    {activity.status}
                                  </span>
                                )}
                                <span className="text-gray-500 ml-auto">{activity.time}</span>
                              </div>
                              {activity.comment && (
                                <p className="mt-2 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                                  {activity.comment}
                                </p>
                              )}
                              {activity.tags && (
                                <div className="flex gap-2 mt-2">
                                  {activity.tags.map((tag, tagIndex) => (
                                    <span
                                      key={tagIndex}
                                      className="px-2 py-0.5 text-xs font-medium text-gray-600 bg-gray-100 rounded-full"
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default ActivityLog;
