'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import CommonPagination from '../../components/table/CommonPagination';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import { Tooltip } from 'react-tooltip';

const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const UpdateStatusModal = ({ isOpen, onClose, order, onUpdateStatus }) => {
  const [paymentStatus, setPaymentStatus] = useState(order?.paymentStatus || '');
  const [fulfillmentStatus, setFulfillmentStatus] = useState(order?.fulfillmentStatus || '');
  const [notes, setNotes] = useState('');

  const paymentStatusOptions = ['Pending', 'Paid', 'Failed', 'Refunded'];
  const fulfillmentStatusOptions = ['Processing', 'Shipped', 'Delivered', 'Cancelled'];

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdateStatus(order.id, {
      paymentStatus,
      fulfillmentStatus,
      notes,
      updatedAt: new Date().toISOString()
    });
    onClose();
  };

  if (!order) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Update Status - ${order.orderId}`}
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          <div className="flex flex-col mb-4">
            <span className="block text-sm font-medium text-gray-700 mb-2">
              Payment Status
            </span>
            <SelectField
              label="Payment Status"
              name="paymentStatus"
              className="single-select"
              placeholder="Select payment status"
              value={paymentStatus}
              onChange={(selectedOption) =>
                setPaymentStatus(selectedOption.value)
              }
              options={paymentStatusOptions.map((status) => ({
                value: status,
                label: status
              }))}
              required={false}
            />
          </div>
          <div className="flex flex-col mb-4">
            <span className="block text-sm font-medium text-gray-700 mb-2">
              Fulfillment Status
            </span>
            <SelectField
              label="Fulfillment Status"
              name="fulfillmentStatus"
              className="single-select"
              placeholder="Select fulfillment status"
              value={fulfillmentStatus}
              onChange={(selectedOption) =>
                setFulfillmentStatus(selectedOption.value)
              }
              options={fulfillmentStatusOptions.map((status) => ({
                value: status,
                label: status
              }))}
              required={false}
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes <span className="text-dark-500/40 text-xs font-medium ml-0.5">(Optional)</span>
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={5}
              className="form-control"
              placeholder="Add any notes about this status update..."
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t border-border-color p-4">
          <button
            type="submit"
            className="btn btn-primary"
          >
            Update Status
          </button>
          <button
            type="button"
            onClick={onClose}
            className="btn btn-outline-gray"
          >
            Cancel
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

const EditOrderModal = ({ isOpen, onClose, order, onUpdateOrder }) => {
  const [formData, setFormData] = useState({
    customerName: order?.customerName || '',
    email: order?.email || '',
    phone: order?.phone || '',
    orderAmount: order?.orderAmount || '',
    shippingMethod: order?.shippingMethod || '',
    orderSource: order?.orderSource || ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onUpdateOrder(order.id, {
      ...formData,
      orderAmount: parseFloat(formData.orderAmount),
      updatedAt: new Date().toISOString()
    });
    onClose();
  };

  if (!order) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Edit Order - ${order.orderId}`}
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          <InputField
            label="Customer Name"
            name="customerName"
            type="text"
            placeholder="Enter customer name"
            value={formData.customerName}
            onChange={handleInputChange}
            required={false}
          />
          <InputField
            label="Email"
            name="email"
            type="email"
            placeholder="Enter email"
            value={formData.email}
            onChange={handleInputChange}
            required={false}
          />
          <InputField
            label="Phone"
            name="phone"
            type="tel"
            placeholder="Enter phone number"
            value={formData.phone}
            onChange={handleInputChange}
            required={false}
          />
          <InputField
            label="Order Amount"
            name="orderAmount"
            type="number"
            placeholder="Enter order amount"
            rightText="$"
            rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
            inputClassName="form-control !pl-7 !pr-4"
            value={formData.orderAmount}
            onChange={handleInputChange}
            required={false}
          />
          <div className="flex flex-col mb-4">
            <span className="block text-sm font-medium text-gray-700 mb-2">
              Shipping Method
            </span>
            <SelectField
              label="Shipping Method"
              name="shippingMethod"
              className="single-select"
              placeholder="Select shipping method"
              value={formData.shippingMethod}
              onChange={(selectedOption) =>
                setFormData(prev => ({
                  ...prev,
                  shippingMethod: selectedOption?.value || ''
                }))
              }
              options={[
                { value: 'Standard', label: 'Standard' },
                { value: 'Express', label: 'Express' },
                { value: 'Overnight', label: 'Overnight' },
                { value: 'Free', label: 'Free' },
              ]}
              required={false}
            />
          </div>
          {/* <div className="flex flex-col mb-4">
            <span className="block text-sm font-medium text-gray-700 mb-2">
              Order Source
            </span>
            <SelectField
              label="Order Source"
              name="orderSource"
              className="single-select"
              placeholder="Select order source"
              value={formData.orderSource}
              onChange={handleInputChange}
              options={[
                { value: 'Website', label: 'Website' },
                { value: 'Mobile App', label: 'Mobile App' },
                { value: 'Marketplace', label: 'Marketplace' },
                { value: 'Phone', label: 'Phone' },
                { value: 'In-Store', label: 'In-Store' },
              ]}
              required={false}
            />
          </div> */}
        </div>

        <div className="flex justify-between gap-3 border-t border-border-color p-4">
          <button
            type="button"
            onClick={onClose}
            className="btn btn-outline-gray"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="btn btn-primary"
          >
            Update Order
          </button>
          
        </div>
      </form>
    </BaseOffCanvas>
  );
};

const OrderDetailModal = ({ isOpen, onClose, order, onUpdateStatus }) => {
  const [isUpdateStatusOpen, setIsUpdateStatusOpen] = useState(false);

  if (!order) return null;

  return (
    <>
      <BaseOffCanvas
        isOpen={isOpen}
        onClose={onClose}
        title={`Order Details - ${order.orderId}`}
        size="sm"
      >
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          {/* Customer Information */}
          <div className="">
            <h4 className="font-semibold mb-3">Customer Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <InfoRow label="Name" value={order.customerName} />
              <InfoRow label="Email" value={order.email} />
              <InfoRow label="Phone" value={order.phone} />
              <InfoRow label="Items" value={order.productItems.toLocaleString()} />
            </div>
          </div>
          <hr/>
          {/* Order Information */}
          <div className="">
            <h4 className="font-semibold mb-3">Order Information</h4>
            <div className="grid grid-cols-2 gap-4">
              <InfoRow label="Order Date" value={new Date(order.orderDate).toLocaleDateString()} />
              <InfoRow label="Order Amount" value={`$ ${order.orderAmount.toFixed(2)}`} />
              <div className="flex flex-col gap-1 items-start">
                <span className="text-sm text-gray-500/60 font-normal">Payment Status</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  order.paymentStatus === 'Paid' ? 'bg-green-100 text-green-800' :
                  order.paymentStatus === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {order.paymentStatus}
                </span>
              </div>
              <div className="flex flex-col gap-1 items-start">
                <span className="text-sm text-gray-500/60 font-normal">Fulfillment Status</span>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  order.fulfillmentStatus === 'Delivered' ? 'bg-green-100 text-green-800' :
                  order.fulfillmentStatus === 'Shipped' ? 'bg-purple-100 text-purple-800' :
                  order.fulfillmentStatus === 'Processing' ? 'bg-info-500/20 text-info-500' :
                  'bg-red-100 text-red-800'
                }`}>
                  {order.fulfillmentStatus}
                </span>
              </div>
            </div>
          </div>
        </div>
        {/* Action Buttons */}
        <div className="flex justify-end gap-3 border-t border-border-color p-4">
          <button
            onClick={() => setIsUpdateStatusOpen(true)}
            className="btn btn-primary"
          >
            Update Status
          </button>
          <button className="btn btn-outline-danger">Cancel Order</button>
        </div>
      </BaseOffCanvas>

      <UpdateStatusModal
        isOpen={isUpdateStatusOpen}
        onClose={() => setIsUpdateStatusOpen(false)}
        order={order}
        onUpdateStatus={onUpdateStatus}
      />
    </>
  );
};

const RetailOrdersTable = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [isOrderDetailOpen, setIsOrderDetailOpen] = useState(false);
  const [isEditOrderOpen, setIsEditOrderOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    orderId: true,
    customerName: true,
    productItems: true,
    orderAmount: true,
    email: true,
    orderDate: true,
    paymentStatus: true,
    fulfillmentStatus: true,
    shippingMethod: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [orders, setOrders] = useState([
    {
      id: 'ORD-001',
      orderId: '#HSU-19384',
      productItems: 1,
      orderDate: '2024-01-15T10:30:00Z',
      vendorName: 'ABC Corporation',
      customerName: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      orderAmount: 299.99,
      paymentStatus: 'Paid',
      fulfillmentStatus: 'Processing',
      shippingMethod: 'Standard',
      orderSource: 'Website'
    },
    {
      id: 'ORD-002',
      orderId: '#HSU-19385',
      productItems: 1,
      orderDate: '2024-01-15T09:15:00Z',
      vendorName: 'XYZ Corporation',
      customerName: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-0124',
      orderAmount: 156.50,
      paymentStatus: 'Paid',
      fulfillmentStatus: 'Shipped',
      shippingMethod: 'Express',
      orderSource: 'Mobile App'
    },
    {
      id: 'ORD-003',
      orderId: '#HSU-19386',
      productItems: 1,
      orderDate: '2024-01-15T08:45:00Z',
      vendorName: 'PQR Corporation',
      customerName: 'Mike Davis',
      email: '<EMAIL>',
      phone: '******-0125',
      orderAmount: 89.99,
      paymentStatus: 'Pending',
      fulfillmentStatus: 'Processing',
      shippingMethod: 'Standard',
      orderSource: 'Marketplace'
    },
    {
      id: 'ORD-004',
      orderId: '#HSU-19387',
      productItems: 1,
      orderDate: '2024-01-14T16:20:00Z',
      vendorName: 'LMN Corporation',
      customerName: 'Emily Wilson',
      email: '<EMAIL>',
      phone: '******-0126',
      orderAmount: 445.75,
      paymentStatus: 'Paid',
      fulfillmentStatus: 'Delivered',
      shippingMethod: 'Express',
      orderSource: 'Website'
    },
    {
      id: 'ORD-005',
      orderId: '#HSU-19388',
      productItems: 4,
      orderDate: '2024-01-14T14:10:00Z',
      vendorName: 'STU Corporation',
      customerName: 'Robert Brown',
      email: '<EMAIL>',
      phone: '******-0127',
      orderAmount: 78.25,
      paymentStatus: 'Failed',
      fulfillmentStatus: 'Cancelled',
      shippingMethod: 'Standard',
      orderSource: 'Mobile App'
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-info-500/20 text-info-500';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Order ID',
      selector: (row) => row.orderId,
      sortable: true,
      omit: !displayProperties.orderId,
    },
    {
      name: 'Vendor / Customer Name',
      selector: (row) => row.customerName,
      sortable: true,
      width: '180px',
      omit: !displayProperties.customerName,
      cell: (row) => (
        <div className="flex flex-col">
          <span className="font-medium">{row.vendorName}</span>
          <span className="text-gray-400 text-xs">{row.customerName}</span>
        </div>
      ),
    },
    {
      name: 'Email / Phone',
      selector: (row) => row.email,
      sortable: true,
      width: '180px',
      omit: !displayProperties.email,
      cell: (row) => (
        <div className="flex flex-col">
          <span className="text-gray-400">{row.email}</span>
          <span className="text-gray-400 text-xs">{row.phone}</span>
        </div>
      ),
    },
    {
      name: 'Items',
      selector: (row) => row.productItems,
      sortable: true,
      omit: !displayProperties.productItems,
    },
    {
      name: 'Order Amount',
      selector: (row) => row.orderAmount,
      sortable: true,
      omit: !displayProperties.orderAmount,
      cell: (row) => `$${row.orderAmount.toFixed(2)}`,
    },
    
    {
      name: 'Order Date',
      selector: (row) => row.orderDate,
      sortable: true,
      omit: !displayProperties.orderDate,
      cell: (row) => new Date(row.orderDate).toLocaleDateString(),
    },
    {
      name: 'Payment Status',
      selector: (row) => row.paymentStatus,
      sortable: true,
      omit: !displayProperties.paymentStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold text-nowrap rounded-full ${getStatusColor(row.paymentStatus)}`}>
          {row.paymentStatus}
        </span>
      ),
    },
    {
      name: 'Status',
      selector: (row) => row.fulfillmentStatus,
      sortable: true,
      omit: !displayProperties.fulfillmentStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold text-nowrap rounded-full ${getStatusColor(row.fulfillmentStatus)}`}>
          {row.fulfillmentStatus}
        </span>
      ),
    },
    {
      name: 'Shipping Method',
      selector: (row) => row.shippingMethod,
      sortable: true,
      omit: !displayProperties.shippingMethod,
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewOrder(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button
            onClick={() => handleEditOrder(row)}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Order"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            onClick={() => handleCancelOrder(row.id)}
            data-tooltip-id="cancel-tooltip"
            data-tooltip-content="Cancel Order"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
            <span className="icon icon-x text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="cancel-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '170px',
    },
  ];

  const handleViewOrder = (order) => {
    setSelectedOrder(order);
    setIsOrderDetailOpen(true);
  };

  const handleEditOrder = (order) => {
    setSelectedOrder(order);
    setIsEditOrderOpen(true);
  };

  const handleUpdateOrder = (orderId, updatedData) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? { ...order, ...updatedData }
          : order
      )
    );
    // Show success message (in real app, this would be an API call)
    console.log('Order updated successfully:', { orderId, updatedData });
  };

  const handleUpdateStatus = (orderId, statusData) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? {
              ...order,
              paymentStatus: statusData.paymentStatus,
              fulfillmentStatus: statusData.fulfillmentStatus,
              lastUpdated: statusData.updatedAt,
              statusNotes: statusData.notes
            }
          : order
      )
    );
    // Show success message (in real app, this would be an API call)
    console.log('Order status updated successfully:', { orderId, statusData });
  };

  const handleCancelOrder = (orderId) => {
    setOrders(prevOrders =>
      prevOrders.map(order =>
        order.id === orderId
          ? {
              ...order,
              paymentStatus: 'Refunded',
              fulfillmentStatus: 'Cancelled',
              lastUpdated: new Date().toISOString()
            }
          : order
      )
    );
    // Show success message (in real app, this would be an API call)
    console.log('Order cancelled successfully:', orderId);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredOrders = orders.filter((order) =>
    order.customerName.toLowerCase().includes(filterText.toLowerCase()) ||
    order.orderId.toLowerCase().includes(filterText.toLowerCase()) ||
    order.email.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Retail Orders</h2>
          <span className="text-sm text-gray-300">
            {filteredOrders.length} of {orders.length} orders
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-outline-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
        </div>
      </div>

      <div className="rounded-xl mb-0">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium', grow: 1 },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredOrders}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Order Detail Modal */}
      <OrderDetailModal
        isOpen={isOrderDetailOpen}
        onClose={() => setIsOrderDetailOpen(false)}
        order={selectedOrder}
        onUpdateStatus={handleUpdateStatus}
      />

      {/* Edit Order Modal */}
      <EditOrderModal
        isOpen={isEditOrderOpen}
        onClose={() => setIsEditOrderOpen(false)}
        order={selectedOrder}
        onUpdateOrder={handleUpdateOrder}
      />
    </div>
  );
};

export default RetailOrdersTable;
