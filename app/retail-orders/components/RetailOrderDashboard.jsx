'use client';

import React, { useState } from 'react';
import dynamic from 'next/dynamic';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import SortIcon from '../../components/table/SortIcon';
import CommonPagination from '../../components/table/CommonPagination';

const ApexCharts = dynamic(
  () => import('react-apexcharts').then((mod) => mod.default),
  {
    ssr: false,
  }
);

const MetricCard = ({ title, value, percentage, isPositive, chartData, icon, color = 'primary' }) => {
  const chartOptions = {
    chart: {
      type: 'area',
      toolbar: {
        show: false,
      },
      sparkline: {
        enabled: true,
      },
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      },
    },
    stroke: {
      curve: 'smooth',
      width: 3,
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.2,
        stops: [0, 90, 100],
      },
    },
    colors: [isPositive ? '#10B981' : '#EF4444'],
    tooltip: {
      enabled: false,
    },
    xaxis: {
      labels: {
        show: false,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    markers: {
      size: 0,
    },
  };

  const series = [
    {
      name: title,
      data: chartData || [10, 12, 9, 14, 15, 13, 14],
    },
  ];

  return (
    <div className="card flex gap-2 justify-between items-center">
      <div className="flex flex-col gap-3 2xl:gap-5">
        <div className="flex items-center gap-2">
          {/* {icon && <span className={`icon ${icon} text-${color}-500 text-lg`} />} */}
          <h3 className="text-xs font-semibold text-dark-500">{title}</h3>
        </div>
        <span className="text-xl font-bold">{value}</span>
      </div>
      <div className="flex flex-col justify-between items-end gap-3 2xl:gap-5">
        {percentage && (
          <span
            className={`inline-flex items-center gap-1 text-sm font-medium text-nowrap ${
              isPositive ? 'text-green-500' : 'text-red-500'
            }`}
          >
            <span
              className={`icon ${isPositive ? 'icon-trend-up' : 'icon-trend-down'}`}
            />
            {percentage}%
            <span className="text-dark-500">this month</span>
          </span>
        )}
        {chartData && (
          <ApexCharts
            options={chartOptions}
            series={series}
            type="area"
            height="32px"
            width="80px"
          />
        )}
      </div>
    </div>
  );
};

const RecentOrdersTable = ({ orders = [] }) => {
  const [sortDirection, setSortDirection] = useState(null);
  const [sortedField, setSortedField] = useState(null);

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'processing':
        return 'bg-info-500/20 text-info-500';
      case 'shipped':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Order ID',
      selector: row => row.orderId,
      sortable: true,
      wrap: true,
    },
    {
      name: 'Customer',
      selector: row => row.customerName,
      sortable: true,
      cell: row => (
        <div className="text-sm">
          <div className="font-medium text-gray-900">{row.customerName}</div>
          <div className="text-gray-300 text-xs">{row.email}</div>
        </div>
      ),
    },
    {
      name: 'Amount',
      selector: row => row.orderAmount,
      sortable: true,
      cell: row => `$${row.orderAmount.toFixed(2)}`,
    },
    {
      name: 'Payment Status',
      selector: row => row.paymentStatus,
      sortable: true,
      cell: row => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.paymentStatus)}`}>
          {row.paymentStatus}
        </span>
      ),
    },
    {
      name: 'Fulfillment',
      selector: row => row.fulfillmentStatus,
      sortable: true,
      cell: row => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold text-nowrap rounded-full ${getStatusColor(row.fulfillmentStatus)}`}>
          {row.fulfillmentStatus}
        </span>
      ),
    },
    // {
    //   name: 'Source',
    //   selector: row => row.orderSource,
    //   sortable: true,
    // },
    {
      name: 'Actions',
      cell: row => (
        <div className="flex items-center gap-2">
          <button 
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          <button 
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
            >
            <span className="icon icon-pencil-line text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '100px',
    },
  ];

  return (
    <div className="card overflow-hidden !p-0">
      <div className="flex items-center justify-between p-4 border-b border-border-color">
        <h3 className="text-sm font-semibold">Recent Orders</h3>
        {/* <button className="text-sm text-primary-500 hover:text-primary-600 font-medium">
          View All
        </button> */}
      </div>
      <DataTable
        columns={columns}
        data={orders}
        highlightOnHover
        className="custom-table auto-height-table border-none-table"
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={handleSort}
        sortField={sortedField}
        defaultSortAsc={true}
      />
    </div>
  );
};

const RetailOrderDashboard = ({ data, isLoading }) => {
  const metrics = [
    {
      title: 'Total Orders',
      value: data?.metrics?.totalOrders?.toLocaleString() || '0',
      icon: 'product-lisiting',
      color: 'blue',
      percentage: 12,
      isPositive: true,
      chartData: [10, 12, 9, 14, 15, 13, 14]
    },
    {
      title: 'Pending Orders',
      value: data?.metrics?.pendingOrders?.toLocaleString() || '0',
      icon: 'icon-clock',
      color: 'yellow',
      percentage: 8,
      isPositive: true,
      chartData: [5, 7, 4, 5, 6, 4, 5]
    },
    {
      title: 'Shipped Orders',
      value: data?.metrics?.shippedOrders?.toLocaleString() || '0',
      icon: 'icon-truck',
      color: 'purple',
      percentage: 15,
      isPositive: true,
      chartData: [8, 10, 7, 9, 11, 8, 10]
    },
    {
      title: 'Delivered Orders',
      value: data?.metrics?.deliveredOrders?.toLocaleString() || '0',
      icon: 'icon-check-circle',
      color: 'green',
      percentage: 5,
      isPositive: true,
      chartData: [100, 110, 105, 115, 110, 120, 115]
    },
    {
      title: 'Cancelled Orders',
      value: data?.metrics?.cancelledOrders?.toLocaleString() || '0',
      icon: 'icon-x-circle',
      color: 'red',
      percentage: 2,
      isPositive: false,
      chartData: [3, 4, 3, 2, 4, 3, 2]
    },
    {
      title: 'Return Requests',
      value: data?.metrics?.returnRequests?.toLocaleString() || '0',
      icon: 'icon-refresh-ccw',
      color: 'orange',
      percentage: 3,
      isPositive: false,
      chartData: [2, 3, 2, 4, 3, 2, 3]
    },
    {
      title: 'Total Revenue',
      value: `$${data?.metrics?.totalRevenue?.toLocaleString() || '0'}`,
      icon: 'icon-dollar-sign',
      color: 'green',
      percentage: 18,
      isPositive: true,
      chartData: [300, 350, 320, 380, 360, 400, 387]
    },
    {
      title: 'Average Order Value',
      value: `$${data?.metrics?.averageOrderValue?.toFixed(2) || '0.00'}`,
      icon: 'icon-trending-up',
      color: 'blue',
      percentage: 7,
      isPositive: true,
      chartData: [280, 290, 285, 310, 305, 315, 311]
    }
  ];

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-4 gap-3">
          {Array.from({ length: 8 }).map((_, index) => (
            <div key={index} className="card animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        <div className="card animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, index) => (
              <div key={index} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Order Dashboard</h2>
          <span className="text-sm text-gray-300">Last 30 days</span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-outline-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
        </div>
      </div>

      {/* Dashboard Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-4 gap-3">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* Recent Orders Table */}
      <RecentOrdersTable orders={data?.recentOrders || []} />
    </div>
  );
};

export default RetailOrderDashboard;
