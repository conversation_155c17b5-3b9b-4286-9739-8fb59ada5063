'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import DataTable from 'react-data-table-component';
import SortableItem from '../components/table/SortableItem';
import { Tooltip } from 'react-tooltip';
import Swal from 'sweetalert2';
import FilterField from '../components/table/FilterField';
import CommonPagination from '../components/table/CommonPagination';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import ProductReviewModal from './components/ProductReviewModal';
import SortIcon from '../components/table/SortIcon';

// Status Badge Component
const StatusBadge = ({ status }) => {
  const getStatusConfig = (status) => {
    switch (status) {
      case 'Pending Review':
        return { bg: 'bg-yellow-100', text: 'text-yellow-800'};
      case 'Approved':
        return { bg: 'bg-green-100', text: 'text-green-800'};
      case 'Rejected':
        return { bg: 'bg-red-100', text: 'text-red-800'};
      case 'Needs Revision':
        return { bg: 'bg-orange-100', text: 'text-orange-800'};
      default:
        return { bg: 'bg-gray-100', text: 'text-gray-800'};
    }
  };

  const config = getStatusConfig(status);

  return (
    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
      {/* <span className={`${config.icon} text-[10px]`} /> */}
      {status}
    </span>
  );
};

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ProductReviews = () => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState('');
  const [sortDirection, setSortDirection] = useState('asc');
  const [activeStatusTab, setActiveStatusTab] = useState('all');
  const [isProductReviewOpen, setIsProductReviewOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  const displayMenuRef = useRef(null);

  // Display properties for column visibility
  const [displayProperties, setDisplayProperties] = useState({
    Vendor: true,
    productName: true,
    category: true,
    submittedDate: true,
    reviewStatus: true,
    priority: true,
    sellingPrice: true,
    inventory: true,
  });

  // Sample data for pending product reviews
  const [pendingProducts, setPendingProducts] = useState([
    {
      id: 'PRD-001',
      productName: 'Wireless Bluetooth Headphones',
      productCode: 'WBH-001',
      Vendor: 'Tech Sound',
      productType: 'Simple',
      category: { name: 'Electronics', id: 'cat-1' },
      manufacturer: { name: 'TechSound', id: 'manufacturer-1' },
      reviewStatus: 'Pending Review',
      priority: 'High',
      mrp: 199.99,
      sellingPrice: 149.99,
      costPrice: 89.99,
      inventory: 150,
      minOrderQty: 1,
      variants: 0,
      tags: ['wireless', 'bluetooth', 'audio'],
      thumbnail: '/images/product-3.jpg',
      shortDescription: 'High-quality wireless headphones with noise cancellation',
      submittedDate: '2024-01-15T10:30:00Z',
      reviewNotes: '',
      rejectionReason: ''
    },
    {
      id: 'PRD-002',
      productName: 'Smart Fitness Tracker',
      productCode: 'SFT-002',
      Vendor: 'FitTech',
      productType: 'Variable',
      category: { name: 'Wearables', id: 'cat-2' },
      manufacturer: { name: 'FitTech Inc', id: 'manufacturer-2' },
      reviewStatus: 'Needs Revision',
      priority: 'Medium',
      mrp: 299.99,
      sellingPrice: 249.99,
      costPrice: 149.99,
      inventory: 75,
      minOrderQty: 1,
      variants: 3,
      tags: ['fitness', 'smart', 'health'],
      thumbnail: '/images/product-1.jpg',
      shortDescription: 'Advanced fitness tracker with heart rate monitoring',
      submittedDate: '2024-01-14T09:15:00Z',
      reviewNotes: 'Please update product images and add more detailed specifications',
      rejectionReason: ''
    },
    {
      id: 'PRD-003',
      productName: 'Organic Green Tea',
      productCode: 'OGT-003',
      Vendor: 'Nature\'s Best',
      productType: 'Simple',
      category: { name: 'Food & Beverages', id: 'cat-3' },
      manufacturer: { name: 'Nature\'s Best Co', id: 'manufacturer-3' },
      reviewStatus: 'Approved',
      priority: 'Low',
      mrp: 24.99,
      sellingPrice: 19.99,
      costPrice: 12.99,
      inventory: 200,
      minOrderQty: 5,
      variants: 0,
      tags: ['organic', 'tea', 'healthy'],
      thumbnail: '/images/product-2.jpg',
      shortDescription: 'Premium organic green tea leaves',
      submittedDate: '2024-01-13T14:45:00Z',
      reviewNotes: 'Product approved for listing',
      rejectionReason: ''
    },
    {
      id: 'PRD-004',
      productName: 'Gaming Mechanical Keyboard',
      productCode: 'GMK-004',
      Vendor: 'GameGear',
      productType: 'Variable',
      category: { name: 'Gaming', id: 'cat-4' },
      manufacturer: { name: 'GameGear Ltd', id: 'manufacturer-4' },
      reviewStatus: 'Rejected',
      priority: 'High',
      mrp: 159.99,
      sellingPrice: 129.99,
      costPrice: 79.99,
      inventory: 50,
      minOrderQty: 1,
      variants: 2,
      tags: ['gaming', 'mechanical', 'rgb'],
      thumbnail: '/images/product-1.jpg',
      shortDescription: 'RGB mechanical gaming keyboard with blue switches',
      submittedDate: '2024-01-12T16:20:00Z',
      reviewNotes: '',
      rejectionReason: 'Product images are of poor quality and description lacks technical specifications'
    }
  ]);

  // Status tabs configuration
  const statusTabs = [
    { id: 'all', label: 'All', count: pendingProducts.length },
    { id: 'pending', label: 'Pending Review', count: pendingProducts.filter(p => p.reviewStatus === 'Pending Review').length },
    { id: 'approved', label: 'Approved', count: pendingProducts.filter(p => p.reviewStatus === 'Approved').length },
    { id: 'rejected', label: 'Rejected', count: pendingProducts.filter(p => p.reviewStatus === 'Rejected').length },
    { id: 'revision', label: 'Needs Revision', count: pendingProducts.filter(p => p.reviewStatus === 'Needs Revision').length },
  ];

  // Filtering logic
  const filteredProducts = pendingProducts.filter(product => {
    const matchesSearch = product.productName.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.productCode.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.Vendor.toLowerCase().includes(filterText.toLowerCase()) ||
                         product.category.name.toLowerCase().includes(filterText.toLowerCase());

    const matchesStatus = activeStatusTab === 'all' ||
                         (activeStatusTab === 'pending' && product.reviewStatus === 'Pending Review') ||
                         (activeStatusTab === 'approved' && product.reviewStatus === 'Approved') ||
                         (activeStatusTab === 'rejected' && product.reviewStatus === 'Rejected') ||
                         (activeStatusTab === 'revision' && product.reviewStatus === 'Needs Revision');

    return matchesSearch && matchesStatus;
  });

  // Handle functions
  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setIsProductReviewOpen(true);
  };

  const handleProductAction = async (productId, action, notes = '') => {
    const result = await Swal.fire({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Product`,
      text: `Are you sure you want to ${action} this product?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: action === 'approve' ? '#10B857' : action === 'reject' ? '#EF4444' : '#F59E0B',
      cancelButtonColor: '#6B7280',
      confirmButtonText: `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-xl',
        confirmButton: 'rounded-lg px-4 py-2',
        cancelButton: 'rounded-lg px-4 py-2'
      }
    });

    if (result.isConfirmed) {
      setPendingProducts(prev => prev.map(product =>
        product.id === productId
          ? {
              ...product,
              reviewStatus: action === 'approve' ? 'Approved' :
                           action === 'reject' ? 'Rejected' : 'Needs Revision',
              reviewNotes: notes,
              rejectionReason: action === 'reject' ? notes : ''
            }
          : product
      ));

      Swal.fire({
        title: 'Success!',
        text: `Product has been ${action}d successfully.`,
        icon: 'success',
        confirmButtonColor: '#10B857',
        customClass: {
          popup: 'rounded-xl',
          confirmButton: 'rounded-lg px-4 py-2'
        }
      });
    }
  };

  const handleBulkAction = async (action) => {
    if (selectedRows.length === 0) {
      Swal.fire({
        title: 'No Selection',
        text: 'Please select products to perform bulk action.',
        icon: 'warning',
        confirmButtonColor: '#F59E0B',
        customClass: {
          popup: 'rounded-xl',
          confirmButton: 'rounded-lg px-4 py-2'
        }
      });
      return;
    }

    const result = await Swal.fire({
      title: `Bulk ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      text: `Are you sure you want to ${action} ${selectedRows.length} selected products?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: action === 'approve' ? '#10B857' : '#EF4444',
      cancelButtonColor: '#6B7280',
      confirmButtonText: `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)} All`,
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-xl',
        confirmButton: 'rounded-lg px-4 py-2',
        cancelButton: 'rounded-lg px-4 py-2'
      }
    });

    if (result.isConfirmed) {
      const selectedIds = selectedRows.map(row => row.id);
      setPendingProducts(prev => prev.map(product =>
        selectedIds.includes(product.id)
          ? {
              ...product,
              reviewStatus: action === 'approve' ? 'Approved' : 'Rejected',
              reviewNotes: `Bulk ${action}d`,
              rejectionReason: action === 'reject' ? `Bulk ${action}d` : ''
            }
          : product
      ));
      setSelectedRows([]);

      Swal.fire({
        title: 'Success!',
        text: `${selectedRows.length} products have been ${action}d successfully.`,
        icon: 'success',
        confirmButtonColor: '#10B857',
        customClass: {
          popup: 'rounded-xl',
          confirmButton: 'rounded-lg px-4 py-2'
        }
      });
    }
  };

  // Drag and drop for column reordering
  const items = Object.keys(displayProperties);

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      // Handle reordering logic here if needed
      console.log('Reordering columns:', active.id, 'to', over.id);
    }
  };

  // Priority Badge Component
  const PriorityBadge = ({ priority }) => {
    const getPriorityConfig = (priority) => {
      switch (priority) {
        case 'High':
          return { bg: 'bg-red-100', text: 'text-red-800' };
        case 'Medium':
          return { bg: 'bg-yellow-100', text: 'text-yellow-800' };
        case 'Low':
          return { bg: 'bg-green-100', text: 'text-green-800'};
        default:
          return { bg: 'bg-gray-100', text: 'text-gray-800' };
      }
    };

    const config = getPriorityConfig(priority);

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {/* <span className={`${config.icon} text-[10px]`} /> */}
        {priority}
      </span>
    );
  };

  // Table columns configuration
  const columns = [
    {
      name: 'Vendor',
      selector: row => row.Vendor || '-',
      sortable: true,
      omit: !displayProperties.Vendor,
      width: '150px',
    },
    {
      name: 'Product',
      selector: row => row.productName || '-',
      sortable: true,
      omit: !displayProperties.productName,
      cell: (row) => (
        <div className="flex items-center gap-3 py-2">
          <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
            <Image
              src={row.thumbnail}
              alt={row.productName}
              width={40}
              height={40}
              className="object-cover w-10 h-10 rounded-lg"
            />
          </div>
          <div>
            <div className="font-medium text-sm">{row.productName || '-'}</div>
            <div className="text-xs text-gray-500">{row.productCode || '-'}</div>
          </div>
        </div>
      ),
      width: '300px',
    },
    {
      name: 'Category',
      selector: row => row.category.name || '-',
      sortable: true,
      omit: !displayProperties.category,
    },
    {
      name: 'Submitted Date',
      selector: row => row.submittedDate || '-',
      sortable: true,
      omit: !displayProperties.submittedDate,
      cell: (row) => (
        <div className="text-sm">
          {new Date(row.submittedDate).toLocaleDateString()}
        </div>
      ),
    },
    {
      name: 'Review Status',
      selector: row => row.reviewStatus || '-',
      sortable: true,
      omit: !displayProperties.reviewStatus,
      cell: (row) => <StatusBadge status={row.reviewStatus} />,
    },
    {
      name: 'Priority',
      selector: row => row.priority || '-', 
      sortable: true,
      omit: !displayProperties.priority,
      cell: (row) => <PriorityBadge priority={row.priority} />,
    },
    {
      name: 'Price',
      selector: row => row.sellingPrice || '-',
      sortable: true,
      omit: !displayProperties.sellingPrice,
      cell: (row) => (
        <div className="text-sm">
          <div className="font-medium">${row.sellingPrice || '-'}</div>
          {row.mrp !== row.sellingPrice && (
            <div className="text-xs text-gray-300 line-through">${row.mrp || '-'}</div>
          )}
        </div>
      ),
    },
    {
      name: 'Inventory',
      selector: row => row.inventory || '-',
      sortable: true,
      omit: !displayProperties.inventory,
      cell: (row) => (
        <span className={`font-medium ${
          row.inventory < 10 ? 'text-red-600' :
          row.inventory < 50 ? 'text-yellow-600' :
          'text-green-600'
        }`}>
          {row.inventory || '-'}
        </span>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewProduct(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="Review Product"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-eye text-base" />
          </button>
          {row.reviewStatus === 'Pending Review' && (
            <>
              <button
                onClick={() => handleProductAction(row.id, 'approve')}
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve Product"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base"
              >
                <span className="icon icon-check-3 text-base" />
              </button>
              <button
                onClick={() => handleProductAction(row.id, 'reject')}
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject Product"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-red-500/10 hover:text-red-500 rounded-lg cursor-pointer transition-base"
              >
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.reviewStatus === 'Needs Revision' && (
            <button
              onClick={() => handleProductAction(row.id, 'approve')}
              data-tooltip-id="approve-tooltip"
              data-tooltip-content="Approve Product"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base"
            >
              <span className="icon icon-check-3 text-base" />
            </button>
          )}
        </div>
      ),
    },
  ];

  // Custom styles for DataTable
 const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Pending Product Reviews' }
  ];

  return (
    <PrivateLayout>
      <div className="lex bg-surface-100 rounded-xl w-[calc(100% - 12px)] mt-[60px]">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full p-6 mx-auto rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100vh-60px)] transition-base`}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Pending Product Reviews</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          <div className="flex-1">
            {/* Bulk Actions */}
              {selectedRows.length > 0 && (
                <div className={`fixed bottom-0 content-wrapper collapsed-${isSidebarCollapsed} px-6 py-4 left-0 right-0 bg-white flex justify-between items-center gap-2 transition-base z-10 border-t border-border-color`}>
                  <div className="text-gray-300 flex gap-1 items-center text-sm font-medium">
                    <span className="inline-flex items-center gap-1 text-gray-500 font-semibold">
                      <span className="icon icon-checks text-base !font-semibold text-primary-500" />
                      {selectedRows.length} Items
                    </span> 
                    selected
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleBulkAction('approve')}
                      className="btn btn-outline-success inline-flex items-center"
                    >
                      <span className="icon icon-check-3 mr-2" />
                      Approve Selected
                    </button>
                    <button
                      onClick={() => handleBulkAction('reject')}
                      className="btn btn-outline-danger inline-flex items-center"
                    >
                      <span className="icon icon-x mr-2" />
                      Reject Selected
                    </button>
                  </div>
                </div>
              )}

              {/* Filters and Table */}
              <div className="rounded-xl">
                <FilterField
                  filterText={filterText}
                  setFilterText={setFilterText}
                  isSearchFilterOpen={isSearchFilterOpen}
                  setIsSearchFilterOpen={setIsSearchFilterOpen}
                  isDisplayMenuOpen={isDisplayMenuOpen}
                  setIsDisplayMenuOpen={setIsDisplayMenuOpen}
                  displayMenuRef={displayMenuRef}
                  placeholder="Search products..."
                  statusTabs={statusTabs}
                  activeStatusTab={activeStatusTab}
                  setActiveStatusTab={setActiveStatusTab}
                >
                  <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
                    <DndContext
                      collisionDetection={closestCenter}
                      onDragEnd={handleDragEnd}
                    >
                      <SortableContext
                        items={items}
                        strategy={verticalListSortingStrategy}
                      >
                        <ul className="space-y-1">
                          {items.map((key) => (
                            <SortableItem
                              key={key}
                              id={key}
                              value={key}
                              checked={displayProperties[key]}
                              onChange={() =>
                                setDisplayProperties((prev) => ({
                                  ...prev,
                                  [key]: !prev[key],
                                }))
                              }
                            />
                          ))}
                        </ul>
                      </SortableContext>
                    </DndContext>
                  </div>
                </FilterField>

                <DataTable
                  columns={columns}
                  data={filteredProducts}
                  pagination
                  paginationPerPage={10}
                  selectableRows
                  fixedHeader={true}
                  onSelectedRowsChange={handleSelectedRowsChange}
                  customStyles={customStyles}
                  className="custom-table auto-height-table"
                  noDataComponent={
                    <div className="flex flex-col items-center justify-center py-12">
                      <span className="icon icon-package text-4xl text-gray-300 mb-4" />
                      <p className="text-gray-500 text-lg font-medium">No products found</p>
                      <p className="text-gray-400 text-sm">Try adjusting your search or filters</p>
                    </div>
                  }
                  selectableRowsComponent={CustomCheckbox}
                  sortIcon={<SortIcon sortDirection={sortDirection} />}
                  onSort={handleSort}
                  sortField={sortedField}
                  defaultSortAsc={true}
                  paginationRowsPerPageOptions={[8]}
                  paginationComponentOptions={{
                    rowsPerPageText: 'Rows per page:',
                    rangeSeparatorText: 'of',
                    selectAllRowsItem: false,
                    noRowsPerPage: true,
                  }}
                  paginationComponent={(props) => (
                    <CommonPagination
                      selectedCount={props.selectedRows?.length}
                      total={props.totalRows}
                      page={props.currentPage}
                      perPage={props.rowsPerPage}
                      onPageChange={props.onChangePage}
                    />
                  )}
                />
              </div>

              {/* Product Review Modal */}
              <ProductReviewModal
                isOpen={isProductReviewOpen}
                onClose={() => setIsProductReviewOpen(false)}
                product={selectedProduct}
                onProductAction={handleProductAction}
              />

              {/* Tooltips */}
              <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
              <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
              <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
};

export default ProductReviews;
