'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import InputField from '../../components/Inputs/InputField';
import SelectField from '../../components/Inputs/SelectField';

const ProductReviewModal = ({ isOpen, onClose, product, onProductAction }) => {
  const [reviewNotes, setReviewNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [priority, setPriority] = useState(product?.priority || 'Medium');

  if (!product) return null;

  const handleAction = (action) => {
    const notes = action === 'reject' ? rejectionReason : reviewNotes;
    onProductAction(product.id, action, notes);
    onClose();
    setReviewNotes('');
    setRejectionReason('');
  };

  // Status Badge Component
  const StatusBadge = ({ status }) => {
    const getStatusConfig = (status) => {
      switch (status) {
        case 'Pending Review':
          return { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: 'icon-clock' };
        case 'Approved':
          return { bg: 'bg-green-100', text: 'text-green-800', icon: 'icon-check-3' };
        case 'Rejected':
          return { bg: 'bg-red-100', text: 'text-red-800', icon: 'icon-x' };
        case 'Needs Revision':
          return { bg: 'bg-orange-100', text: 'text-orange-800', icon: 'icon-pencil-line' };
        default:
          return { bg: 'bg-gray-100', text: 'text-gray-800', icon: 'icon-question' };
      }
    };

    const config = getStatusConfig(status);
    
    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        <span className={`${config.icon} text-[10px]`} />
        {status}
      </span>
    );
  };

  // Priority Badge Component
  const PriorityBadge = ({ priority }) => {
    const getPriorityConfig = (priority) => {
      switch (priority) {
        case 'High':
          return { bg: 'bg-red-100', text: 'text-red-800', icon: 'icon-arrow-up' };
        case 'Medium':
          return { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: 'icon-minus' };
        case 'Low':
          return { bg: 'bg-green-100', text: 'text-green-800', icon: 'icon-arrow-down' };
        default:
          return { bg: 'bg-gray-100', text: 'text-gray-800', icon: 'icon-minus' };
      }
    };

    const config = getPriorityConfig(priority);
    
    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        <span className={`${config.icon} text-[10px]`} />
        {priority}
      </span>
    );
  };

  const InfoRow = ({ label, value, children }) => (
    <div className="flex justify-between items-start py-2 border-b border-gray-100 last:border-b-0">
      <span className="text-sm font-medium text-gray-600 min-w-[120px]">{label}:</span>
      <div className="text-sm text-gray-900 text-right flex-1">
        {children || value}
      </div>
    </div>
  );

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Product Review - ${product.productName}`}
      size="lg"
    >
      <div className="space-y-6 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Product Header */}
        <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
          <div className="w-20 h-20 bg-white rounded-lg flex items-center justify-center">
            <Image
              src={product.thumbnail}
              alt={product.productName}
              width={80}
              height={80}
              className="object-cover w-20 h-20 rounded-lg"
            />
          </div>
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{product.productName}</h3>
                <p className="text-sm text-gray-500">{product.productCode}</p>
                <p className="text-sm text-gray-600 mt-1">{product.shortDescription}</p>
              </div>
              <div className="flex flex-col gap-2">
                <StatusBadge status={product.reviewStatus} />
                <PriorityBadge priority={product.priority} />
              </div>
            </div>
          </div>
        </div>

        {/* Product Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Basic Information</h4>
            <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-2">
              <InfoRow label="Vendor" value={product.Vendor} />
              <InfoRow label="Category" value={product.category.name} />
              <InfoRow label="Manufacturer" value={product.manufacturer.name} />
              <InfoRow label="Product Type" value={product.productType} />
              <InfoRow label="Min Order Qty" value={product.minOrderQty} />
              <InfoRow label="Variants" value={product.variants} />
              <InfoRow label="Submitted Date" value={new Date(product.submittedDate).toLocaleDateString()} />
            </div>
          </div>

          {/* Pricing & Inventory */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Pricing & Inventory</h4>
            <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-2">
              <InfoRow label="MRP" value={`$${product.mrp}`} />
              <InfoRow label="Selling Price" value={`$${product.sellingPrice}`} />
              <InfoRow label="Cost Price" value={`$${product.costPrice}`} />
              <InfoRow label="Inventory" value={product.inventory} />
              <InfoRow label="Tags">
                <div className="flex flex-wrap gap-1">
                  {product.tags.map((tag, index) => (
                    <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </InfoRow>
            </div>
          </div>
        </div>

        {/* Review Notes Section */}
        {(product.reviewNotes || product.rejectionReason) && (
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Previous Review Notes</h4>
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              {product.reviewNotes && (
                <div className="mb-3">
                  <span className="text-sm font-medium text-gray-600">Review Notes:</span>
                  <p className="text-sm text-gray-900 mt-1">{product.reviewNotes}</p>
                </div>
              )}
              {product.rejectionReason && (
                <div>
                  <span className="text-sm font-medium text-red-600">Rejection Reason:</span>
                  <p className="text-sm text-red-900 mt-1">{product.rejectionReason}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Review Actions */}
        {product.reviewStatus === 'Pending Review' && (
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Review Actions</h4>
            <div className="bg-white border border-gray-200 rounded-lg p-4 space-y-4">
              <InputField
                label="Review Notes"
                placeholder="Add your review notes here..."
                value={reviewNotes}
                onChange={(e) => setReviewNotes(e.target.value)}
                marginBottom="mb-0"
                inputClassName="min-h-[80px]"
                type="textarea"
              />
              
              <InputField
                label="Rejection Reason (if rejecting)"
                placeholder="Specify reason for rejection..."
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                marginBottom="mb-0"
                inputClassName="min-h-[60px]"
                type="textarea"
              />

              <SelectField
                label="Priority"
                value={{ value: priority, label: priority }}
                onChange={(option) => setPriority(option.value)}
                options={[
                  { value: 'High', label: 'High' },
                  { value: 'Medium', label: 'Medium' },
                  { value: 'Low', label: 'Low' }
                ]}
                className="single-select"
              />
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
        
        {product.reviewStatus === 'Pending Review' && (
          <>
            <button
              type="button"
              className="btn btn-outline-success inline-flex items-center"
              onClick={() => handleAction('approve')}
            >
              <span className="icon icon-check-3 mr-2" />
              Approve Product
            </button>
            <button
              type="button"
              className="btn btn-outline-warning inline-flex items-center"
              onClick={() => handleAction('revision')}
            >
              <span className="icon icon-pencil-line mr-2" />
              Request Revision
            </button>
            <button
              type="button"
              className="btn btn-outline-danger inline-flex items-center"
              onClick={() => handleAction('reject')}
            >
              <span className="icon icon-x mr-2" />
              Reject Product
            </button>
          </>
        )}
        
        {product.reviewStatus === 'Needs Revision' && (
          <button
            type="button"
            className="btn btn-outline-success inline-flex items-center"
            onClick={() => handleAction('approve')}
          >
            <span className="icon icon-check-3 mr-2" />
            Approve Product
          </button>
        )}
      </div>
    </BaseOffCanvas>
  );
};

export default ProductReviewModal;
