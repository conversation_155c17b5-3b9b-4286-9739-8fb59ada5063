'use client';

import React from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Link from 'next/link';
// import CreateCampaign from '@/app/components/create-advertisement/CreateCampaign';

export default function Page() {
  return (
    <PrivateLayout>
      <div className="flex bg-white mt-[58px] w-full relative">
        {/* Sidebar */}
        <div className="fixed top-16 transition-base z-50 left-3 h-[calc(100dvh-58px)] max-w-[250px] 2xl:max-w-[352px] p-6 2xl:p-[55] 2xl:pb-5 flex flex-col gap-y-5 2xl:gap-y-9">
          <div className="flex flex-col gap-y-1">
            <h1 className="text-lg 2xl:text-xl font-semibold">Create Advertisement</h1>
            <p className="text-sm text-gray-500">
              Set up targeted ad campaigns to boost product visibility. Upload creative assets, define placement, and schedule your ad to reach the right buyers at the right time.
            </p>
          </div>
          <ul className="flex gap-3 mt-auto text-xs text-gray-500">
            <li className="relative">
              <Link
                href=""
                title="NeeD help?"
                className="text-xs font-semibold hover:text-primary-500 transition-base"
              >
                Need help?
              </Link>
            </li>
          </ul>
        </div>

        {/* Create Campaign */}
        <div className="flex-1 w-full bg-surface-200 rounded-tl-xl rounded-bl-xl pt-8 2xl:pt-[52px] sm:ml-[250px] 2xl:ml-[352px] overflow-auto h-[calc(100vh-58px)]">
          {/* <CreateCampaign /> */}
        </div>
      </div>
    </PrivateLayout>
  );
}
