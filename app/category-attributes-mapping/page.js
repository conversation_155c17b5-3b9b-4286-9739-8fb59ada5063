'use client';

import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import CustomCheckbox from '../components/Inputs/CustomCheckbox';
import ToggleSwitch from '../components/Inputs/ToggleSwitch';
import SelectField from '../components/Inputs/SelectField';
import InputField from '../components/Inputs/InputField';
import BaseOffCanvas from '../components/offCanvas/BaseOffCanvas';

const CategoryAttributeMapping = () => {
  const [isOffCanvasOpen, setIsOffCanvasOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [attributeType, setAttributeType] = useState('all');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [newAttributeForm, setNewAttributeForm] = useState({
    name: '',
    type: 'text',
    isRequired: false
  });

  const handleAddAttribute = () => {
    // Add new attribute logic
    setIsOffCanvasOpen(false);
  };

  // Sample categories data
  const categories = [
    { value: '1', label: 'Electronics' },
    { value: '2', label: 'Clothing' },
    { value: '3', label: 'Home & Garden' },
    { value: '4', label: 'Books' },
    { value: '5', label: 'Sports Equipment' }
  ];

  // Sample attributes data
  const [attributes] = useState([
    { id: 1, name: 'Brand', type: 'text' },
    { id: 2, name: 'Color', type: 'color' },
    { id: 3, name: 'Size', type: 'text' },
    { id: 4, name: 'Material', type: 'text' },
    { id: 5, name: 'Product Images', type: 'image' },
    { id: 6, name: 'Weight', type: 'text' },
    { id: 7, name: 'Dimensions', type: 'text' },
    { id: 8, name: 'Warranty Period', type: 'text' }
  ]);

  const validationSchema = Yup.object().shape({
    category: Yup.string().required('Category selection is required'),
    attributes: Yup.array().min(1, 'At least one attribute must be selected')
  });

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Product Attributes' },
    { label: 'Category Attribute Mapping' }
  ];

  const initialValues = {
    category: '',
    attributes: [],
    requiredAttributes: []
  };

  const attributeTypes = [
    { label: 'All', value: 'all' },
    { label: 'Text', value: 'text' },
    { label: 'Color', value: 'color' },
    { label: 'Image', value: 'image' }
  ];

  // Filter attributes based on selected type
  const filteredAttributes = attributeType === 'all'
    ? attributes
    : attributes.filter(attr => attr.type === attributeType);

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Category Attribute Mapping</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <button
              onClick={() => setIsOffCanvasOpen(true)}
              className="btn btn-primary flex items-center gap-2"
            >
              <span className="icon icon-plus text-base" />
              Add New Attribute
            </button>
          </div>

          <div className="card !p-0 grid grid-cols-12">
            {/* Left Panel - Category Selection */}
            <div className="col-span-3 border-r border-border-color p-4">
              <h2 className="text-base font-semibold mb-4">Select Category</h2>
              <Formik
                initialValues={initialValues}
                validationSchema={validationSchema}
                onSubmit={(values) => {
                  console.log(values);
                }}
              >
                {(formik) => (
                  <Form>
                    <SelectField
                      name="category"
                      className="single-select"
                      placeholder="Search and select category"
                      options={categories}
                      onChange={(value) => setSelectedCategory(value)}
                      formik={formik}
                      isSearchable
                    />
                  </Form>
                )}
              </Formik>
            </div>

            {/* Right Panel - Attributes */}
            <div className="col-span-9 h-[calc(100dvh-175px)] overflow-auto">
              <div className="flex items-center justify-between mb-4 sticky top-0 bg-white z-10 p-4">
                <h2 className="text-base font-semibold">Attributes</h2>
                <div className="flex items-center gap-3">
                  {attributeTypes.map((type) => (
                    <button
                      key={type.value}
                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-colors ${attributeType === type.value ? 'bg-primary-500 text-white' : 'bg-surface-100 text-gray-500 hover:bg-surface-200'}`}
                      onClick={() => setAttributeType(type.value)}
                    >
                      {type.label}
                    </button>
                  ))}                    
                </div>
              </div>
              <div className="p-4 border border-border-color m-4 rounded-lg bg-lighter-100/80">
                <div className="grid grid-cols-2 xl:grid-cols-3 gap-4">
                  {filteredAttributes.map((attribute) => (
                    <div key={attribute.id} className="flex flex-col p-3 border bg-white border-border-color rounded-lg hover:shadow-md  transition-base">
                      <div className="flex justify-between items-center gap-3 border-b border-border-color pb-3 mb-3">
                        <CustomCheckbox
                          labelClassName="!font-semibold !text-sm"
                          id={`attr-${attribute.id}`}
                          name={`attributes.${attribute.id}`}
                          label={attribute.name}
                        />
                      </div>
                      <div className="flex items-center justify-between gap-2">
                        <span className="text-xs text-gray-500 font-semibold border border-gray-500/20 px-2 py-1 bg-gray-100/50 capitalize rounded-lg">{attribute.type}</span>
                        <div className="flex justify-between items-center gap-2">
                          <span className="text-sm text-gray-500">Required</span>
                          <ToggleSwitch
                            id={`required-${attribute.id}`}
                            name={`requiredAttributes.${attribute.id}`}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex justify-end mt-4 pt-4 border-t border-border-color">
                  <button className="btn btn-primary">Save</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>


      {/* Add New Attribute Off-canvas */}
      <BaseOffCanvas
        isOpen={isOffCanvasOpen}
        onClose={() => setIsOffCanvasOpen(false)}
        title="Add New Attribute"
        size="sm"
      >
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          <InputField
            label="Attribute Name"
            placeholder="Enter attribute name"
            value={newAttributeForm.name}
            onChange={(e) => setNewAttributeForm({
              ...newAttributeForm,
              name: e.target.value
            })}
          />
          <div className="flex flex-col mb-4 xl:mb-6">
            <span className="form-label">
              Attribute Type
            </span>
            <SelectField
              id="attributeType"
              name="attributeType"
              label="Attribute Type"
              className="single-select"
              placeholder="Select attribute type"
              options={attributeTypes}
              value={attributeTypes.find(type => type.value === newAttributeForm.type)}
              onChange={(selected) => setNewAttributeForm({
                ...newAttributeForm,
                type: selected.value
              })}
            />
          </div>
          <div className="flex flex-col gap-2">
            <span className="text-sm text-gray-500">Required</span>
            <ToggleSwitch
              id="required-new"
              checked={newAttributeForm.isRequired}
              onChange={(checked) => setNewAttributeForm({
                ...newAttributeForm,
                isRequired: checked
              })}
            />
          </div>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={() => setIsOffCanvasOpen(false)}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn"
            onClick={handleAddAttribute}
          >
            Add Attribute
          </button>
        </div>
      </BaseOffCanvas>
    </PrivateLayout>
  );
};

export default CategoryAttributeMapping;