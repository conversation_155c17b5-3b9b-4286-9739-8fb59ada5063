'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';

// InfoRow components
const InfoRow = ({ label, value, className }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className="text-sm text-dark-500 font-medium">{value}</span>
  </div>
);

// Custom Checkbox
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ReturnDetailModal = ({ isOpen, onClose, returnItem }) => {
  if (!returnItem) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Return Details - ${returnItem.returnId}`}
      size="sm"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Return Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Return Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Return ID" value={returnItem.returnId} />
            <InfoRow label="Order ID" value={returnItem.orderId} />
            <InfoRow label="Return Reason" value={returnItem.returnReason} />
            <InfoRow label="Return Date" value={new Date(returnItem.returnDate).toLocaleDateString()} />
          </div>
        </div>
        <hr/>

        {/* Products Returned */}
        <div className="">
          <h4 className="font-semibold mb-3">Products Returned</h4>
          <div className="space-y-3">
            {returnItem.productsReturned.map((product, index) => (
              <div key={index} className="flex justify-between items-center p-3 border border-border-color rounded-lg">
                <div>
                  <p className="text-sm font-medium">{product.name}</p>
                  <p className="text-xs text-gray-400">SKU: {product.sku}</p>
                  <p className="text-xs text-gray-400">Condition: {product.condition}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{product.quantity.toLocaleString()} units</p>
                  <p className="text-xs text-gray-400">${product.unitValue.toFixed(2)} each</p>
                  <p className="text-sm font-medium">Total: ${(product.quantity * product.unitValue).toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
        <hr/>

        {/* Credit Note Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Credit Note Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1 items-start">
              <span className="text-sm text-gray-400">Credit Note Issued</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                returnItem.creditNoteIssued === 'Yes' ? 'bg-green-100 text-green-800' : 
                returnItem.creditNoteIssued === 'No' ? 'bg-red-100 text-red-800' :
                'bg-yellow-100 text-yellow-800'
              }`}>
                {returnItem.creditNoteIssued}
              </span>
            </div>
            <InfoRow label="Credit Amount" value={returnItem.creditAmount ? `$${returnItem.creditAmount.toLocaleString()}` : 'N/A'} />
          </div>
        </div>
        <hr/>

        {/* Reconciliation Status */}
        <div className="">
          <h4 className="font-semibold mb-3">Reconciliation Status</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="flex flex-col gap-1 items-start">
              <label className="text-sm text-gray-400">Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                returnItem.reconciliationStatus === 'Settled' ? 'bg-green-100 text-green-800' : 
                returnItem.reconciliationStatus === 'Pending' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {returnItem.reconciliationStatus}
              </span>
            </div>
            <InfoRow label="Processed By" value={returnItem.processedBy || 'Unassigned'} />
          </div>
        </div>
        <hr/>

        {/* Inspection Notes */}
        <div className="">
          <h4 className="font-semibold mb-3">Inspection Notes</h4>
          <p className="text-sm text-gray-500">{returnItem.inspectionNotes || 'No inspection notes available.'}</p>
        </div>
        <hr/>

        {/* Pickup Request */}
        {returnItem.pickupRequest && (
          <div className="">
            <h4 className="font-semibold mb-3">Pickup Request</h4>
            <div className="grid grid-cols-2 gap-4">
              {/* <InfoRow label="Pickup Status" value={returnItem.pickupRequest.requested ? 'Yes' : 'No'} /> */}
              <InfoRow label="Pickup Address" value={returnItem.pickupRequest.status === 'Pending' ? 'Pending' : 'Scheduled'} />
              <InfoRow label="Pickup Date" value={returnItem.pickupRequest.status === 'Pending' ? 'Pending' : new Date(returnItem.pickupRequest.scheduledDate).toLocaleDateString()} />
            </div>
          </div>
        )}

        {/* Images */}
        {returnItem.images && returnItem.images.length > 0 && (
          <>
            <hr/>
            <div className="">
              <h4 className="font-semibold mb-3">Images</h4>
              <div className="grid grid-cols-3 gap-3">
                {returnItem.images.map((image, index) => (
                  <div key={index} className="aspect-square bg-gray-200 rounded-lg flex items-center justify-center">
                    <Image
                      src={image.preview}
                      alt={image.name}
                      width={200}
                      height={200}
                      className="object-cover rounded-lg"
                    />
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        {returnItem.returnStatus === 'Requested' && (
          <>
            <button className="btn btn-primary">Approve Return</button>
            <button className="btn btn-outline-red">Reject Return</button>
          </>
        )}
        {returnItem.returnStatus === 'Received' && (
          <button className="btn btn-primary">Complete Inspection</button>
        )}
        {returnItem.creditNoteIssued === 'No' && returnItem.returnStatus === 'Inspected' && (
          <button className="btn btn-primary">Issue Credit Note</button>
        )}
        <button className="btn btn-gray" onClick={onClose}>Cancel</button>
      </div>
    </BaseOffCanvas>
  );
};

const BulkReturnsReconciliation = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedReturn, setSelectedReturn] = useState(null);
  const [isReturnDetailOpen, setIsReturnDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    returnId: true,
    orderId: true,
    returnReason: true,
    productsReturned: true,
    returnStatus: true,
    creditNoteIssued: true,
    reconciliationStatus: true,
    inspectionNotes: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [returns] = useState([
    {
      id: 'RET-B001',
      returnId: 'RET-B001',
      orderId: 'BO-001',
      returnReason: 'Damage',
      productsReturned: [
        { name: 'Industrial Printer', sku: 'IP-001', quantity: 5, unitValue: 1200.00, condition: 'Damaged' }
      ],
      returnStatus: 'Inspected',
      creditNoteIssued: 'Yes',
      creditAmount: 6000,
      reconciliationStatus: 'Settled',
      inspectionNotes: 'Units arrived with shipping damage. Packaging was insufficient for the weight.',
      processedBy: 'QA Manager',
      returnDate: '2024-01-20T10:00:00Z',
      pickupRequest: {
        status: 'Completed',
        scheduledDate: '2024-01-19T14:00:00Z'
      },
      images: [
        { name: 'default-Image.jpg', preview: '/images/default-Image.jpg' },
        { name: 'default-Image.jpg', preview: '/images/default-Image.jpg' }
      ]
    },
    {
      id: 'RET-B002',
      returnId: 'RET-B002',
      orderId: 'BO-002',
      returnReason: 'Over-shipment',
      productsReturned: [
        { name: 'Steel Pipes', sku: 'SP-003', quantity: 20, unitValue: 150.00, condition: 'Good' }
      ],
      returnStatus: 'Received',
      creditNoteIssued: 'Pending',
      creditAmount: null,
      reconciliationStatus: 'Pending',
      inspectionNotes: 'Excess units received. Customer ordered 200 but received 220.',
      processedBy: 'Warehouse Supervisor',
      returnDate: '2024-01-18T15:30:00Z',
      pickupRequest: {
        status: 'Completed',
        scheduledDate: '2024-01-18T10:00:00Z'
      },
      images: []
    },
    {
      id: 'RET-B003',
      returnId: 'RET-B003',
      orderId: 'BO-003',
      returnReason: 'Wrong item',
      productsReturned: [
        { name: 'Desktop Computers', sku: 'DC-007', quantity: 10, unitValue: 600.00, condition: 'New' }
      ],
      returnStatus: 'Requested',
      creditNoteIssued: 'No',
      creditAmount: null,
      reconciliationStatus: 'Pending',
      inspectionNotes: null,
      processedBy: null,
      returnDate: '2024-01-21T09:15:00Z',
      pickupRequest: {
        status: 'Scheduled',
        scheduledDate: '2024-01-22T11:00:00Z'
      },
      images: []
    },
    {
      id: 'RET-B004',
      returnId: 'RET-B004',
      orderId: 'BO-004',
      returnReason: 'Quality issues',
      productsReturned: [
        { name: 'Office Chairs', sku: 'OC-006', quantity: 15, unitValue: 120.00, condition: 'Defective' }
      ],
      returnStatus: 'Inspected',
      creditNoteIssued: 'No',
      creditAmount: null,
      reconciliationStatus: 'Escalated',
      inspectionNotes: 'Multiple units have manufacturing defects. Escalated to supplier for investigation.',
      processedBy: 'QA Manager',
      returnDate: '2024-01-19T13:45:00Z',
      pickupRequest: {
        status: 'Completed',
        scheduledDate: '2024-01-19T08:00:00Z'
      },
      images: [
        { name: 'default-Image.jpg', preview: '/images/default-Image.jpg' },
        { name: 'default-Image.pdf', preview: '/images/default-Image.pdf' }
      ]
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'requested':
        return 'bg-info-500/20 text-info-500';
      case 'received':
        return 'bg-green-100 text-green-800';
      case 'inspected':
        return 'bg-purple-100 text-purple-800';
      case 'closed':
        return 'bg-green-100 text-green-800';
      case 'yes':
        return 'bg-green-100 text-green-800';
      case 'no':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'settled':
        return 'bg-green-100 text-green-800';
      case 'escalated':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Return ID',
      selector: (row) => row.returnId,
      sortable: true,
      omit: !displayProperties.returnId,
    },
    {
      name: 'Order ID',
      selector: (row) => row.orderId,
      sortable: true,
      omit: !displayProperties.orderId,
    },
    {
      name: 'Return Reason',
      selector: (row) => row.returnReason,
      sortable: true,
      omit: !displayProperties.returnReason,
    },
    {
      name: 'Product(s) Returned',
      selector: (row) => row.productsReturned,
      omit: !displayProperties.productsReturned,
      cell: (row) => (
        <div className="text-sm">
          {row.productsReturned.length === 1 ? (
            <div>
              <div>{row.productsReturned[0].name}</div>
              <div className="text-gray-500">{row.productsReturned[0].quantity} units</div>
            </div>
          ) : (
            <div>{row.productsReturned.length} products</div>
          )}
        </div>
      ),
    },
    {
      name: 'Return Status',
      selector: (row) => row.returnStatus,
      sortable: true,
      omit: !displayProperties.returnStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.returnStatus)}`}>
          {row.returnStatus}
        </span>
      ),
    },
    {
      name: 'Credit Note Issued',
      selector: (row) => row.creditNoteIssued,
      sortable: true,
      omit: !displayProperties.creditNoteIssued,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.creditNoteIssued)}`}>
          {row.creditNoteIssued}
        </span>
      ),
    },
    {
      name: 'Reconciliation Status',
      selector: (row) => row.reconciliationStatus,
      sortable: true,
      omit: !displayProperties.reconciliationStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.reconciliationStatus)}`}>
          {row.reconciliationStatus}
        </span>
      ),
    },
    {
      name: 'Inspection Notes',
      selector: (row) => row.inspectionNotes,
      omit: !displayProperties.inspectionNotes,
      cell: (row) => (
        <div className="text-sm max-w-[200px] truncate" title={row.inspectionNotes}>
          {row.inspectionNotes || '-'}
        </div>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewReturn(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Details"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            {/* View Details */}
            <span className="icon icon-eye text-base" />
          </button>
          {row.returnStatus === 'Requested' && (
            <>
              <button 
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base">
                {/* Approve */}
                <span className="icon icon-check-3 text-base" />
              </button>
              <button 
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base">
                {/* Reject */}
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}
          {row.creditNoteIssued === 'No' && row.returnStatus === 'Inspected' && (
            <button 
              data-tooltip-id="issue-credit-note-tooltip"
              data-tooltip-content="Issue Credit Note"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base">
              {/* Issue Credit Note */}
              <span className="icon icon-file-text text-base" />
            </button>
          )}

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="issue-credit-note-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '150px',
    },
  ];

  const handleViewReturn = (returnItem) => {
    setSelectedReturn(returnItem);
    setIsReturnDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredReturns = returns.filter((returnItem) =>
    returnItem.returnId.toLowerCase().includes(filterText.toLowerCase()) ||
    returnItem.orderId.toLowerCase().includes(filterText.toLowerCase()) ||
    returnItem.returnReason.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Bulk Returns & Reconciliation</h2>
          <span className="text-sm text-gray-300">
            {filteredReturns.length} of {returns.length} returns
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-outline-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
        </div>
      </div>

      <div className="rounded-xl mb-0">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'medium' },
              { size: 'medium', grow: 1 },
              { size: 'small' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large', grow: 1 },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredReturns}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Return Detail Modal */}
      <ReturnDetailModal
        isOpen={isReturnDetailOpen}
        onClose={() => setIsReturnDetailOpen(false)}
        returnItem={selectedReturn}
      />
    </div>
  );
};

export default BulkReturnsReconciliation;
