'use client';

import React from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';

const BulkOrderNegotiationForm = ({ order, onClose }) => {
  const formik = useFormik({
    initialValues: {
      counterOffer: order?.price || '',
      reason: '',
      status: '',
    },
    validationSchema: Yup.object({
      counterOffer: Yup.number()
        .required('Counter offer is required')
        .positive('Price must be positive'),
      reason: Yup.string().required('Reason is required'),
      status: Yup.string().required('Status is required'),
    }),
    onSubmit: async (values) => {
      try {
        // Submit negotiation data to API
        onClose();
      } catch (error) {
        console.error('Error submitting negotiation:', error);
      }
    },
  });

  const statusOptions = [
    { value: 'counter_offered', label: 'Send Counter Offer' },
    { value: 'approved', label: 'Approve Order' },
    { value: 'rejected', label: 'Reject Order' },
  ];

  return (
    <form onSubmit={formik.handleSubmit} className="h-full">
      <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Order Summary */}
        <div className="bg-surface-100 rounded-xl p-4 mb-4 border border-border-color">
          <h3 className="text-sm font-semibold mb-4">Order Summary</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex flex-col">
              <span className="text-sm text-gray-400">Order ID</span>
              <span className="text-dark-500 font-medium">{order?.id}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-gray-400">Buyer</span>
              <span className="text-dark-500 font-medium">{order?.buyer}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-gray-400">Total Units</span>
              <span className="text-dark-500 font-medium">{order?.totalUnits}</span>
            </div>
            <div className="flex flex-col">
              <span className="text-sm text-gray-400">Current Price</span>
              <span className="text-dark-500 font-medium">${order?.price?.toLocaleString()}</span>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <span className="form-label">
            Action
          </span>
          <SelectField
            id="status"
            name="status"
            placeholder="Select action"
            className="single-select"
            value={formik.values.status}
            onChange={(selected) => formik.setFieldValue('status', selected.value)}
            error={formik.touched.status && formik.errors.status}
            options={statusOptions}
          />
        </div>

        <InputField
          type="number"
          id="counterOffer"
          label="Counter Offer Price"
          name="counterOffer"
          placeholder="Enter counter offer amount"
          rightText="$"
          rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
          inputClassName="form-control !pl-7 !pr-4"
          value={formik.values.counterOffer}
          onChange={formik.handleChange}
          error={formik.touched.counterOffer && formik.errors.counterOffer}
        />
        <div className="flex flex-col">
          <label htmlFor="reason" className="form-label">
            Reason
          </label>
          <textarea
            id="reason"
            name="reason"
            placeholder="Enter reason"
            value={formik.values.reason}
            onChange={formik.handleChange}
            className="form-control"
            rows={5}
          />
        </div>
      </div>

      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn"
          disabled={formik.isSubmitting}
        >
          Submit
        </button>
      </div>
    </form>
  );
};

export default BulkOrderNegotiationForm;
