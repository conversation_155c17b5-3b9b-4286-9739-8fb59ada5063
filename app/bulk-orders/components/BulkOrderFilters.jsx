'use client';

import React from 'react';
import { useFormik } from 'formik';
import SelectField from '@/app/components/Inputs/SelectField';
import RangeDatePicker from '@/app/components/Inputs/RangeDatePicker';

const BulkOrderFilters = ({ filters, setFilters }) => {
  const statusOptions = [
    { value: '', label: 'All Status' },
    { value: 'pending', label: 'Pending' },
    { value: 'negotiating', label: 'Negotiating' },
    { value: 'approved', label: 'Approved' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'completed', label: 'Completed' },
  ];

  const formik = useFormik({
    initialValues: {
      buyer: filters.buyer || '',
      status: filters.status || '',
      startDate: filters.dateRange?.startDate || '',
      endDate: filters.dateRange?.endDate || ''
    },
    onSubmit: (values) => {
      setFilters({
        buyer: values.buyer,
        status: values.status,
        dateRange: {
          startDate: values.startDate,
          endDate: values.endDate
        }
      });
    }
  });

  const handleDateRangeChange = (range) => {
    formik.setFieldValue('startDate', range.startDate);
    formik.setFieldValue('endDate', range.endDate);
    setFilters(prev => ({
      ...prev,
      dateRange: range,
    }));
  };

  return (
    <form onSubmit={formik.handleSubmit} className="card p-4 mb-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <SelectField
          label="Buyer"
          placeholder="Select buyer"
          name="buyer"
          value={formik.values.buyer}
          onChange={(selected) => {
            formik.setFieldValue('buyer', selected.value);
            setFilters(prev => ({ ...prev, buyer: selected.value }));
          }}
          options={[
            { value: '', label: 'All Buyers' },
            // Add buyer options from API
          ]}
        />

        <SelectField
          label="Status"
          placeholder="Select status"
          name="status"
          value={formik.values.status}
          onChange={(selected) => {
            formik.setFieldValue('status', selected.value);
            setFilters(prev => ({ ...prev, status: selected.value }));
          }}
          options={statusOptions}
        />

        <div>
          <label className="form-label">Date Range</label>
          <RangeDatePicker
            value={filters.dateRange}
            onChange={handleDateRangeChange}
            placeholder="Select date range"
            nameFrom="startDate"
            nameTo="endDate"
            formik={formik}
          />
        </div>
      </div>
    </form>
  );
};

export default BulkOrderFilters;
