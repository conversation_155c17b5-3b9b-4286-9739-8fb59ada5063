'use client';

import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import FilterField from '../../components/table/FilterField';
import SortableItem from '../../components/table/SortableItem';
import SortIcon from '../../components/table/SortIcon';
import TableSkeleton from '../../components/table/TableSkeleton';
import BaseOffCanvas from '../../components/offCanvas/BaseOffCanvas';
import { Tooltip } from 'react-tooltip';
import CommonPagination from '../../components/table/CommonPagination';
import FileUpload from '../../components/Inputs/FileUpload';

// Info row component
const InfoRow = ({ label, value, className, valueClassName }) => (
  <div className={`flex flex-col gap-1 ${className}`}>
    <span className="text-sm text-gray-500/60 font-normal">{label}</span>
    <span className={`text-sm text-dark-500 font-medium ${valueClassName}`}>{value}</span>
  </div>
);

// Custom checkbox component
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const DispatchDetailModal = ({ isOpen, onClose, dispatch }) => {
  if (!dispatch) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      title={`Dispatch Details - ${dispatch.dispatchId}`}
      size="sm"
    >
      <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        {/* Dispatch Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Dispatch Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Dispatch ID" value={dispatch.dispatchId} />
            <InfoRow label="Order ID" value={dispatch.orderId} />
            <InfoRow label="Dispatch Date" value={new Date(dispatch.dispatchDate).toLocaleDateString()} />
            <InfoRow label="Quantity Dispatched" value={`${dispatch.quantityDispatched.toLocaleString()} units`} />
          </div>
        </div>
        <hr/>

        {/* Transport Information */}
        <div className="">
          <h4 className="font-semibold mb-3">Transport Information</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Transport Partner" value={dispatch.transportPartner} />
            <InfoRow label="Vehicle/Consignment No" value={dispatch.vehicleConsignmentNo} />
            <InfoRow label="Driver Name" value={dispatch.driverName || 'N/A'} />
            <InfoRow label="Driver Contact" value={dispatch.driverContact || 'N/A'} />
          </div>
        </div>
        <hr/>

        {/* Delivery Address */}
        <div className="">
          <h4 className="font-semibold mb-3">Delivery Address</h4>
          <InfoRow label="Address" value={dispatch.deliveryAddress} />
        </div>
        <hr/>

        {/* Delivery Status */}
        <div className="">
          <h4 className="font-semibold mb-3">Delivery Status</h4>
          <div className="grid grid-cols-2 gap-4">
            <InfoRow label="Current Status" value={dispatch.deliveryStatus} />
            <InfoRow label="Expected Delivery" value={dispatch.expectedDelivery ? new Date(dispatch.expectedDelivery).toLocaleDateString() : 'TBD'} />
          </div>
        </div>
        <hr/>

        {/* Proof of Delivery */}
        <div className="">
          <h4 className="font-semibold mb-3">Proof of Delivery (POD)</h4>
          {dispatch.podFiles && dispatch.podFiles.length > 0 ? (
            <div className="space-y-2">
              {dispatch.podFiles.map((file, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-white rounded border">
                  <span className="icon icon-file text-gray-500"></span>
                  <span className="text-sm">{file.name}</span>
                  <span className="text-xs text-gray-500">({file.size})</span>
                  <button className="text-primary-500 hover:text-primary-600 text-sm ml-auto">
                    Download
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <FileUpload 
              label="Upload POD"
              accept=".pdf"
              maxFileSizeMB={5}
              name="podFile"
              onChange={(file) => console.log(file)}
            />
            // <div className="text-center py-4">
            //   <p className="text-gray-500 text-sm mb-3">No POD uploaded yet</p>
            //   <button className="btn btn-outline-gray btn-sm">
            //     Upload POD
            //   </button>
            // </div>
          )}
        </div>
        <hr/>

        {/* Tracking Timeline */}
        <div className="">
          <h4 className="font-semibold mb-3">Tracking Timeline</h4>
          <div className="space-y-3">
            {dispatch.trackingTimeline.map((event, index) => (
              <div key={index} className="flex items-start gap-3 relative">
                <div className={`w-3 h-3 rounded-full mt-1 flex-shrink-0 ${
                  index === 0 ? 'bg-success-500' : 'bg-gray-300'
                }`}></div>
                {!event.isLast && (
                  <div className="absolute top-[20px] left-[5px] w-[2px] h-[calc(100%-8px)] bg-success-500"></div>
                )}
                <div className="flex-1">
                  <div className="flex justify-between flex-col items-start">
                    <p className="font-medium text-sm">{event.status}</p>
                    <p className="text-xs text-gray-400 mb-1">{event.location}</p>
                    <p className="text-xs text-gray-300">{new Date(event.timestamp).toLocaleString()}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      {/* Action Buttons */}
      <div className="flex justify-end gap-3 border-t border-border-color p-4">
        <button className="btn btn-primary">Update Status</button>
        <button className="btn btn-outline-gray">Upload POD</button>
        <button className="btn btn-outline-success">Track Delivery</button>
      </div>
    </BaseOffCanvas>
  );
};

const BulkDispatchDelivery = ({ isLoading = false }) => {
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [selectedDispatch, setSelectedDispatch] = useState(null);
  const [isDispatchDetailOpen, setIsDispatchDetailOpen] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    dispatchId: true,
    orderId: true,
    dispatchDate: true,
    deliveryAddress: true,
    quantityDispatched: true,
    transportPartner: true,
    vehicleConsignmentNo: true,
    deliveryStatus: true,
    podStatus: true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  // Sample data - in real app this would come from API
  const [dispatches] = useState([
    {
      id: 'DISP-001',
      dispatchId: 'DISP-001',
      orderId: 'BO-001',
      dispatchDate: '2024-01-18T08:00:00Z',
      deliveryAddress: '123 Industrial Park, Manufacturing District, City A, State 12345',
      quantityDispatched: 550,
      transportPartner: 'Express Logistics',
      vehicleConsignmentNo: 'EL-TRK-4567',
      deliveryStatus: 'In Transit',
      podStatus: 'Pending',
      expectedDelivery: '2024-01-20T17:00:00Z',
      driverName: 'John Driver',
      driverContact: '+1-555-0199',
      podFiles: [],
      trackingTimeline: [
        {
          status: 'Package picked up',
          location: 'Warehouse A',
          timestamp: '2024-01-18T08:00:00Z',
          isLast: false,
        },
        {
          status: 'In transit to destination',
          location: 'Highway Checkpoint',
          timestamp: '2024-01-18T14:30:00Z',
          isLast: true,
        }
      ]
    },
    {
      id: 'DISP-002',
      dispatchId: 'DISP-002',
      orderId: 'BO-002',
      dispatchDate: '2024-01-17T10:30:00Z',
      deliveryAddress: '456 Business Center, Commercial Zone, City B, State 67890',
      quantityDispatched: 200,
      transportPartner: 'Fast Freight',
      vehicleConsignmentNo: 'FF-VAN-8901',
      deliveryStatus: 'Delivered',
      podStatus: 'Uploaded',
      expectedDelivery: '2024-01-19T16:00:00Z',
      driverName: 'Mike Transporter',
      driverContact: '+1-555-0188',
      podFiles: [
        { name: 'delivery_receipt.pdf', size: '156KB' },
        { name: 'signature_proof.jpg', size: '89KB' }
      ],
      trackingTimeline: [
        {
          status: 'Package delivered',
          location: 'Customer Warehouse',
          timestamp: '2024-01-19T15:45:00Z',
          isLast: false,
        },
        {
          status: 'Out for delivery',
          location: 'Local Hub',
          timestamp: '2024-01-19T09:00:00Z',
          isLast: false,
        },
        {
          status: 'Package picked up',
          location: 'Warehouse B',
          timestamp: '2024-01-17T10:30:00Z',
          isLast: true,
        }
      ]
    },
    {
      id: 'DISP-003',
      dispatchId: 'DISP-003',
      orderId: 'BO-003',
      dispatchDate: '2024-01-16T14:15:00Z',
      deliveryAddress: '789 Tech Campus, Innovation District, City C, State 11111',
      quantityDispatched: 200,
      transportPartner: 'Reliable Movers',
      vehicleConsignmentNo: 'RM-TRUCK-2345',
      deliveryStatus: 'Failed',
      podStatus: 'N/A',
      expectedDelivery: '2024-01-18T12:00:00Z',
      driverName: 'Sam Carrier',
      driverContact: '+1-555-0177',
      podFiles: [],
      trackingTimeline: [
        {
          status: 'Delivery failed - address not accessible',
          location: 'Customer Location',
          timestamp: '2024-01-18T11:30:00Z',
          isLast: false,
        },
        {
          status: 'Out for delivery',
          location: 'Local Hub',
          timestamp: '2024-01-18T08:00:00Z',
          isLast: false,
        },
        {
          status: 'Package picked up',
          location: 'Warehouse C',
          timestamp: '2024-01-16T14:15:00Z',
          isLast: true,
        }
      ]
    },
    {
      id: 'DISP-004',
      dispatchId: 'DISP-004',
      orderId: 'BO-004',
      dispatchDate: '2024-01-20T09:00:00Z',
      deliveryAddress: '321 Retail Plaza, Shopping District, City D, State 22222',
      quantityDispatched: 300,
      transportPartner: 'Quick Ship',
      vehicleConsignmentNo: 'QS-VAN-6789',
      deliveryStatus: 'Dispatched',
      podStatus: 'Pending',
      expectedDelivery: '2024-01-22T14:00:00Z',
      driverName: 'Alex Delivery',
      driverContact: '+1-555-0166',
      podFiles: [],
      trackingTimeline: [
        {
          status: 'Package dispatched',
          location: 'Warehouse D',
          timestamp: '2024-01-20T09:00:00Z',
          isLast: true,
        }
      ]
    }
  ]);

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'dispatched':
        return 'bg-info-500/20 text-info-500';
      case 'in transit':
        return 'bg-purple-100 text-purple-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'uploaded':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'n/a':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const columns = [
    {
      name: 'Dispatch ID',
      selector: (row) => row.dispatchId,
      sortable: true,
      omit: !displayProperties.dispatchId,
    },
    {
      name: 'Order ID',
      selector: (row) => row.orderId,
      sortable: true,
      omit: !displayProperties.orderId,
    },
    {
      name: 'Dispatch Date',
      selector: (row) => row.dispatchDate,
      sortable: true,
      omit: !displayProperties.dispatchDate,
      cell: (row) => new Date(row.dispatchDate).toLocaleDateString(),
    },
    {
      name: 'Delivery Address',
      selector: (row) => row.deliveryAddress,
      sortable: true,
      omit: !displayProperties.deliveryAddress,
      cell: (row) => (
        <div className="text-sm max-w-[200px] truncate" title={row.deliveryAddress}>
          {row.deliveryAddress}
        </div>
      ),
    },
    {
      name: 'Quantity Dispatched',
      selector: (row) => row.quantityDispatched,
      sortable: true,
      omit: !displayProperties.quantityDispatched,
      cell: (row) => `${row.quantityDispatched.toLocaleString()} units`,
    },
    {
      name: 'Transport Partner',
      selector: (row) => row.transportPartner,
      sortable: true,
      omit: !displayProperties.transportPartner,
    },
    {
      name: 'Vehicle/Consignment No',
      selector: (row) => row.vehicleConsignmentNo,
      sortable: true,
      omit: !displayProperties.vehicleConsignmentNo,
    },
    {
      name: 'Delivery Status',
      selector: (row) => row.deliveryStatus,
      sortable: true,
      omit: !displayProperties.deliveryStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.deliveryStatus)}`}>
          {row.deliveryStatus}
        </span>
      ),
    },
    {
      name: 'POD',
      selector: (row) => row.podStatus,
      sortable: true,
      omit: !displayProperties.podStatus,
      cell: (row) => (
        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(row.podStatus)}`}>
          {row.podStatus}
        </span>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleViewDispatch(row)}
            data-tooltip-id="track-delivery-tooltip"
            data-tooltip-content="Track Delivery"
            className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
          >
            {/* Track Delivery */}
            <span className="icon icon-eye text-base" />
          </button>
          {row.deliveryStatus !== 'Delivered' && (
            <button 
              data-tooltip-id="update-status-tooltip"
              data-tooltip-content="Update Status"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
              >
              {/* Update Status */}
              <span className="icon icon-pencil-line text-base" />
            </button>
          )}
          {row.podStatus === 'Pending' && (
            <button 
              data-tooltip-id="upload-pod-tooltip"
              data-tooltip-content="Upload POD"
              className="flex justify-center items-center h-7 w-7 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
              >
              {/* Upload POD */}
              <span className="icon icon-upload text-base" />
            </button>
          )}

          <Tooltip id="track-delivery-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="update-status-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="upload-pod-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
      ignoreRowClick: true,
      // allowOverflow: true,
      // button: true,
      width: '150px',
    },
  ];

  const handleViewDispatch = (dispatch) => {
    setSelectedDispatch(dispatch);
    setIsDispatchDetailOpen(true);
  };

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const filteredDispatches = dispatches.filter((dispatch) =>
    dispatch.dispatchId.toLowerCase().includes(filterText.toLowerCase()) ||
    dispatch.orderId.toLowerCase().includes(filterText.toLowerCase()) ||
    dispatch.transportPartner.toLowerCase().includes(filterText.toLowerCase()) ||
    dispatch.vehicleConsignmentNo.toLowerCase().includes(filterText.toLowerCase())
  );

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">Bulk Dispatch & Delivery</h2>
          <span className="text-sm text-gray-300">
            {filteredDispatches.length} of {dispatches.length} dispatches
          </span>
        </div>
        <div className="flex gap-2">
          <button className="btn btn-outline-gray inline-flex items-center gap-2">
            <span className="icon icon-upload-simple font-normal text-lg" />
            Export
          </button>
        </div>
      </div>

      <div className="rounded-xl mb-0">
        <FilterField
          filterText={filterText}
          setFilterText={setFilterText}
          isSearchFilterOpen={isSearchFilterOpen}
          setIsSearchFilterOpen={setIsSearchFilterOpen}
          isDisplayMenuOpen={isDisplayMenuOpen}
          setIsDisplayMenuOpen={setIsDisplayMenuOpen}
          displayMenuRef={displayMenuRef}
        >
          <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
            <DndContext
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={items}
                strategy={verticalListSortingStrategy}
              >
                <ul className="space-y-1">
                  {items.map((key) => (
                    <SortableItem
                      key={key}
                      id={key}
                      value={key}
                      checked={displayProperties[key]}
                      onChange={() =>
                        setDisplayProperties((prev) => ({
                          ...prev,
                          [key]: !prev[key],
                        }))
                      }
                    />
                  ))}
                </ul>
              </SortableContext>
            </DndContext>
          </div>
        </FilterField>

        {isLoading ? (
          <TableSkeleton 
            rowsPerPage={8}
            columns={[
              { size: 'small', grow: 1 },
              { size: 'small' },
              { size: 'small' },
              { size: 'large', grow: 1 },
              { size: 'small' },
              { size: 'medium' },
              { size: 'medium' },
              { size: 'small' },
              { size: 'small' },
              { size: 'large' },
            ]}
            showCheckbox={true}
            cellHeight={4}
          />
        ) : (
          <DataTable
            columns={columns}
            data={filteredDispatches}
            customStyles={customStyles}
            pagination
            selectableRows
            fixedHeader={true}
            selectableRowsHighlight
            selectableRowsComponent={CustomCheckbox}
            onSelectedRowsChange={handleSelectedRowsChange}
            selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
            className="custom-table auto-height-table"
            sortIcon={<SortIcon sortDirection={sortDirection} />}
            onSort={handleSort}
            sortField={sortedField}
            defaultSortAsc={true}
            paginationPerPage={8}
            paginationRowsPerPageOptions={[8]}
            paginationComponentOptions={{
              rowsPerPageText: 'Rows per page:',
              rangeSeparatorText: 'of',
              selectAllRowsItem: false,
              noRowsPerPage: true,
            }}
            paginationComponent={(props) => (
              <CommonPagination
                selectedCount={props.selectedRows?.length}
                total={props.totalRows}
                page={props.currentPage}
                perPage={props.rowsPerPage}
                onPageChange={props.onChangePage}
              />
            )}
          />
        )}
      </div>

      {/* Dispatch Detail Modal */}
      <DispatchDetailModal
        isOpen={isDispatchDetailOpen}
        onClose={() => setIsDispatchDetailOpen(false)}
        dispatch={selectedDispatch}
      />
    </div>
  );
};

export default BulkDispatchDelivery;
