'use client';

import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import { Tooltip } from 'react-tooltip';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';


const BulkOrderRequestForm = ({ onClose, onSubmit }) => {
  const [items, setItems] = useState([{ productId: '', quantity: '', price: '' }]);

  const formik = useFormik({
    initialValues: {
      buyerId: '',
      deliveryDate: '',
      deliveryDateEnd: '',
      notes: '',
    },
    validationSchema: Yup.object({
      buyerId: Yup.string().required('Buyer is required'),
      deliveryDate: Yup.date().required('Delivery date is required'),
      deliveryDateEnd: Yup.date().required('Delivery date is required'),
      notes: Yup.string(),
    }),
    onSubmit: async (values) => {
      try {
        const orderData = {
          ...values,
          items: items.filter(item => item.productId && item.quantity),
        };
        // Submit order data to API
        onSubmit(orderData);
      } catch (error) {
        console.error('Error submitting bulk order:', error);
      }
    },
  });

  const handleAddItem = () => {
    setItems([...items, { productId: '', quantity: '', price: '' }]);
  };

  const handleRemoveItem = (index) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const handleItemChange = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    setItems(newItems);
  };

  return (
    <form onSubmit={formik.handleSubmit} className="h-full">
      <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        <div className="flex flex-col mb-4">
          <span className="form-label">
            Buyer
          </span>
          <SelectField
            label="Buyer"
            placeholder="Select buyer"
            name="buyerId"
            className="single-select"
            value={formik.values.buyerId}
            onChange={(selected) => formik.setFieldValue('buyerId', selected.value)}
            error={formik.touched.buyerId && formik.errors.buyerId}
            options={[
              // Add buyer options from API
              { value: '1', label: 'ABC Corp' },
              { value: '2', label: 'XYZ Ltd' },
            ]}
          />
        </div>
        <div className="mb-4 flex flex-col custom-datepicker">
          <label className="form-label">Delivery Date</label>
          <DatePicker
            selected={formik.values.deliveryDate}
            onChange={(date) => formik.setFieldValue('deliveryDate', date)}
            className="form-control calendar-icon"
            dateFormat="MM/dd/yyyy"
            placeholderText="Select delivery date"
            minDate={new Date()}
          />
          {formik.touched.deliveryDate && formik.errors.deliveryDate && (
            <div className="text-danger-500 text-sm mt-1">{formik.errors.deliveryDate}</div>
          )}
        </div>

        <div className="mb-4 mt-4 pt-4 border-t border-border-color">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-base font-medium">Items</h3>
            <button
              type="button"
              className="btn btn-sm btn-outline-primary flex items-center gap-2"
              onClick={handleAddItem}
            >
              <span className="icon icon-plus" />
              Add Item
            </button>
          </div>

          <div className="space-y-2">
            {items.map((item, index) => (
              <div key={index} className="card">
                <div className="flex items-center justify-between w-full mb-3">
                  <h3 className="text-sm font-medium">Item {index + 1}</h3>
                  {items.length > 1 && (
                    <>
                      <button
                        type="button"
                        className="flex justify-center items-center h-8 w-8 p-2 text-lg text-danger-500 hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
                        onClick={() => handleRemoveItem(index)}
                        data-tooltip-id="edit-tooltip"
                        data-tooltip-content="Remove"
                      >
                        <span className="icon icon-trash" />
                      </button>
                      <Tooltip
                        id="edit-tooltip"
                        className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
                      />
                    </>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-3 items-start">
                  <div className="col-span-full">
                    <span className="form-label">
                      Product
                    </span>
                    <SelectField
                      className="single-select"
                      placeholder="Select product"
                      value={item.productId}
                      onChange={(selected) => 
                        handleItemChange(index, 'productId', selected.value)
                      }
                      options={[
                        // Add product options from API
                        { value: '1', label: 'Product A' },
                        { value: '2', label: 'Product B' },
                      ]}
                    />
                  </div>
                  <InputField
                    id={`quantity-${index}`}
                    type="number"
                    label="Qty"
                    placeholder="Qty"
                    marginBottom="mb-0"
                    value={item.quantity}
                    onChange={(e) => 
                      handleItemChange(index, 'quantity', e.target.value)
                    }
                  />
                  <InputField
                    id={`price-${index}`}
                    type="number"
                    label="Price"
                    placeholder="Price"
                    rightText="$"
                    rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                    inputClassName="form-control !pl-7 !pr-4"
                    marginBottom="mb-0"
                    value={item.price}
                    onChange={(e) => 
                      handleItemChange(index, 'price', e.target.value)
                    }
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="col-span-full">
          <label htmlFor="notes" className="form-label">
            Notes
          </label>
          <textarea 
            id="notes" 
            name="notes" 
            placeholder="Add any special instructions or notes" 
            className="form-control" 
            value={formik.values.notes} 
            onChange={formik.handleChange} 
            error={formik.touched.notes && formik.errors.notes}
            rows={5}
          />
        </div>
      </div>

      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn"
          disabled={formik.isSubmitting}
        >
          Submit Order
        </button>
      </div>
    </form>
  );
};

export default BulkOrderRequestForm;
