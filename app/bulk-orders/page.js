'use client';

import React, { useState } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import AllBulkOrders from './components/AllBulkOrders';
import BulkOrderInquiries from './components/BulkOrderInquiries';
import PriceNegotiationTracker from './components/PriceNegotiationTracker';
import BulkDispatchDelivery from './components/BulkDispatchDelivery';
import BulkReturnsReconciliation from './components/BulkReturnsReconciliation';

export default function BulkOrders() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('all-orders');
  const [isLoading] = useState(false);

  const tabs = [
    { id: 'all-orders', label: 'All Bulk Orders', icon: 'icon-shopping-bag' },
    { id: 'inquiries', label: 'Bulk Order Inquiries', icon: 'icon-message-circle' },
    { id: 'negotiations', label: 'Price Negotiation Tracker', icon: 'icon-trending-up' },
    { id: 'dispatch', label: 'Bulk Dispatch & Delivery', icon: 'icon-truck' },
    { id: 'returns', label: 'Bulk Returns & Reconciliation', icon: 'icon-refresh-ccw' }
  ];

  const breadcrumbItems = [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Orders Management' },
    { label: 'Bulk Orders', href: '/bulk-orders' }
  ];
  const renderTabContent = () => {
    switch (activeTab) {
      case 'all-orders':
        return <AllBulkOrders isLoading={isLoading} />;
      case 'inquiries':
        return <BulkOrderInquiries isLoading={isLoading} />;
      case 'negotiations':
        return <PriceNegotiationTracker isLoading={isLoading} />;
      case 'dispatch':
        return <BulkDispatchDelivery isLoading={isLoading} />;
      case 'returns':
        return <BulkReturnsReconciliation isLoading={isLoading} />;
      default:
        return <AllBulkOrders isLoading={isLoading} />;
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}
        >
          {/* Header */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Bulk Orders</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            {/* <div className="flex gap-3">
              <button className="btn btn-outline-gray flex items-center gap-2">
                <span className="icon icon-upload-simple text-base" />
                Export
              </button>
            </div> */}
          </div>

          <div className="bg-white rounded-xl w-[calc(100% - 12px)]">
            {/* Navigation Tabs */}
            <div className="border-b border-border-color">
              <div className="flex overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-3 xl:px-6 py-3 xl:py-4 text-sm font-semibold whitespace-nowrap border-b-2 transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-500'
                        : 'border-transparent text-gray-300 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-4 xl:p-6">
              {renderTabContent()}
            </div>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}