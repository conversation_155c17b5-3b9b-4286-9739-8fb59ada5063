
'use client';
import React, { useState, useRef } from 'react';
import DataTable from 'react-data-table-component';
import SortIcon from '../table/SortIcon';
import CommonPagination from '../table/CommonPagination';
import { Tooltip } from 'react-tooltip';
import Swal from 'sweetalert2';
import { useRouter } from 'next/navigation';
import FilterField from '../table/FilterField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableItem from '../table/SortableItem';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
      }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const VendorDirectory = (data) => {
  const router = useRouter();
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [filterText, setFilterText] = useState('');
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);

  const [displayProperties, setDisplayProperties] = useState({
    // 'Vendor ID': false,
    // 'Vendor Name': true,
    'Contact Person': true,
    'Contact info': true,
    'Business Type': true,
    'State': true,
    'Status': true,
    'Onboarded Date': true,
    'Products': true,
  });

  const [items, setItems] = useState(Object.keys(displayProperties));

  const handleDragEnd = ({ active, over }) => {
    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);
        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);
        return newItems;
      });
    }
  };

  // Sample vendor data
  const [vendors, setVendors] = useState(data?.data || []);

  const getStatusBadge = (status) => {
    const statusConfig = {
      Active: 'bg-green-100 text-green-800',
      Inactive: 'bg-gray-100 text-gray-800',
      Pending: 'bg-yellow-100 text-yellow-800',
      Rejected: 'bg-red-100 text-red-800',
    };

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig[status] || 'bg-gray-100 text-gray-800'}`}>
        {status}
      </span>
    );
  };

  const handleAction = async (action, vendorId, vendorName) => {
    if (action === 'approve') {
      const result = await Swal.fire({
        title: 'Approve Vendor',
        html: `
          <div class="text-left">
            <p class="mb-2 text-sm">Are you sure you want to approve this vendor?</p>
            <div class="bg-surface-100 p-3 flex flex-col gap-1 rounded-lg">
              <p class="text-sm">Vendor ID: <strong>${vendorId}</strong></p>
              <p class="text-sm">Vendor Name: <strong>${vendorName}</strong></p>
            </div>
          </div>
        `,
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#10b857',
        cancelButtonColor: '#6B7280',
        confirmButtonText: 'Yes, Approve',
        cancelButtonText: 'Cancel',
        customClass: {
          popup: 'rounded-2xl',
          confirmButton: '!rounded-lg px-4 py-2',
          cancelButton: '!rounded-lg px-4 py-2'
        }
      });

      if (result.isConfirmed) {
        // Here you would typically make an API call to approve the vendor
        // Show success message
        Swal.fire({
          title: 'Approved!',
          text: `${vendorName} has been approved successfully.`,
          icon: 'success',
          confirmButtonColor: '#10B857',
          confirmButtonText: 'OK',
          customClass: {
            popup: 'rounded-xl',
            confirmButton: 'rounded-lg px-4 py-2'
          }
        });

        // Update vendor status in state
        setVendors(prev => prev.map(vendor =>
          vendor.id === vendorId ? { ...vendor, status: 'Active' } : vendor
        ));
      }
    } else if (action === 'view') {
      // Navigate to vendor profile review page with vendor ID
      router.push(`/vendor-profile-review?vendorId=${vendorId}`);
    } else {
      // console.log(`${action} action for vendor:`, vendorId);
      // Implement other action logic here
    }
  };

  const columns = [
    {
      name: 'Vendor ID',
      selector: (row) => row.vendor_code || '-',
      sortable: true,
      width: '100px',
      cell: (row) => <span className="font-medium">{row?.vendor_code || '-'}</span>,
    },
    {
      name: 'Vendor Name', //business name
      selector: (row) => row.business_name || '-',
      sortable: true,
      width: '180px',
    },
    {
      name: 'Contact Person',
      selector: (row) => row.first_name + row.last_name || '-',
      sortable: true,
      // width: '150px',
    },
    {
      name: 'Contact info',
      selector: (row) => row.phone || '-',
      sortable: true,
      width: '200px',
      cell: (row) => (
        <div className="flex flex-col">
          <a href={`mailto:${row?.email}`} className="text-gray-400 hover:underline">
            {row?.email || '-'}
          </a>
          <a href={`tel:${row.phone}`} className="text-gray-400 text-xs hover:underline">
            {row.phone || '-'}
          </a>
        </div>
      ),
    },
    {
      name: 'Business Type',
      selector: (row) => row.business_type || '-',
      sortable: true,
      // width: '120px',
    },
    {
      name: 'State',
      selector: (row) => row.state || '-',
      sortable: true,
      // width: '80px',
    },
    {
      name: 'Status',
      selector: (row) => row.status || '-',
      sortable: true,
      width: '100px',
      cell: (row) => getStatusBadge(row.status),
    },
    {
      name: 'Onboarded Date',
      selector: (row) => row.onboarding_status || '-',
      sortable: true,
      // width: '130px',
      cell: (row) => new Date(row.onboarding_status).toLocaleDateString(), //changw
    },
    {
      name: 'Products',
      selector: (row) => row.productCount || '-', //change
      sortable: true,
      // width: '90px',
    },
    {
      name: 'Actions',
      sortable: false,
      width: '200px',
      cell: (row) => (
        <div className="flex items-center gap-1">
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View Profile"
            onClick={() => handleAction('view', row.id)}
          >
            <span className="icon icon-eye text-base" />
          </button>

          {row.status === 'Pending' && (
            <>
              <button
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-green-500/10 hover:text-green-500 rounded-lg cursor-pointer transition-base"
                data-tooltip-id="approve-tooltip"
                data-tooltip-content="Approve"
                onClick={() => handleAction('approve', row.id, row.vendorName)}
              >
                <span className="icon icon-check-3 " />
              </button>
              <button
                className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
                data-tooltip-id="reject-tooltip"
                data-tooltip-content="Reject"
                onClick={() => handleAction('reject', row.id)}
              >
                <span className="icon icon-x text-base" />
              </button>
            </>
          )}

          {row.status === 'Active' && (
            <button
              className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-orange-500/10 hover:text-orange-500 rounded-lg cursor-pointer transition-base"
              data-tooltip-id="suspend-tooltip"
              data-tooltip-content="Suspend"
              onClick={() => handleAction('suspend', row.id)}
            >
              <span className="icon icon-pause text-base" />
            </button>
          )}

          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete"
            onClick={() => handleAction('delete', row.id)}
          >
            <span className="icon icon-trash text-base" />
          </button>

          <Tooltip id="view-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="approve-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="reject-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="suspend-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      ),
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        // textTransform: 'uppercase',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  return (
    <div className="rounded-xl">
      {/* Filter filed */}
      <FilterField
        filterText={filterText}
        setFilterText={setFilterText}
        isSearchFilterOpen={isSearchFilterOpen}
        setIsSearchFilterOpen={setIsSearchFilterOpen}
        isDisplayMenuOpen={isDisplayMenuOpen}
        setIsDisplayMenuOpen={setIsDisplayMenuOpen}
        displayMenuRef={displayMenuRef}
        filterByStatus={true}
      >
        <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
          <DndContext
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={items}
              strategy={verticalListSortingStrategy}
            >
              <ul className="space-y-1">
                {items.map((key) => (
                  <SortableItem
                    key={key}
                    id={key}
                    value={key}
                    checked={displayProperties[key]}
                    onChange={() =>
                      setDisplayProperties((prev) => ({
                        ...prev,
                        [key]: !prev[key],
                      }))
                    }
                  />
                ))}
              </ul>
            </SortableContext>
          </DndContext>
        </div>
      </FilterField>
      <DataTable
        columns={columns}
        data={data?.data}
        customStyles={customStyles}
        highlightOnHover
        selectableRowsComponent={CustomCheckbox}
        className="custom-table"
        pagination
        selectableRows
        fixedHeader={true}
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={handleSort}
        sortField={sortedField}
        defaultSortAsc={true}
        paginationPerPage={10}
        paginationRowsPerPageOptions={[10, 25, 50]}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
          noRowsPerPage: false,
        }}
        paginationComponent={(props) => (
          <CommonPagination
            selectedCount={props.selectedRows?.length}
            total={props.totalRows}
            page={props.currentPage}
            perPage={props.rowsPerPage}
            onPageChange={props.onChangePage}
          />
        )}
      />
    </div>
  );
};

export default VendorDirectory;
