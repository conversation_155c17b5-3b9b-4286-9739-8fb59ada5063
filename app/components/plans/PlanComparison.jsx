import React from 'react';

const COMPARISON_DATA = [
  {
    feature: 'Product Listings',
    starter: 'Limited (only for end-user visibility)',
    pinnacle: 'Unlimited',
  },
  {
    feature: 'Users / Team Members',
    starter: '2 Users',
    pinnacle: 'Unlimited',
  },
  {
    feature: 'Support Availability',
    starter: '24x5 - Email, Bot',
    pinnacle: '24x7 - Phone, Email, Bot',
  },
  {
    feature: 'Ad Campaign Discounts',
    starter: 'Available at Cost',
    pinnacle: '25% Discount on Advanced Campaigns',
  },
  {
    feature: 'Dedicated Account Manager',
    starter: 'No',
    pinnacle: 'Yes',
  },
  {
    feature: 'Promotional Opportunities',
    starter: 'No',
    pinnacle: 'Exclusive Featured Listings, Trending Priority',
  },
  {
    feature: 'Verified Seller Badge',
    starter: 'No',
    pinnacle: 'Yes',
  },
  {
    feature: 'AI Performance Reports',
    starter: 'Basic Reporting Tools',
    pinnacle: 'Advanced + Predictive Reporting',
  },
  {
    feature: 'Product Upload Assistance',
    starter: 'Available at Cost',
    pinnacle: '50% of Product Upload Done for You',
  },
  {
    feature: 'API Access (Inventory, Orders, Shipping)',
    starter: 'Available at Cost',
    pinnacle: 'Full Access',
  },
  {
    feature: 'Product Video Showcasing',
    starter: 'Available at Cost',
    pinnacle: 'Yes',
  },
  {
    feature: 'Custom Payment Terms',
    starter: 'Not Included',
    pinnacle: 'TBD / Negotiable',
  },
  {
    feature: 'Commission Model',
    starter: 'Higher Commission Rate (e.g., 10%)',
    pinnacle: 'Discounted Commission (e.g., 4-5%) OR Flat Subscription Fee',
  },
  {
    feature: 'Vendor Microsite / Brand Storefront',
    starter: 'Not Included',
    pinnacle: 'Included',
  },
  {
    feature: 'Inclusion in Brand Campaigns (e.g., Marriott, Hilton)',
    starter: 'No',
    pinnacle: 'Yes',
  },
  {
    feature: 'Product Recommendation Engine Integration',
    starter: 'Not Included',
    pinnacle: 'Integrated (Boost Cross-sell)',
  }
];

const PlanComparison = () => {
  const renderValue = (value) => {
    if (value === 'Yes') {
      return <span className="icon icon-check text-success-500 text-base"></span>;
    }
    if (value === 'No') {
      return <span className="icon icon-x text-danger-500 text-base"></span>;
    }
    if (value === 'Not Included') {
      return <span className="icon icon-x text-danger-500 text-base"></span>;
    }
    return value;
  };

  return (
    <div>
      <h2 className="text-lg lg:text-xl font-bold mb-4">Compare Plans</h2>
      <div className="card !p-0 overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-border-color">
              <th className="text-left py-4 px-4 text-sm font-semibold">
                Features
              </th>
              <th className="py-4 px-4 text-sm font-semibold text-center">
                Essential
              </th>
              <th className="py-4 px-4 text-sm font-semibold text-center">
                Pinnacle
                <span className="ml-2 bg-success-500/5 text-success-500 text-xs border border-success-500/20 font-medium px-2 py-1 rounded-full whitespace-nowrap">
                  Recommended
                </span>
              </th>
            </tr>
          </thead>
          <tbody>
            {COMPARISON_DATA.map((row, index) => (
              <tr
                key={index}
                className="border-b border-border-color last:border-0"
              >
                <td className="py-[18px] px-4 text-sm border-r border-border-color last:border-0 font-medium">
                  {row.feature}
                </td>
                <td className="py-[18px] px-4 text-sm border-r border-border-color last:border-0 text-center">
                  {renderValue(row.starter)}
                </td>
                <td className="py-[18px] px-4 text-sm border-r border-border-color last:border-0 text-center">
                  {renderValue(row.pinnacle)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PlanComparison;
