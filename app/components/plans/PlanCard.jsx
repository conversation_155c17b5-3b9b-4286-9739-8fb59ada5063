import React from 'react';

const PlanCard = ({
  name,
  description,
  price,
  features,
  isPopular,
  isEnterprise,
  isCurrentPlan,
  onSelect,
}) => {
  return (
    <div
      className={`hover:shadow-lg transition-base px-4 relative ${
        isPopular
          ? 'border-gradient gradient-border py-4 lg:py-4'
          : 'border border-border-color bg-white rounded-xl py-4 lg:py-6 '
      }`}
    >
      <div className="relative z-10">
        <div className="flex items-center justify-between gap-3 mb-4">
          <div className="flex flex-col gap-1">
            <h2 className="text-sm font-bold">{name}</h2>
            <p className="text-sm text-gray-400">{description}</p>
          </div>
          {isPopular && (
            <div className="bg-success-500/5 text-success-500 text-xs border border-success-500/20 font-semibold px-2 py-1 rounded-full whitespace-nowrap">
              Recommended
            </div>
          )}
          {isEnterprise && (
            <div className="bg-warning-500/5 text-warning-500 text-xs border border-warning-500/20 font-semibold px-3 py-1 rounded-full whitespace-nowrap">
              Enterprise
            </div>
          )}
        </div>

        <div className="flex items-baseline mb-4">
          {price === 'FREE' ? (
            <span className="text-2xl lg:text-3xl font-bold">FREE</span>
          ) : (
            <>
              <span className="text-2xl lg:text-3xl font-bold">${price}</span>
              <span className="text-sm text-gray-300 ml-1">per month</span>
            </>
          )}
        </div>

        <button
          className={`w-full btn ${isCurrentPlan ? 'btn-outline-gray' : 'btn-primary'} mb-5 lg:mb-8`}
          onClick={() => onSelect()}
        >
          {isCurrentPlan ? 'Current Plan' : 'Select Plan'}
        </button>

        <ul className="space-y-2 lg:space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2 text-sm">
              <span className="icon icon-check-circle-fill text-base text-success-500" />
              {feature}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default PlanCard;
