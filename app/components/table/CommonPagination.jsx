import React from 'react';

const CommonPagination = ({
  selectedCount,
  total,
  page,
  perPage = 10,
  onPageChange = {},
}) => {
  const totalPages = Math.ceil(total / perPage);

  return (
    <div className="flex items-center justify-between w-full pt-6">
      {selectedCount > 0 && (
        <div className="text-sm text-dark-500/60 font-medium">
          {selectedCount} of {total} row(s) selected
        </div>
      )}

      <div className="flex items-center gap-3 ml-auto">
        <div className="text-sm text-dark-500 font-medium">
          <span className="text-gray-500">Showing</span>{' '}
          {(page - 1) * perPage + 1}-{Math.min(page * perPage, total)}{' '}
          <span className="text-gray-500">of</span> {total}
        </div>

        <div className="flex items-center bg-gray-500/10 rounded-lg overflow-hidden">
          <button
            onClick={() => onPageChange(1)}
            disabled={page === 1}
            className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
          >
            <span className="icon icon-caret-double-left text-base align-middle" />
          </button>
          <button
            onClick={() => onPageChange(Math.max(page - 1, 1))}
            disabled={page === 1}
            className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
          >
            <span className="icon icon-caret-left text-base align-middle" />
          </button>
          <button
            onClick={() => onPageChange(Math.min(page + 1, totalPages))}
            disabled={page === totalPages}
            className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
          >
            <span className="icon icon-caret-right text-base align-middle" />
          </button>
          <button
            onClick={() => onPageChange(totalPages)}
            disabled={page === totalPages}
            className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
          >
            <span className="icon icon-caret-double-right text-base align-middle" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default CommonPagination;
