'use client';

import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import TableSkeleton from './TableSkeleton';
import SortIcon from './SortIcon';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const OrdersTable = ({ data = [], isLoading = false, paginationPerPage = 5 }) => {
  const skeletonColumns = [
    { size: 'small', grow: 1 },
    { size: 'small' },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small' },
    { size: 'small' },
    { size: 'small' },
    { size: 'small' },
  ];
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);

  const columns = [
    {
      name: 'Purchase Order ID',
      selector: (row) => row?.purchaseOrderId || '-',
      sortable: true,
    },
    {
      name: 'Order Type',
      selector: (row) => row?.orderType || '-',
      sortable: true,
    },
    {
      name: 'Payment Method',
      selector: (row) => row?.paymentMethod || '-',
      sortable: true,
    },
    {
      name: 'Shipping Method',
      selector: (row) => row?.shippingMethod || '-',
      sortable: true,
    },
    {
      name: 'Order Status',
      selector: (row) => row?.status || '-',
      sortable: true,
      cell: (row) => (
        <span
          className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold text-nowrap ${getStatusColor(row?.status)}`}
        >
          {row?.status || '-'}
        </span>
      ),
    },
    {
      name: 'Total',
      selector: (row) => row?.total || '-',
      sortable: true,
      cell: (row) => <span className="font-bold">${row?.total || '-'}</span>,
    },
    {
      name: 'Quantity',
      selector: (row) => row?.quantity || '-',
      sortable: true,
    },
    {
      name: 'Date / Time',
      selector: (row) => row?.date || '-',
      sortable: true,
      cell: (row) => (
        <div className="flex items-start flex-col gap-[1px]">
          <span className="text-sm">{row?.date || '-'}</span>
          <span className="text-xs text-gray-400">{row?.time || '-'}</span>
        </div>
      ),
    },
  ];

  const getStatusColor = (status) => {
    const colors = {
      Processing: 'bg-info-500/10 text-info-500',
      Delivered: 'bg-success-500/10 text-success-500',
      Pending: 'bg-warning-500/10 text-warning-500',
      Cancelled: 'bg-danger-500/10 text-danger-500',
      Returned: 'bg-gray-500/10 text-gray-500',
      'New Order': 'bg-purple-500/10 text-purple-500',
    };
    return colors[status] || 'bg-gray-100 text-gray-600';
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px', // override the cell padding for head cells
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px', // override the cell padding for data cells
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '48px',
      },
    },
  };
  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

   // Custom sorting handler
  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  return (
    <>
    {selectedRows.length > 0 && (
        <div className="bg-white p-2 flex items-center justify-between border-l-1 border-r-1 border-b-1 border-border-color">
          <span className="text-danger-500 text-sm font-medium">
            {selectedRows.length} items selected
          </span>
          <div className="flex gap-2">
            <button type='button' className="btn btn-danger inline-flex gap-1 items-center !px-3 !py-1.5 !rounded-md">
              <span className="icon icon-trash text-base" />
              Delete Selected
            </button>
          </div>
        </div>
      )}
    {isLoading ? (
      <TableSkeleton 
        rowsPerPage={paginationPerPage}
        columns={skeletonColumns}
        showCheckbox={true}
        cellHeight={4}
      />
    ) : (
        <DataTable
        columns={columns}
        data={data}
        customStyles={customStyles}
        pagination
        selectableRows
        fixedHeader={true}
        selectableRowsHighlight
        selectableRowsComponent={CustomCheckbox}
        onSelectedRowsChange={handleSelectedRowsChange}
        selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
        className="custom-table"
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={handleSort}
        sortField={sortedField}
        defaultSortAsc={true}
        paginationPerPage={8}
        paginationRowsPerPageOptions={[8]}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
          noRowsPerPage: true,
        }}
        paginationComponent={(props) => (
          <div className="flex items-center justify-between w-full pt-6">
            <div className="text-sm text-dark-500/60 font-medium">
              {selectedRows.length} of {data.length} row(s) selected
            </div>

            <div className="flex items-center gap-3 ml-auto">
              <div className="text-sm text-dark-500 font-medium">
                <span className="text-gray-500">Showing</span>{' '}
                {(props.currentPage - 1) * props.rowsPerPage + 1}-
                {Math.min(props.currentPage * props.rowsPerPage, data.length)}{' '}
                <span className="text-gray-500">of</span> {data.length}
              </div>
              <div className="flex items-center gap-2 bg-dark-500/5 rounded-lg overflow-hidden">
                <button
                  onClick={() => props.onChangePage(1)}
                  disabled={props.currentPage === 1}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-double-left text-base align-middle" />
                </button>
                <button
                  onClick={() => props.onChangePage(props.currentPage - 1)}
                  disabled={props.currentPage === 1}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-left text-base align-middle" />
                </button>
                <button
                  onClick={() => props.onChangePage(props.currentPage + 1)}
                  disabled={props.currentPage === props.totalPages}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-right text-base align-middle" />
                </button>
                <button
                  onClick={() => props.onChangePage(props.totalPages)}
                  disabled={props.currentPage === props.totalPages}
                  className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                >
                  <span className="icon icon-caret-double-right text-base align-middle" />
                </button>
              </div>
            </div>
          </div>
        )}
        />
      )}
    </>
  );
};

export default OrdersTable;
