'use client';
import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import DataTable from 'react-data-table-component';
import Image from 'next/image';
import { Tooltip } from 'react-tooltip';
import { useRouter } from 'next/navigation';
import { archieveProductAsync } from '@/store/api/productApi';
import { useDispatch } from 'react-redux';
import CommonPagination from './CommonPagination';
import SortIcon from './SortIcon';
import { setProductId } from '@/store/actions/userActions';
import { ProductType } from '@/utils/constant';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

// Add this component at the top of the file
const VariantDropdown = ({ isVisible, anchorEl, variants, checkKeys }) => {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef(null);

  useEffect(() => {
    if (isVisible && anchorEl) {
      const updatePosition = () => {
        const rect = anchorEl.getBoundingClientRect();
        const scrollY = window.scrollY;
        const scrollX = window.scrollX;

        let top = rect.bottom + scrollY;
        let left = rect.left + scrollX;

        if (dropdownRef.current) {
          const dropdownHeight = dropdownRef.current.offsetHeight;
          const viewportHeight = window.innerHeight;
          const bottomSpace = viewportHeight - rect.bottom;

          if (bottomSpace < dropdownHeight) {
            top = rect.top + scrollY - dropdownHeight;
          }
        }

        setPosition({ top, left });
      };

      updatePosition();
      window.addEventListener('scroll', updatePosition, true);
      window.addEventListener('resize', updatePosition);

      return () => {
        window.removeEventListener('scroll', updatePosition, true);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isVisible, anchorEl]);

  if (!isVisible) return null;

  return createPortal(
    <div
      ref={dropdownRef}
      className="fixed z-50"
      style={{
        top: `${position.top}px`,
        left: `${position.left}px`,
        transform: 'translate3d(0, 0, 0)',
      }}
    >
      <div className="w-[296px] p-3 bg-white rounded-2xl shadow-lg border border-border-color max-h-64 overflow-y-auto">
        {checkKeys?.is_variation && variants?.length > 0 ? (
          variants?.map((variant, index) => (
            <div
              key={index}
              className="flex items-center justify-between px-4 py-2 hover:bg-gray-50 not-last:border-b border-surface-100"
            >
              <span className="text-sm text-dark-500">
                {variant.name || '-'}
              </span>
            </div>
          ))
        ) : (
          <span className="text-sm text-dark-300">No Variants Found</span>
        )}
      </div>
    </div>,
    document.body
  );
};

const ProductTable = ({
  data,
  filterText,
  selectedStatus,
  DefaultImage,
  getProducts,
  displayProperties,
  activeTab,
  pagination,
  setPagination,
}) => {
  const router = useRouter();
  const [selectedRows, setSelectedRows] = useState([]);
  const dispatch = useDispatch();
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);

  const columns = [
    displayProperties?.product_name && {
      name: 'Name/Category',
      selector: (row) => row?.name || '-',
      sortable: true,
      width: '260px',
      cell: (row) => (
        <div className="flex items-center gap-3 py-1.5">
          <Image
            src={row?.image || DefaultImage}
            width={48}
            height={48}
            alt={row.name}
            className="rounded-lg"
          />
          <div className="flex flex-col gap-1 text-left">
            <p className="font-medium text-sm text-dark-500">
              {row?.name || '-'}
            </p>
            <p className="font-medium text-xs text-gray-400 capitalize">
              {row?.category?.name || '-'}
            </p>
          </div>
        </div>
      ),
    },
    displayProperties?.brand && {
      name: 'Brand/Model',
      selector: (row) => row?.brand || '-',
      sortable: true,
      // width: '240px',
      cell: (row) => (
        <div className="flex flex-col gap-1 text-left">
          <p className="font-medium text-sm text-dark-500">
            {row?.brand_manufacturer?.name || '-'}
          </p>
          <p className="font-medium text-sm text-gray-400">
            <span className="font-bold text-dark-500"></span>
            {row?.model_number || '-'}
          </p>
        </div>
      ),
    },
    displayProperties?.id && {
      name: 'ID',
      selector: (row) => row?.id || '-',
      sortable: true,
      width: '80px',
      cell: (row) => <span className="font-medium">{row?.id || '-'}</span>,
    },
    displayProperties?.sku && {
      name: 'SKU',
      selector: (row) => row?.sku || '-',
      sortable: true,
    },
    displayProperties?.variants && {
      name: 'Variants',
      selector: (row) => row?.variants,
      sortable: true,
      cell: (row) => {
        const [isHovered, setIsHovered] = useState(false);
        const buttonRef = useRef(null);

        return (
          <div
            className="relative"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <button
              ref={buttonRef}
              className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-lg text-sm hover:bg-gray-100 text-nowrap transition-base"
            >
              <span>{row?.variants} variants</span>
              <span
                className={`icon icon-caret-down text-xs transition-transform duration-200 ${
                  isHovered ? 'rotate-180' : ''
                }`}
              ></span>
            </button>

            <VariantDropdown
              isVisible={isHovered}
              anchorEl={buttonRef.current}
              variants={row?.variantOptions}
              checkKeys={row}
            />
          </div>
        );
      },
    },
    displayProperties?.status && {
      name: 'Status',
      selector: (row) => row?.status || '-',
      sortable: true,
      width: '130px',
      cell: (row) => {
        const statusStyles = {
          Published: 'bg-success-500/10 text-success-600',
          'Pending Approval': 'bg-danger-500/10 text-danger-500',
          Archive: 'bg-warning-500/10 text-warning-600',
          Draft: 'bg-gray-500/10 text-gray-500',
        };

        return (
          <span
            className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold text-nowrap ${
              statusStyles[row?.status] || 'bg-gray-500/10 text-gray-500'
            }`}
          >
            {row?.status || '-'}
          </span>
        );
      },
    },
    displayProperties?.price && {
      name: 'Price',
      selector: (row) => row?.base_price || '-',
      sortable: true,
      cell: (row) => (
        <span className="font-medium">
          <span>{!row.base_price ? '-' : `$${row.base_price}`}</span>
        </span>
      ),
    },
    displayProperties?.quantity && {
      name: 'Quantity',
      selector: (row) => row?.qty || '-',
      sortable: true,
    },
    activeTab !== 'Archive' && {
      name: 'Actions',
      width: '150px',
      cell: (row) => (
        <div className="flex gap-3">
          <button
            className="flex justify-center items-center h-9 w-9 p-2 text-lg hover:bg-gray-500/10 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="action-tooltip"
            data-tooltip-content="View"
          >
            <span className="icon icon-eye text-gray-500" />
          </button>
          <button
            className="flex justify-center items-center h-9 w-9 p-2 text-lg hover:bg-gray-500/10 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="action-tooltip"
            data-tooltip-content="Edit"
            onClick={() => {
              dispatch(setProductId(row.editId));
              if (row?.productType == ProductType?.RETAIL) {
                router.push(`/manage-products/product/edit`);
              }
            }}
          >
            <span className="icon icon-pencil-line text-gray-500" />
          </button>

          <button
            className="flex justify-center items-center h-9 w-9 p-2 text-lg hover:bg-gray-500/10 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="action-tooltip"
            data-tooltip-content="Archive"
            onClick={async () => {
              const { payload } = await dispatch(
                archieveProductAsync({ productId: row?.archieveId })
              );
              if (payload?.status) getProducts();
            }}
          >
            <span className="icon icon-box-arrow-down text-gray-500" />
          </button>

          <Tooltip
            id="action-tooltip"
            place="top"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
        </div>
      ),
    },
  ];

  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };
  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };
  return (
    <>
      <div className="flex flex-col">
        <DataTable
          columns={columns}
          data={data}
          pagination
          selectableRows
          onSelectedRowsChange={handleSelectedRowsChange}
          customStyles={customStyles}
          selectableRowsComponent={CustomCheckbox}
          selectableRowsComponentProps={{
            indeterminate: (isIndeterminate) => isIndeterminate,
          }}
          selectableRowsHighlight
          fixedHeader={true}
          highlightOnHover
          pointerOnHover
          className="custom-table"
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationPerPage={10}
          paginationRowsPerPageOptions={[10]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={() => (
            <CommonPagination
              selectedCount={selectedRows?.length}
              total={pagination.total}
              page={pagination.page}
              perPage={pagination.perPage}
              onPageChange={(newPage) =>
                setPagination((prev) => ({ ...prev, page: newPage }))
              }
            />
          )}
        />
      </div>
    </>
  );
};

export default ProductTable;
