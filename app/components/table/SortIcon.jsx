import React from 'react';

const SortIcon = ({ sortDirection }) => {
  return (
    <div className="flex flex-col ml-1.5 mt-1">
      <span
        className={`block w-2 h-2 ${
          sortDirection === 'asc' ? 'opacity-100' : 'opacity-50'
        }`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 8 4"
          fill="currentColor"
        >
          <path d="M4 0l4 4H0z" />
        </svg>
      </span>
      <span
        className={`block w-2 h-2 ${
          sortDirection === 'desc' ? 'opacity-100' : 'opacity-50'
        }`}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 8 4"
          fill="currentColor"
        >
          <path d="M4 4L0 0h8z" />
        </svg>
      </span>
    </div>
  );
};

export default SortIcon;