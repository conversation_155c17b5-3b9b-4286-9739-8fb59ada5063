'use client';

import React, { useState, useMemo } from 'react';
import DataTable from 'react-data-table-component';
import { format } from 'date-fns';
import { Tooltip } from 'react-tooltip';
import SortIcon from './SortIcon';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';


const BulkOrdersTable = ({ data, onNegotiate, onViewDetails }) => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);

  const columns = useMemo(
    () => [
      {
        name: 'Order ID',
        selector: row => row.id || '-',
        sortable: true,
      },
      {
        name: 'Buyer',
        selector: row => row.buyer || '-',
        sortable: true,
      },
      {
        name: 'Items',
        selector: row => row.items || '-',
        cell: row => (
          <div className="py-2">
            {row.items.map((item, index) => (
              <div key={index} className="text-sm">
                {item.name} ({item.quantity} units)
              </div>
            ))}
          </div>
        ),
      },
      {
        name: 'Total Units',
        selector: row => row.totalUnits || '-',
        sortable: true,
      },
      {
        name: 'Price',
        selector: row => row.price || '-',
        cell: row => `$${row.price.toLocaleString()}`,
        sortable: true,
      },
      {
        name: 'Status',
        selector: row => row.status || '-',
        cell: row => (
          <div className="flex items-center gap-2">
            <span className={`status-badge ${row.status}`}>
              {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
            </span>
          </div>
        ),
        sortable: true,
      },
      {
        name: 'Delivery Date',
        selector: row => row.deliveryDate || '-',
        cell: row => format(new Date(row.deliveryDate), 'MMM dd, yyyy'),
        sortable: true,
      },
      {
        name: 'Actions',
        cell: row => (
          <div className="flex items-center gap-2">
            <button
              className="flex justify-center items-center h-9 w-9 p-2 text-lg hover:bg-gray-500/10 rounded-lg cursor-pointer transition-base"
              data-tooltip-id="action-tooltip"
              data-tooltip-content="Negotiate"
              onClick={() => onNegotiate(row)}
            >
              <span className="icon icon-pencil-line text-gray-500" />
            </button>
            <button
              className="flex justify-center items-center h-9 w-9 p-2 text-lg hover:bg-gray-500/10 rounded-lg cursor-pointer transition-base"
              data-tooltip-id="action-tooltip"
              data-tooltip-content="View"
              onClick={() => onViewDetails(row)}
            >
              <span className="icon icon-eye text-gray-500" />
            </button>
            <Tooltip
              id="action-tooltip"
              place="top"
              className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
            />
          </div>
        ),
      },
    ],
    [onNegotiate, onViewDetails]
  );
  const handleSelectedRowsChange = ({ selectedRows }) => {
    setSelectedRows(selectedRows);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };
  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };
  return (
    <DataTable
      columns={columns}
      data={data}
      pagination
      selectableRows
      onSelectedRowsChange={handleSelectedRowsChange}
      customStyles={customStyles}
      selectableRowsComponent={CustomCheckbox}
      selectableRowsComponentProps={{
        indeterminate: (isIndeterminate) => isIndeterminate,
      }}
      selectableRowsHighlight
      responsive
      fixedHeader={true}
      highlightOnHover
      pointerOnHover
      className="custom-table"
      sortIcon={<SortIcon sortDirection={sortDirection} />}
      onSort={handleSort}
      sortField={sortedField}
      defaultSortAsc={true}
      paginationPerPage={8}
      paginationRowsPerPageOptions={[8]}
      paginationComponentOptions={{
        rowsPerPageText: 'Rows per page:',
        rangeSeparatorText: 'of',
        selectAllRowsItem: false,
        noRowsPerPage: true,
      }}
      paginationComponent={(props) => (
        <div className="flex items-center justify-between w-full pt-6">
          <div className="text-sm text-dark-500/60 font-medium">
            {selectedRows.length} of {data.length} row(s) selected
          </div>

          <div className="flex items-center gap-3 ml-auto">
            <div className="text-sm text-dark-500 font-medium">
              <span className="text-gray-500">Showing</span>{' '}
              {(props.currentPage - 1) * props.rowsPerPage + 1}-
              {Math.min(props.currentPage * props.rowsPerPage, data.length)}{' '}
              <span className="text-gray-500">of</span> {data.length}
            </div>
            <div className="flex items-center gap-2 bg-dark-500/5 rounded-lg overflow-hidden">
              <button
                onClick={() => props.onChangePage(1)}
                disabled={props.currentPage === 1}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-double-left text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.currentPage - 1)}
                disabled={props.currentPage === 1}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-left text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.currentPage + 1)}
                disabled={props.currentPage === props.totalPages}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-right text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.totalPages)}
                disabled={props.currentPage === props.totalPages}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-double-right text-base align-middle" />
              </button>
            </div>
          </div>
        </div>
      )}
    />
  );
};

export default BulkOrdersTable;
