'use client';

import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import TableSkeleton from './TableSkeleton';
import SortIcon from './SortIcon';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

// const SortIcon = ({ sortDirection }) => (
//   <div className="flex flex-col ml-1">
//     <span className={`icon icon-caret-up block text-[10px] ${sortDirection === 'asc' ? 'text-primary-500' : 'text-gray-400'}`} />
//     <span className={`icon icon-caret-down block text-[10px] ${sortDirection === 'desc' ? 'text-primary-500' : 'text-gray-400'} -mt-1`} />
//   </div>
// );

const OngoingOrdersTable = ({ data = [], isLoading = false, paginationPerPage = 5 }) => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const skeletonColumns = [
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
  ];

  const columns = [
    {
      name: 'Expected Ship Date',
      selector: (row) => row?.shipDate || '-',
      sortable: true,
    },
    {
      name: 'Order Type',
      selector: (row) => row?.orderType || '-',
      sortable: true,
    },
    {
      name: 'Payment Status',
      selector: (row) => row?.paymentStatus || '-',
      sortable: true,
    },
    {
      name: 'Shipping Method',
      selector: (row) => row?.shippingMethod || '-',
      sortable: true,
    },
    {
      name: 'Status',
      selector: (row) => row?.status || '-',
      sortable: true,
      cell: (row) => (
        <span className="text-info-600 bg-info-500/10 px-2 py-1 rounded-lg text-xs">
          {row.status || '-'}
        </span>
      ),
    },
    {
      name: 'Total',
      selector: (row) => row?.total || '-',
      sortable: true,
      cell: (row) => <span className="font-medium">${row.total}</span>,
    },
    {
      name: 'Quantity',
      selector: (row) => row?.quantity || '-',
      sortable: true,
      cell: (row) => <span className="font-medium">{row.quantity}</span>,
    },
    {
      name: 'Actions',
      cell: (row) => (
        <button className="btn btn-outline-gray text-nowrap !font-medium">
          Mark as shipped
        </button>
      ),
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  return (
    <>
      {isLoading ? (
        <TableSkeleton 
          rowsPerPage={paginationPerPage}
          columns={skeletonColumns}
          showCheckbox={true}
          cellHeight={4}
        />
      ) : (
        <DataTable
          columns={columns}
          data={data}
          customStyles={customStyles}
          // selectableRows
          highlightOnHover
          selectableRowsComponent={CustomCheckbox}
          className="custom-table ongoing-orders-table"
          pagination
          sortIcon={<SortIcon sortDirection={sortDirection} />}
          onSort={handleSort}
          sortField={sortedField}
          defaultSortAsc={true}
          paginationPerPage={8}
          paginationRowsPerPageOptions={[8]}
          paginationComponentOptions={{
            rowsPerPageText: 'Rows per page:',
            rangeSeparatorText: 'of',
            selectAllRowsItem: false,
            noRowsPerPage: true,
          }}
          paginationComponent={(props) => (
            <div className="flex items-center justify-between w-full pt-6">
              <div className="text-sm text-dark-500/60 font-medium">
                {selectedRows.length} of {data.length} row(s) selected
              </div>

              <div className="flex items-center gap-3 ml-auto">
                <div className="text-sm text-dark-500 font-medium">
                  <span className="text-gray-500">Showing</span>{' '}
                  {(props.currentPage - 1) * props.rowsPerPage + 1}-
                  {Math.min(props.currentPage * props.rowsPerPage, data.length)}{' '}
                  <span className="text-gray-500">of</span> {data.length}
                </div>
                <div className="flex items-center gap-2 bg-dark-500/5 rounded-lg overflow-hidden">
                  <button
                    onClick={() => props.onChangePage(1)}
                    disabled={props.currentPage === 1}
                    className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                  >
                    <span className="icon icon-caret-double-left text-base align-middle" />
                  </button>
                  <button
                    onClick={() => props.onChangePage(props.currentPage - 1)}
                    disabled={props.currentPage === 1}
                    className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                  >
                    <span className="icon icon-caret-left text-base align-middle" />
                  </button>
                  <button
                    onClick={() => props.onChangePage(props.currentPage + 1)}
                    disabled={props.currentPage === props.totalPages}
                    className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                  >
                    <span className="icon icon-caret-right text-base align-middle" />
                  </button>
                  <button
                    onClick={() => props.onChangePage(props.totalPages)}
                    disabled={props.currentPage === props.totalPages}
                    className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
                  >
                    <span className="icon icon-caret-double-right text-base align-middle" />
                  </button>
                </div>
              </div>
            </div>
          )}
        />
      )}
    </>
  );
};

export default OngoingOrdersTable;
