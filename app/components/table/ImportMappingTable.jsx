'use client';
import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import <PERSON>Field from '../Inputs/SelectField';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));
CustomCheckbox.displayName = 'CustomCheckbox';

const ImportMappingTable = () => {
  const [selectedRows, setSelectedRows] = useState([]);
  const columns = [
    {
      name: 'Import to Hubsups',
      selector: row => row.hubsupsField,
      grow: 2,
    },
    {
      name: 'Column Heading',
      selector: row => row.columnHeading,
      grow: 2,
      cell: row => (
        <SelectField
          name={`column-${row.id}`}
          value={row.columnHeading}
          onChange={(value) => handleColumnChange(row, value)}
          options={availableColumns.map(col => ({ value: col, label: col }))}
          placeholder="Select Column"
          className="single-select w-full"
        />
      ),
    },
  ];
  const handleColumnChange = (row, value) => {
    // Update the data with the new column selection
    const updatedData = data.map(item => 
      item.id === row.id ? { ...item, columnHeading: value } : item
    );
    setData(updatedData);
  };

  const data = [
    { id: 1, hubsupsField: 'Product Title', columnHeading: 'Title', selected: false },
    { id: 2, hubsupsField: 'Product Description', columnHeading: 'Description', selected: false },
    { id: 3, hubsupsField: 'Product Category', columnHeading: 'Category', selected: false },
    { id: 4, hubsupsField: 'Product Style', columnHeading: 'Style', selected: false },
    { id: 5, hubsupsField: 'Industry Tags', columnHeading: 'Tags', selected: false },
    { id: 6, hubsupsField: 'Brand/Manufacturer Number', columnHeading: 'Manufacturer Number', selected: false },
    { id: 7, hubsupsField: 'Price', columnHeading: 'Pricing', selected: false },
    { id: 8, hubsupsField: 'Quantity', columnHeading: 'Inventory', selected: false },
    { id: 9, hubsupsField: 'SKU', columnHeading: 'SKU', selected: false },
    { id: 10, hubsupsField: 'Country of Origin', columnHeading: 'Country From', selected: false },
    { id: 11, hubsupsField: 'Image URL', columnHeading: 'Product Image URL', selected: false },
  ];

  const availableColumns = [
    'Title',
    'Description',
    'Category',
    'Style',
    'Tags',
    'Manufacturer Number',
    'Pricing',
    'Inventory',
    'SKU',
    'Country From',
    'Product Image URL',
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px', // override the cell padding for head cells
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px', // override the cell padding for data cells
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '48px',
      },
    },
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      customStyles={customStyles}
      pagination
      selectableRows
      fixedHeader={true}
      selectableRowsHighlight
      selectableRowsComponent={CustomCheckbox}
      selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
      className="custom-table auto-height-table upload-columns-table"
      paginationPerPage={8}
      paginationRowsPerPageOptions={[8]}
      paginationComponentOptions={{
        rowsPerPageText: 'Rows per page:',
        rangeSeparatorText: 'of',
        selectAllRowsItem: false,
        noRowsPerPage: true,
      }}
      paginationComponent={(props) => (
        <div className="flex items-center justify-between w-full pt-6">
          <div className="text-sm text-dark-500/60 font-medium">
            {selectedRows.length} of {data.length} row(s) selected
          </div>

          <div className="flex items-center gap-3 ml-auto">
            <div className="text-sm text-dark-500 font-medium">
              <span className="text-gray-500">Showing</span>{' '}
              {(props.currentPage - 1) * props.rowsPerPage + 1}-
              {Math.min(props.currentPage * props.rowsPerPage, data.length)}{' '}
              <span className="text-gray-500">of</span> {data.length}
            </div>
            <div className="flex items-center gap-2 bg-dark-500/5 rounded-lg overflow-hidden">
              <button
                onClick={() => props.onChangePage(1)}
                disabled={props.currentPage === 1}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-double-left text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.currentPage - 1)}
                disabled={props.currentPage === 1}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-left text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.currentPage + 1)}
                disabled={props.currentPage === props.totalPages}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-right text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.totalPages)}
                disabled={props.currentPage === props.totalPages}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-double-right text-base align-middle" />
              </button>
            </div>
          </div>
        </div>
      )}
    />
  );
};

export default ImportMappingTable;