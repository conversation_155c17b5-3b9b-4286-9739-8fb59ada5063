import React from 'react';
import DataTable from 'react-data-table-component';
import Image from 'next/image';
import { Tooltip } from 'react-tooltip';
import InputField from '../Inputs/InputField';
import DefaultImage from '@/public/images/default-Image.jpg';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));
CustomCheckbox.displayName = 'CustomCheckbox';

const ComboBuilderTable = ({ data = [], formik }) => {
  const handleQtyChange = (value, index, price) => {
    const numericQty = Number(value);
    formik.setFieldValue(`selectedProducts[${index}].qty`, numericQty);
    formik.setFieldValue(
      `selectedProducts[${index}].sku_total_price`,
      numericQty * price
    );
  };

  const handleDeleteProduct = (productSkuId) => {
    const currentProducts = formik.values?.selectedProducts || [];
    const updatedProducts = currentProducts.filter(
      (product) => product.product_sku_id !== productSkuId
    );

    formik.setFieldValue('selectedProducts', updatedProducts);
  };

  const columns = [
    {
      name: 'Name/Category',
      selector: (row) => row.name,
      grow: 3,
      cell: (row) => (
        <div className="flex items-center gap-4">
          <Image
            src={row?.images?.main_image || DefaultImage}
            width={48}
            height={48}
            alt={row?.sku_lable}
            className="rounded-lg"
          />
          <span className="font-medium text-gray-900 text-[13px]">
            {row?.sku_lable}
          </span>
        </div>
      ),
    },
    {
      name: 'Quantity',
      selector: (row) => row.quantity,
      grow: 1,
      cell: (row, index) => (
        <InputField
          id={`qty-${index}`}
          name={`selectedProducts[${index}].qty`}
          type="number"
          placeholder="Enter min qty 1"
          min="1"
          value={row?.qty || ''}
          onChange={(e) => handleQtyChange(e.target.value, index, row?.price)}
          marginBottom="mb-0"
        />
      ),
    },
    {
      name: 'Price',
      selector: (row) => row.price,
      grow: 1,
      cell: (row) => (
        <span className="text-gray-900 text-[13px]">
          $ {row?.sku_total_price || (row?.qty || 1) * (row?.price || 0)}
        </span>
      ),
    },
    {
      name: '',
      width: '80px',
      cell: (row) => (
        <>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Delete"
            onClick={(e) => {
              e?.preventDefault();
              handleDeleteProduct(row.product_sku_id);
            }}
          >
            <span className="icon icon-trash text-base"></span>
          </button>
          <Tooltip
            id="edit-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
        </>
      ),
      sortable: false,
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '64px',
      },
    },
    selectableRows: {
      style: {
        width: '42px',
        paddingLeft: '8px',
        paddingRight: '0',
      },
    },
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      customStyles={customStyles}
      pagination={false}
      fixedHeader
      selectableRows
      selectableRowsComponent={CustomCheckbox}
      selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
      selectableRowsHighlight
      className="custom-table auto-height-table configure-table"
    />
  );
};

export default ComboBuilderTable;
