import React from 'react';

const getColumnWidth = (size) => {
  switch (size) {
    case 'small':
      return { min: '80px', default: '120px' };
    case 'medium':
      return { min: '150px', default: '300px' };
    case 'large':
      return { min: '200px', default: '460px' };
    case 'icon':
      return { min: '48px', default: '48px' };
    default:
      return { min: '80px', default: '100px' };
  }
};

const TableSkeleton = ({ 
  rowsPerPage = 5,
  columns = [],
  showCheckbox = false,
  cellHeight = 4
}) => {
  return (
    <div className="animate-pulse bg-white border border-border-color border-t-0 rounded-bl-lg rounded-br-lg overflow-x-auto">
      <div className="min-w-[640px]"> {/* Minimum width container */}
        {/* Header */}
        <div className="flex items-center gap-3 p-3 bg-[#f5f6f4]">
          {showCheckbox && (
            <div className="flex-shrink-0 w-4 h-4 rounded bg-gray-200"></div>
          )}
          {columns.map((col, idx) => (
            <div 
              key={idx} 
              className={`h-${cellHeight} bg-gray-200 rounded`}
              style={{ 
                minWidth: getColumnWidth(col.size).min,
                width: getColumnWidth(col.size).default,
                flex: col.grow ? `${col.grow} 1 0` : '0 0 auto'
              }}
            ></div>
          ))}
        </div>

        {/* Rows */}
        {Array.from({ length: rowsPerPage }).map((_, index) => (
          <div key={index} className="flex items-center gap-3 p-3 border-b border-border-color">
            {showCheckbox && (
              <div className="flex-shrink-0 w-4 h-4 rounded bg-gray-200"></div>
            )}
            {columns.map((col, idx) => (
              <div 
                key={idx} 
                className={`h-${cellHeight} bg-gray-200 rounded`}
                style={{ 
                  minWidth: getColumnWidth(col.size).min,
                  width: getColumnWidth(col.size).default,
                  flex: col.grow ? `${col.grow} 1 0` : '0 0 auto'
                }}
              ></div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default TableSkeleton;