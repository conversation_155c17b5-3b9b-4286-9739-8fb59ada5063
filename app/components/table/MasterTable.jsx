import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import CommonPagination from '../table/CommonPagination';
import SortIcon from '../table/SortIcon';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div 
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked 
        ? 'bg-primary-500 border-primary-500' 
        : 'border-border-color hover:border-gray-100'
    }`}
  >
    {rest.checked && <span className="icon icon-check-2 text-white text-[8px]" />}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const MasterTable = ({ columns, data, onEdit, onDelete }) => {
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);

  const handleSort = (field, direction) => {
    setSortedField(field);
    setSortDirection(direction);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      customStyles={customStyles}
      selectableRows
      selectableRowsComponent={CustomCheckbox}
      onSelectedRowsChange={({ selectedRows }) => setSelectedRows(selectedRows)}
      className="custom-table"
      pagination
      sortIcon={<SortIcon sortDirection={sortDirection} />}
      onSort={handleSort}
      sortField={sortedField}
      defaultSortAsc={true}
      paginationPerPage={8}
      paginationRowsPerPageOptions={[8]}
      paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
          noRowsPerPage: true,
        }}
        paginationComponent={(props) => (
          <CommonPagination
            selectedCount={props.selectedRows?.length}
            total={props.totalRows}
            page={props.currentPage}
            perPage={props.rowsPerPage}
            onPageChange={props.onChangePage}
          />
        )}
      onEdit={onEdit}
      onDelete={onDelete}
    />
  );
};

export default MasterTable;