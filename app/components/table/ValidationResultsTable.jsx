'use client';
import React, { useState } from 'react';
import DataTable from 'react-data-table-component';

const ValidationResultsTable = () => {
  const [selectedRows, setSelectedRows] = useState([]);
  const handleRowSelected = (state) => {
    setSelectedRows(state.selectedRows);
  };
  const columns = [
    {
      name: 'Row',
      selector: row => row.row,
      width: '100px',
    },
    {
      name: 'Status',
      selector: row => row.status,
      width: '100px',
      cell: row => (
        <span className={`icon ${row.status === 'success' ? 'icon-check-circle text-success-500' : 'icon-x-circle text-danger-500'} text-lg`} />
      ),
    },
    {
      name: 'Error Description',
      selector: row => row.errorDescription,
      grow: 2,
    },
  ];

  const data = [
    { 
      id: 1, 
      row: '3',
      status: 'error',
      errorDescription: 'Entry missing'
    },
    { 
      id: 2, 
      row: '6',
      status: 'success',
      errorDescription: '-'
    },
    { 
      id: 3, 
      row: '8',
      status: 'error',
      errorDescription: 'Invalid format'
    },
    { 
      id: 4, 
      row: '12',
      status: 'success',
      errorDescription: '-'
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px', // override the cell padding for head cells
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px', // override the cell padding for data cells
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '48px',
      },
    },
  };

  return (
    <DataTable
      columns={columns}
      data={data}
      customStyles={customStyles}
      pagination
      fixedHeader={true}
      className="custom-table auto-height-table upload-columns-table"
      paginationPerPage={8}
      paginationRowsPerPageOptions={[8]}
      paginationComponentOptions={{
        rowsPerPageText: 'Rows per page:',
        rangeSeparatorText: 'of',
        selectAllRowsItem: false,
        noRowsPerPage: true,
      }}
      paginationComponent={(props) => (
        <div className="flex items-center justify-between w-full pt-6">
          <div className="text-sm text-dark-500/60 font-medium">
            {selectedRows.length} of {data.length} row(s) selected
          </div>

          <div className="flex items-center gap-3 ml-auto">
            <div className="text-sm text-dark-500 font-medium">
              <span className="text-gray-500">Showing</span>{' '}
              {(props.currentPage - 1) * props.rowsPerPage + 1}-
              {Math.min(props.currentPage * props.rowsPerPage, data.length)}{' '}
              <span className="text-gray-500">of</span> {data.length}
            </div>
            <div className="flex items-center gap-2 bg-dark-500/5 rounded-lg overflow-hidden">
              <button
                onClick={() => props.onChangePage(1)}
                disabled={props.currentPage === 1}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-double-left text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.currentPage - 1)}
                disabled={props.currentPage === 1}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-left text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.currentPage + 1)}
                disabled={props.currentPage === props.totalPages}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-right text-base align-middle" />
              </button>
              <button
                onClick={() => props.onChangePage(props.totalPages)}
                disabled={props.currentPage === props.totalPages}
                className="flex items-center justify-center h-8 w-8 p-2 hover:bg-gray-100 disabled:opacity-40 cursor-pointer transition-base"
              >
                <span className="icon icon-caret-double-right text-base align-middle" />
              </button>
            </div>
          </div>
        </div>
      )}
    />
  );
};

export default ValidationResultsTable;