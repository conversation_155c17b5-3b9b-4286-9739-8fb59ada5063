import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const SortableItem = ({ id, value, checked, onChange }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <li
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-2 px-2 py-1 hover:bg-light-500/5 rounded-lg"
    >
      <div className="flex items-center justify-between gap-2 w-full">
        <div className="inline-flex items-center gap-2 flex-1">
          <label
            className="flex items-center cursor-pointer relative"
            htmlFor={`check-${id}`}
          >
            <input
              type="checkbox"
              className="peer h-4 w-4 cursor-pointer transition-all appearance-none rounded border-2 border-gray-200 checked:bg-primary-500 checked:border-primary-500"
              id={`check-${id}`}
              checked={checked}
              onChange={onChange}
            />
            <span className="absolute text-white opacity-0 peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
          </label>
          <label htmlFor={`check-${id}`} className="text-xs capitalize">
            {id.replace(/([A-Z])/g, ' $1').trim()}
          </label>
        </div>
        <span
          {...attributes}
          {...listeners}
          className="icon icon-dots-six-vertical text-gray-400 py-1 rounded-md hover:bg-dark-500/5 cursor-move transition-base duration-200"
        />
      </div>
    </li>
  );
};

export default SortableItem;