import React from 'react';
import DataTable from 'react-data-table-component';
import { Tooltip } from 'react-tooltip';
import TableSkeleton from './TableSkeleton';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked
        ? 'bg-primary-500 border-primary-500'
        : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));
CustomCheckbox.displayName = 'CustomCheckbox';

const SubscriptionTable = ({ data = [], isLoading = false, paginationPerPage = 5 }) => {
  const skeletonColumns = [
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'small', grow: 1 },
    { size: 'icon', grow: 1 }
  ];
  const columns = [
    {
      name: 'Plan Name',
      selector: (row) => row.planName,
      cell: (row) => (
        <span className="font-semibold text-gray-900 text-sm">
          {row.planName}
        </span>
      ),
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-lg text-xs font-semibold ${
            row.status === 'Active'
              ? 'bg-success-500/10 text-success-500'
              : 'bg-danger-500/10 text-danger-500'
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: 'Price Paid',
      selector: (row) => row.pricePaid,
      cell: (row) => (
        <span className="text-gray-900 text-sm">${row.pricePaid}</span>
      ),
    },
    {
      name: 'Plan Start Date',
      selector: (row) => row.startDate,
      cell: (row) => (
        <span className="text-gray-700 text-sm">{row.startDate}</span>
      ),
    },
    {
      name: 'Plan End Date',
      selector: (row) => row.endDate,
      cell: (row) => (
        <span className="text-gray-700 text-sm">{row.endDate}</span>
      ),
    },
    {
      name: '',
      width: '80px',
      cell: () => (
        <>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-gray-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Download"
          >
            <span className="icon icon-download-simple text-base"></span>
          </button>
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </>
      ),
      sortable: false,
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
    // Add styles for checkbox column
    selectableRows: {
      style: {
        width: '42px',
        paddingLeft: '8px',
        paddingRight: '0',
      },
    },
  };

  const sampleData = [
    {
      planName: 'Hubsups Grow',
      status: 'Active',
      pricePaid: '99.99',
      startDate: 'Dec 17, 2022',
      endDate: 'Dec 17, 2022',
    },
    {
      planName: 'Hubsups Grow',
      status: 'Expired',
      pricePaid: '99.99',
      startDate: 'Dec 17, 2022',
      endDate: 'Dec 17, 2022',
    },
    {
      planName: 'Hubsups Grow',
      status: 'Expired',
      pricePaid: '99.99',
      startDate: 'Dec 17, 2022',
      endDate: 'Dec 17, 2022',
    },
  ];

  return (
    <>
      {isLoading ? (
        <TableSkeleton 
          rowsPerPage={paginationPerPage}
          columns={skeletonColumns}
          showCheckbox={true}
          cellHeight={4}
        />
      ) : (
      <DataTable
        columns={columns}
        data={data.length ? data : sampleData}
        customStyles={customStyles}
        pagination={false}
        selectableRows // Enable selectable rows
        selectableRowsComponent={CustomCheckbox}
        selectableRowsComponentProps={{ 'aria-label': 'Select Row' }}
        selectableRowsHighlight
        className="custom-table auto-height-table"
      />
      )}
    </>
  );
};

export default SubscriptionTable;
