import React from 'react';
import DataTable from 'react-data-table-component';
import SortIcon from './SortIcon';

const PricingTable = ({ data }) => {
  const columns = [
    {
      name: 'SKU Label',
      selector: (row) => row.sku,
      sortable: true,
    },
    {
      name: 'Attributes',
      selector: (row) => row.attributes,
      sortable: true,
      cell: (row) => <span>{row.attributes.join(', ')}</span>,
    },
    {
      name: 'Final Price',
      selector: (row) => row.price,
      sortable: true,
      cell: (row) => <span className="font-medium">$ {row.price}</span>,
    },
    {
      name: 'Quantity',
      selector: (row) => row.quantity,
      sortable: true,
      width: '100px',
    },
    {
      name: 'Tier Pricing',
      selector: (row) => row.tierPricing,
      sortable: true,
      cell: (row) => (
        <button className="text-info-500 hover:text-info-600">
          {row.tierPricing ? 'Yes' : 'No'}
        </button>
      ),
    },
    {
      name: 'Default SKU',
      selector: (row) => row.isDefault,
      sortable: true,
      cell: (row) =>
        row.isDefault ? (
          <span className="bg-success-500/10 text-success-600 inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold text-nowrap">
            Default
          </span>
        ) : null,
    },
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
        fontWeight: '600',
      },
    },
    rows: {
      style: {
        minHeight: '48px',
      },
    },
  };

  return (
    <div className="rounded-lg bg-white">
      <DataTable
        columns={columns}
        data={data}
        customStyles={customStyles}
        fixedHeader
        highlightOnHover
        pointerOnHover
        sortIcon={<SortIcon />}
        className="custom-table auto-height-table"
      />
    </div>
  );
};

export default PricingTable;
