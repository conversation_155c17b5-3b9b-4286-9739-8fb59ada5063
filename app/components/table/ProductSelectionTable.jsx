import React, { useState, useEffect, useCallback, useMemo } from 'react';
import DataTable from 'react-data-table-component';
import Image from 'next/image';
import DefaultImage from '@/public/images/default-Image.jpg';
import { Tooltip } from 'react-tooltip';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={!rest.disabled ? onClick : undefined}
    ref={ref}
    className={`w-4 h-4 rounded border-2 flex items-center justify-center transition-base ${
      rest.disabled
        ? 'bg-primary-500 border-primary-500 cursor-pointer'
        : rest.checked
          ? 'bg-primary-500 border-primary-500 cursor-pointer'
          : 'border-gray-200 hover:border-gray-100 cursor-pointer'
    }`}
  >
    {!rest.disabled && rest.checked && (
      <span className="icon icon-check-2 text-white text-[8px]" />
    )}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ProductSelectionTable = ({
  data = [],
  setSelectedIds = () => {},
  productCombosList = [],
  onlyAllowSingleSelection = false,
}) => {
  const [selectedRowsState, setSelectedRowsState] = useState([]);

  const modifiedData = useMemo(() => {
    const existingIds = productCombosList.map(
      (item) => item.product_sku_id || item.id
    );
    return data.map((item) => ({
      ...item,
      disabled: existingIds.includes(item.id),
    }));
  }, [data, productCombosList]);

  let columns = [
    {
      name: onlyAllowSingleSelection ? 'Product Name' :'Name/Sku',
      selector: (row) => row.name,
      grow: 3,
      cell: (row) => (
        <div className="flex items-center gap-4">
          <Image
            src={row?.images?.main_image || DefaultImage}
            width={48}
            height={48}
            alt={row?.label || 'Product image'}
            className="rounded-lg"
          />
          <span className="font-medium text-gray-900 text-[13px]">
            {row?.label}
          </span>
        </div>
      ),
    },
    {
      name: 'Price',
      selector: (row) => row.price,
      grow: 1,
      cell: (row) => (
        <span className="text-gray-900 text-[13px]">
          $ {row?.price?.toFixed(2) || 0}
        </span>
      ),
    },
    {
      name: '',
      width: '80px',
      cell: () => (
        <>
          {/* Consider if you want this "delete" button here for selection tables */}
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Delete"
          >
            <span className="icon icon-trash" />
          </button>
          <Tooltip
            id="edit-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
        </>
      ),
      sortable: false,
    },
  ];
   if(onlyAllowSingleSelection){
    columns = columns.filter((item)=> item.name !== 'Price')
   }



  const handleRowSelect = useCallback(({ selectedRows }) => {
    let newSelection = [];

    if (onlyAllowSingleSelection) {
      if (selectedRows.length > 0) {
        const selectedItem = selectedRows[0];
        if (!selectedItem.disabled) {
          newSelection = [{ ...selectedItem, qty: 1 }];
        }
      }
    } else {
      newSelection = selectedRows
        .filter((row) => !row.disabled)
        .map((row) => ({
          ...row,
          qty: 1,
        }));
    }

    setSelectedRowsState(newSelection); 
    setSelectedIds(newSelection); 
  }, [onlyAllowSingleSelection, setSelectedIds]);

  useEffect(() => {
    const currentValidSelection = selectedRowsState.filter(selectedRow =>
      modifiedData.some(mItem => mItem.id === selectedRow.id && !mItem.disabled)
    );
    const currentIds = currentValidSelection.map(r => r.id).sort();
    const stateIds = selectedRowsState.map(r => r.id).sort();

    if (currentIds.length !== stateIds.length || JSON.stringify(currentIds) !== JSON.stringify(stateIds)) {
      setSelectedRowsState(currentValidSelection);
      setSelectedIds(currentValidSelection);
    }
  }, [modifiedData, setSelectedIds]);

  return (
    <>
      <DataTable
        columns={columns}
        data={modifiedData}
        customStyles={customStyles}
        selectableRows
        selectableRowsSingle={onlyAllowSingleSelection}
        selectableRowsComponent={CustomCheckbox}
        onSelectedRowsChange={handleRowSelect}
        selectableRowDisabled={(row) => row.disabled}
        pagination={false}
        fixedHeader={true}
        responsive
        className="custom-table auto-height-table product-selection-table"
        selectedRows={selectedRowsState}
      />
    </>
  );
};

const customStyles = {
  headRow: {
    style: {
      backgroundColor: '#FBFAFA',
      borderRadius: '0',
    },
  },
  headCells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },
  cells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      paddingTop: '8px',
      paddingBottom: '8px',
    },
  },
  rows: {
    style: {
      minHeight: '52px',
    },
  },
  expandableRow: {
    style: {
      backgroundColor: '#ffffff',
    },
  },
};

export default ProductSelectionTable;