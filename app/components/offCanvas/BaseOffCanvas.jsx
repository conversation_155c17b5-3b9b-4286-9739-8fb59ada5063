'use client';
import React from 'react';

const BaseOffCanvas = ({ isOpen, onClose, title, children, customClass, size = 'lg' }) => {

  const sizeClasses = {
    sm: 'max-w-[516px]',
    md: 'max-w-[780px]',
    lg: 'max-w-[85%] lg:max-w-[960px]',
    xl: 'max-w-[85%]',
  };

  return (
    <>
      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-dark-500/50 z-50 mb-0"
          // onClick={onClose}
        />
      )}

      {/* Off-canvas */}
      <div
        className={`fixed h-dvh top-1/2 right-0 -translate-y-1/2 w-full ${sizeClasses[size]} ${customClass} bg-white rounded-s-xl z-50 transition-base overflow-hidden ${
          isOpen
            ? 'translate-x-0 opacity-100 visibility-visible'
            : 'translate-x-full opacity-0 visibility-hidden'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between py-2 px-4 bg-gray-500/5 border-b border-border-color">
            <h2 className="text-base font-semibold">{title}</h2>
            <button
              type='button'
              onClick={onClose}
              className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-danger-500/10 hover:text-danger-500 transition-base cursor-pointer"
            >
              <span className="icon icon-x" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1">{children}</div>
        </div>
      </div>
    </>
  );
};

export default BaseOffCanvas;
