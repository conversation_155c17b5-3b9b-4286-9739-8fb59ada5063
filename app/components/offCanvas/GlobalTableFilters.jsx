'use client';

import React from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import SelectField from '@/app/components/Inputs/SelectField';
import RangeDatePicker from '@/app/components/Inputs/RangeDatePicker';

const GlobalTableFilters = ({ filters = {}, onChange }) => {
  const formik = useFormik({
    initialValues: {
      category: filters.category || '',
      status: filters.status || '',
      dateRange: filters.dateRange || [null, null],
    },
    validationSchema: Yup.object({
      category: Yup.string(),
      status: Yup.string(),
      dateRange: Yup.array().of(Yup.date().nullable()),
    }),
    onSubmit: () => {}, // Not needed as we're using onChange
  });

  // Update parent component whenever form values change
  React.useEffect(() => {
    if (onChange && typeof onChange === 'function') {
      onChange(formik.values);
    }
  }, [formik.values, onChange]);

  const categoryOptions = [
    { value: '', label: 'All Categories' },
    { value: 'technical', label: 'Technical' },
    { value: 'billing', label: 'Billing' },
    { value: 'account', label: 'Account' },
    { value: 'product', label: 'Product' },
    // { value: 'other', label: 'Other' },
  ];

  const statusOptions = [
    // { value: '', label: 'All Status' },
    { value: 'open', label: 'Open' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'escalated', label: 'Escalated' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'closed', label: 'Closed' },
  ];

  const handleReset = () => {
    formik.resetForm();
  };

  return (
    <form className="flex flex-col">
      <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        <div className="flex flex-col mb-4">
          <span className="form-label">
            Category
          </span>
          <SelectField
            id="category"
            name="category"
            placeholder="Select category"
            className="single-select"
            value={formik.values.category}
            onChange={(selected) => formik.setFieldValue('category', selected.value)}
            error={formik.touched.category && formik.errors.category}
            options={categoryOptions}
          />
        </div>

        <div className="flex flex-col mb-4">
          <span className="form-label">
            Status
          </span>
          <SelectField
            id="status"
            name="status"
            placeholder="Select status"
            className="single-select"
            isMulti={true}
            closeMenuOnSelect={false}
            isSearchable
            useMenuPortal={false}
            value={formik.values.status}
            onChange={(selected) => formik.setFieldValue('status', selected.value)}
            error={formik.touched.status && formik.errors.status}
            options={statusOptions}
          />
        </div>

        <div className="flex flex-col mb-4">
          <label htmlFor="dateRange" className="form-label">
            Date Range
          </label>
          <RangeDatePicker
            id="dateRange"
            name="dateRange"
            placeholder="From - To"
            onChange={(dates) => {
              formik.setFieldValue('validityFrom', dates?.[0] || '');
              formik.setFieldValue('validityTo', dates?.[1] || '');
            }}
            formik={formik}
            nameFrom="validityFrom"
            nameTo="validityTo"
          />
          {formik.touched.validityFrom && formik.errors.validityFrom && (
            <div className="text-danger-500 text-xs mt-1">
              {formik.errors.validityFrom}
            </div>
          )}
          {formik.touched.validityTo && formik.errors.validityTo && (
            <div className="text-danger-500 text-xs mt-1">
              {formik.errors.validityTo}
            </div>
          )}
        </div>
        
      </div>

      <div className="flex justify-between p-4 gap-2.5 border-t border-border-color">
        <button
          type="button"
          className="btn btn-gray"
          onClick={handleReset}
        >
          Reset
        </button>
        <div className="flex gap-2.5">
          <button
            type="button"
            className="btn btn-outline-gray"
            // onClick={onClose}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn"
            onClick={formik.handleSubmit}
          >
            Apply
          </button>
        </div>
      </div>
    </form>
  );
};

export default GlobalTableFilters;
