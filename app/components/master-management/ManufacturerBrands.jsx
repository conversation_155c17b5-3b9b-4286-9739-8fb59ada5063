'use client';
import React, { useState } from 'react';
import { Formik } from 'formik';
import BaseMasterModal from '../modals/BaseMasterModal';
import MasterTable from '../table/MasterTable';
import { MANUFACTURER_BRAND_SCHEMA } from '@/utils/schema';

const ManufacturerBrands = ({ isModalOpen, setIsModalOpen }) => {
    const initialValues = {
    manufacturerName: '',
    countryOfOrigin: '',
    status: true,
  };

  const [tableData, setTableData] = useState([]);

  // Sample countries data - replace with actual data from your Countries master
  const countries = [
    { value: 'US', label: 'United States' },
    { value: 'UK', label: 'United Kingdom' },
    { value: 'IN', label: 'India' },
    // Add more as needed
  ];

  const handleSubmit = (values, { resetForm }) => {
    const isDuplicate = tableData.some(
      (item) =>
        item.manufacturerName.toLowerCase() ===
        values.manufacturerName.toLowerCase()
    );

    if (isDuplicate) {
      alert('Manufacturer Brand already exists');
      return;
    }

    setTableData((prev) => [
      ...prev,
      { id: Date.now(), ...values },
    ]);

    resetForm();
    setIsModalOpen(false);
  };

  const handleDelete = (id) => {
    setTableData((prev) => prev.filter((item) => item.id !== id));
  };

  const columns = [
    {
      name: 'Manufacturer Name',
      selector: row => row.manufacturerName,
      sortable: true,
    },
    {
      name: 'Country of Origin',
      selector: row => row.countryOfOrigin,
      sortable: true,
      cell: row => {
        // Fix: Return the label directly instead of the country object
        const country = countries.find(c => c.value === row.countryOfOrigin);
        return country ? country.label : '-';
      }
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold ${
          row.status ? 'bg-success-500/10 text-success-500' : 'bg-gray-500/10 text-gray-600'
        }`}>
          {row.status ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <button
          onClick={() => handleDelete(row.id)}
          className="text-danger-500 hover:bg-danger-500/10 rounded p-2"
        >
          <span className="icon icon-trash" />
        </button>
      ),
    },
  ];

  const fields = [
    {
      type: 'text',
      name: 'manufacturerName',
      label: 'Manufacturer Name',
      placeholder: 'Enter manufacturer name',
      required: true,
    },
    {
      type: 'select',
      name: 'countryOfOrigin',
      label: 'Country of Origin',
      options: countries,
      placeholder: 'Select country',
    },
    {
      type: 'toggle',
      name: 'status',
      label: 'Status',
    },
  ];

  return (
    <>
      <MasterTable columns={columns} data={tableData} />

      {isModalOpen && (
        <Formik
          initialValues={initialValues}
          validationSchema={MANUFACTURER_BRAND_SCHEMA()}
          onSubmit={handleSubmit}
        >
          {(formik) => (
            <BaseMasterModal
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              title="Add Manufacturer Brand"
              fields={fields}
              formik={formik}
            />
          )}
        </Formik>
      )}
    </>
  );
};

export default ManufacturerBrands;