import React, { useState } from 'react';
import MasterTable from '../table/MasterTable';
import BaseMasterModal from '../modals/BaseMasterModal';
import { Tooltip } from 'react-tooltip';
import { UNIT_OF_MASTER_SCHEMA } from '@/utils/schema';
import { Formik } from 'formik';

const UoM = ({ isModalOpen, setIsModalOpen }) => {
  const [tableData, setTableData] = useState([]);

  const columns = [
    {
      name: 'UoM Code',
      selector: row => row.code,
      sortable: true,
    },
    {
      name: 'Full Name',
      selector: row => row.fullName,
      sortable: true,
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span className={`inline-flex items-center gap-1.5 px-2 py-1 rounded-lg text-xs font-bold ${row.status ? 'bg-success-500/10 text-success-500' : 'bg-gray-500/10 text-gray-600'
          }`}>
          {row.status ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      sortable: false,
      grow: 0,
      cell: (row) => (
        <>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete"
            onClick={() => handleDelete(row.id)}
          >
            <span className="icon icon-trash text-base" />
          </button>
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </>
      ),
    },
  ];

  const fields = [
    {
      type: 'text',
      name: 'code',
      label: 'UoM Code',
      placeholder: 'Enter UoM code (e.g., KG, PCS, LTR)',
      required: true
    },
    {
      type: 'text',
      name: 'fullName',
      label: 'Full Name',
      placeholder: 'Enter full name (e.g., Kilogram)',
      required: true
    },
    {
      type: 'toggle',
      name: 'status',
      label: 'Status'
    }
  ];

  const handleSubmit = (values, { resetForm }) => {
    const isDuplicate = tableData.some(
      item => item.code.toLowerCase() === values.code.toLowerCase()
    );

    if (isDuplicate) {
      alert('UoM code already exists');
      return;
    }

    setTableData([...tableData, { id: Date.now(), ...values }]);
    resetForm();
    setIsModalOpen(false);
  };

  const handleDelete = (id) => {
    setTableData(tableData.filter(item => item.id !== id));
  };

  return (
    <>
      <MasterTable columns={columns} data={tableData} />

      <Formik
        initialValues={{ code: '', fullName: '', status: true }}
        validationSchema={UNIT_OF_MASTER_SCHEMA}
        onSubmit={handleSubmit}
        enableReinitialize
      >
        {(formik) => (
          <BaseMasterModal
            isOpen={isModalOpen}
            onClose={() => {
              setIsModalOpen(false);
              formik.resetForm();
            }}
            title="Add Units of Measure"
            formik={formik}
            fields={fields}
          />
        )}
      </Formik>
    </>
  );
};

export default UoM;