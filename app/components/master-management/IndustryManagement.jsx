'use client';

import React, { useEffect, useState } from 'react';
import { Formik, Form } from 'formik';
import DataTable from 'react-data-table-component';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import InputField from '../Inputs/InputField';
import ToggleSwitch from '../Inputs/ToggleSwitch';
import SortIcon from '../table/SortIcon';
import CommonPagination from '../table/CommonPagination';
import { Tooltip } from 'react-tooltip';
import { INDUSTRY_SCHEMA } from '../../../utils/schema';
import {
  createIndustryAsync,
  updateIndustryAsync,
  deleteIndustryAsync,
  fetchIndustriesAsync,
  fetchIdIndustriesAsync,
} from '@/store/api/masterManagementApi';
import { useDispatch } from 'react-redux';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${rest.checked ? 'bg-primary-500 border-primary-500' : 'border-gray-200 hover:border-gray-100'
      }`}
  >
    {rest.checked && <span className="icon icon-check-2 text-white text-[8px]" />}
  </div>
));
CustomCheckbox.displayName = 'CustomCheckbox';

const FormikToggleSwitch = ({ label, name, formik }) => (
  <div className="mb-0">
    {label && <label className="block text-sm font-medium mb-1">{label}</label>}
    <ToggleSwitch
      checked={formik.values[name]}
      onChange={(e) => formik.setFieldValue(name, e.target.checked)}
    />
  </div>
);

const IndustryManagement = ({
  isAddModalOpen,
  setIsAddModalOpen,
  initialFormValues,
  setInitialFormValues,
  editIndustryId,
  setEditIndustryId,
  filterText,
}) => {
  console.log('called');
  
  const dispatch = useDispatch();

  const [industries, setIndustries] = useState([]);
  const [selectedIndustries, setSelectedIndustries] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [pagination, setPagination] = useState({ page: 1, perPage: 10, total: 0 });

  const fetchIndustryList = async () => {
    const { payload } = await dispatch(
      fetchIndustriesAsync({ page: pagination.page, per_page: pagination.perPage })
    );
    if (payload?.data) {
      const filteredData = filterText
        ? payload.data.filter((industry) =>
          industry.name.toLowerCase().includes(filterText.toLowerCase())
        )
        : payload.data;

      setIndustries(filteredData);
      setPagination((prev) => ({
        ...prev,
        total: payload.meta?.total || 0,
      }));
    }
  };

  useEffect(() => {
    fetchIndustryList();
  }, [pagination.page, pagination.perPage, filterText]);

  const handleIndustrySubmit = async (values, { resetForm }) => {
    const payloadData = {
      name: values.industryName,
      status: values.status,
    };

    let response;
    if (editIndustryId) {
      const result = await dispatch(updateIndustryAsync({ id: editIndustryId, ...payloadData }));
      response = result.payload;
    } else {
      const result = await dispatch(createIndustryAsync(payloadData));
      response = result.payload;
    }

    if (response?.ok || response?.status) {
      await fetchIndustryList();
      resetForm();
      setIsAddModalOpen(false);
      setEditIndustryId(null);
      setInitialFormValues({ industryName: '', status: true });
    } else {
      console.error('Submit Error:', response?.message);
    }
  };

  const handleDeleteIndustry = async (industry) => {
    const confirmDelete = window.confirm(`Are you sure you want to delete "${industry.name}"?`);
    if (!confirmDelete) return;
    const { payload } = await dispatch(deleteIndustryAsync({ industryId: industry?.id }));
    if (payload?.status || payload?.ok) {
      await fetchIndustryList();
    } else {
      console.error(payload?.message || 'Failed to delete industry');
    }
  };

  const handleEditIndustry = async (industry) => {
    setIsAddModalOpen(false);
    const { payload } = await dispatch(fetchIdIndustriesAsync(industry?.id));
    if (payload?.data) {
      const statusBoolean = payload.data.status === 'Active';
      setInitialFormValues({
        industryName: payload.data.name,
        status: statusBoolean,
      });
      setEditIndustryId(industry.id);
      setIsAddModalOpen(true);
    } else {
      setInitialFormValues({ industryName: '', status: true });
      setEditIndustryId(null);
    }
  };

  const handleSortChange = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const handleSelectedRowChange = ({ selectedRows }) => {
    setSelectedIndustries(selectedRows);
  };

  const columns = [
    {
      name: 'Industry Name',
      selector: (row) => row?.name || '-',
      sortable: true,
    },
    {
      name: 'Status',
      selector: (row) => row?.status || false,
      sortable: true,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${row.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}
        >
          {row.status === 'Active' ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      sortable: false,
      grow: 0,
      cell: (row) => (
        <>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            onClick={() => handleEditIndustry(row)}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <Tooltip
            id="edit-tooltip"
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id={`delete-tooltip-${row.id}`}
            data-tooltip-content="Delete"
            onClick={() => handleDeleteIndustry(row)}
          >
            <span className="icon icon-trash text-base" />
          </button>
          <Tooltip
            id={`delete-tooltip-${row.id}`}
            className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
          />
        </>
      ),
    },
  ];

  const customTableStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={industries}
        customStyles={customTableStyles}
        pagination
        highlightOnHover
        selectableRows
        selectableRowsComponent={CustomCheckbox}
        onSelectedRowsChange={handleSelectedRowChange}
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={handleSortChange}
        sortField={sortedField}
        defaultSortAsc
        paginationPerPage={pagination.perPage}
        selectableRowsHighlight
        paginationRowsPerPageOptions={[10]}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          noRowsPerPage: true,
        }}
        paginationComponent={() => (
          <CommonPagination
            selectedCount={selectedIndustries.length}
            total={pagination.total}
            page={pagination.page}
            perPage={pagination.perPage}
            onPageChange={(page) =>
              setPagination((prev) => ({
                ...prev,
                page,
              }))
            }
          />
        )}
      />

      <BaseOffCanvas
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false);
          setEditIndustryId(null);
          setInitialFormValues({ industryName: '', status: true });
        }}
        size="sm"
        title={editIndustryId ? 'Edit Industry' : 'Add New Industry'}
      >
        <Formik
          key={editIndustryId !== null ? editIndustryId : 'new-industry'}
          enableReinitialize
          initialValues={initialFormValues}
          validationSchema={INDUSTRY_SCHEMA}
          onSubmit={handleIndustrySubmit}
        >
          {(formik) => (
            <Form>
              <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
                <InputField
                  name="industryName"
                  id="industryName"
                  label="Industry Name"
                  placeholder="Enter industry name"
                  formik={formik}
                  value={formik.values.industryName || ''}
                />
                <FormikToggleSwitch
                  name="status"
                  label="Status"
                  formik={formik}
                />
              </div>
              <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
                <button
                  type="button"
                  onClick={() => {
                    setIsAddModalOpen(false);
                    setEditIndustryId(null);
                    setInitialFormValues({ industryName: '', status: true });
                  }}
                  className="btn btn-outline-gray mr-2"
                >
                  Cancel
                </button>
                <button type="submit" className="btn">
                  {editIndustryId ? 'Update Industry' : 'Add Industry'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </BaseOffCanvas>
    </>
  );
};

export default IndustryManagement;