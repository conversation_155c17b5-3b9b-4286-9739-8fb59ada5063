'use client';
import React, { useEffect, useState, useCallback } from 'react';
import DataTable from 'react-data-table-component';
import { TabGroup, TabList, TabButton, TabPanels, TabPanel } from '@/app/components/tabs/Tabs';
import SortIcon from '@/app/components/table/SortIcon';
import CommonPagination from '@/app/components/table/CommonPagination';
import AddBrandFranchiseModal from '../modals/AddBrandFranchiseModal';
import {
  createBrandAsync,
  createFranchiseAsync,
  deleteBrandAsync,
  deleteFranchiseAsync,
  fetchAllBrandAsync,
  fetchAllFranchiseAsync,
  fetchBrandByIdAsync,
  fetchFranchiseByIdAsync,
  updateBrandByIdAsync,
  updateFranchiseByIdAsync,
} from '@/store/api/masterManagementApi';
import { useDispatch } from 'react-redux';

// Custom Checkbox component for data table
const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked ? 'bg-primary-500 border-primary-500' : 'border-gray-200 hover:border-gray-100'
    }`}
  >
    {rest.checked && <span className="icon icon-check-2 text-white text-[8px]" />}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const BrandFranchiseManagement = () => {
  const dispatch = useDispatch();

  // State for managing tabs and data
  const [selectedTab, setSelectedTab] = useState(0);
  const [brands, setBrands] = useState([]);
  const [franchises, setFranchises] = useState([]);

  // State for modal and edit functionality
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [currentEditData, setCurrentEditData] = useState(null);

  // State for sorting
  const [brandSortedField, setBrandSortedField] = useState(null);
  const [brandSortDirection, setBrandSortDirection] = useState(null);
  const [franchiseSortedField, setFranchiseSortedField] = useState(null);
  const [franchiseSortDirection, setFranchiseSortDirection] = useState(null);

  // State for selected rows in data table
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [selectedFranchises, setSelectedFranchises] = useState([]);

  // State for pagination
  const [brandPagination, setBrandPagination] = useState({ page: 1, perPage: 10, total: 0 });
  const [franchisePagination, setFranchisePagination] = useState({ page: 1, perPage: 10, total: 0 });

  // --- Data Fetching Callbacks ---
  const fetchBrandList = useCallback(async () => {
    const { payload } = await dispatch(
      fetchAllBrandAsync({
        page: brandPagination.page,
        per_page: brandPagination.perPage,
      })
    );
    if (payload?.data) {
      setBrands(payload.data);
      setBrandPagination((prev) => ({
        ...prev,
        total: payload.meta?.total || 0,
      }));
    }
  }, [dispatch, brandPagination.page, brandPagination.perPage]);

  const fetchFranchiseList = useCallback(async () => {
    const { payload } = await dispatch(
      fetchAllFranchiseAsync({
        page: franchisePagination.page,
        per_page: franchisePagination.perPage,
      })
    );
    if (payload?.data) {
      setFranchises(payload.data);
      setFranchisePagination((prev) => ({
        ...prev,
        total: payload.meta?.total || 0,
      }));
    }
  }, [dispatch, franchisePagination.page, franchisePagination.perPage]);

  // --- Effects for Data Loading on Tab/Pagination Change ---
  useEffect(() => {
    if (selectedTab === 0) {
      fetchBrandList();
    }
  }, [selectedTab, brandPagination.page, brandPagination.perPage, fetchBrandList]);

  useEffect(() => {
    if (selectedTab === 1) {
      fetchFranchiseList();
    }
  }, [selectedTab, franchisePagination.page, franchisePagination.perPage, fetchFranchiseList]);

  // --- Form Submission Handlers (Create & Update) ---
  const handleBrandFormSubmit = async (values, { resetForm }) => {
    const payload = {
      name: values.brandName,
      logo: values.logo,
      status: values.status,
    };

    let response;
    if (isEditMode && currentEditData?.id) {
      // Call update API if in edit mode
      const { payload: updatePayload } = await dispatch(
        updateBrandByIdAsync({ id: currentEditData.id, ...payload })
      );
      response = updatePayload;
    } else {
      // Call create API if in add mode
      const { payload: createPayload } = await dispatch(createBrandAsync(payload));
      response = createPayload;
    }

    if (response?.ok) {
      await fetchBrandList();
      resetForm();
      setIsAddModalOpen(false);
      setIsEditMode(false);
      setCurrentEditData(null);
    } else {
      console.error(isEditMode ? 'Update Brand Error:' : 'Create Brand Error:', response?.message);
    }
  };

  const handleFranchiseFormSubmit = async (values, { resetForm }) => {
    const payload = {
      name: values.franchiseName,
      parentBrand: values.parentBrand,
    };

    let response;
    if (isEditMode && currentEditData?.id) {
      // Call update API if in edit mode
      const { payload: updatePayload } = await dispatch(
        updateFranchiseByIdAsync({ id: currentEditData.id, ...payload })
      );
      response = updatePayload;
    } else {
      // Call create API if in add mode
      const { payload: createPayload } = await dispatch(createFranchiseAsync(payload));
      response = createPayload;
    }

    if (response?.ok) {
      await fetchFranchiseList(); // Refresh list after successful operation
      resetForm();
      setIsAddModalOpen(false);
      setIsEditMode(false);
      setCurrentEditData(null);
    } else {
      console.error(isEditMode ? 'Update Franchise Error:' : 'Create Franchise Error:', response?.message);
    }
  };

  // --- Row Selection Handlers ---
  const handleSelectedBrandRowChange = ({ selectedRows }) => {
    setSelectedBrands(selectedRows);
  };

  const handleSelectedFranchiseRowChange = ({ selectedRows }) => {
    setSelectedFranchises(selectedRows);
  };

  // --- Delete Handler ---
  const handleDelete = async (id, type) => {
    const typeLabel = type === 'brand' ? 'Brand' : 'Franchise';

    const confirmDelete = window.confirm(`Are you sure you want to delete this ${typeLabel}?`);
    if (!confirmDelete) return;

    try {
      let payload;
      if (type === 'brand') {
        const response = await dispatch(deleteBrandAsync({ brandId: id }));
        payload = response.payload;
      } else if (type === 'franchise') {
        const response = await dispatch(deleteFranchiseAsync({ franchiseId: id }));
        payload = response.payload;
      }

      if (payload?.status || payload?.ok) {
        if (type === 'brand') {
          setSelectedBrands([]);
          fetchBrandList();
        } else {
          setSelectedFranchises([]);
          fetchFranchiseList();
        }
      } else {
        console.error(payload?.message || `Failed to delete ${typeLabel}`);
      }
    } catch (error) {
      console.error(`Error deleting ${typeLabel}:`, error);
    }
  };

  // --- Edit Handler (Fetches data and opens modal) ---
  const handleEdit = async (row, type) => {
    setIsEditMode(true);
    setIsAddModalOpen(true); 

    if (type === 'brand') {
      const { payload } = await dispatch(fetchBrandByIdAsync(row.id));
      if (payload?.data) {
        const formattedLogo = payload.data.logo
          ? typeof payload.data.logo === 'string'
            ? [{ preview: payload.data.logo }]
            : payload.data.logo
          : [];

        setCurrentEditData({
          id: payload.data.id,
          brandName: payload.data.name,
          logo: formattedLogo,
          status: payload.data.status,
        });
      } else {
        console.error('Failed to fetch brand for editing:', payload?.message);
        setIsEditMode(false);
        setIsAddModalOpen(false);
        setCurrentEditData(null);
      }
    } else if (type === 'franchise') {
      const { payload } = await dispatch(fetchFranchiseByIdAsync(row.id));
      if (payload?.data) {
        setCurrentEditData({
          id: payload.data.id,
          franchiseName: payload.data.name,
          parentBrand: payload.data.parentBrandId,
          status: payload.data.status,
        });
      } else {
        console.error('Failed to fetch franchise for editing:', payload?.message);
        setIsEditMode(false);
        setIsAddModalOpen(false);
        setCurrentEditData(null);
      }
    }
  };

  // --- Modal Close Handler ---
  const handleModalClose = () => {
    setIsAddModalOpen(false);
    setIsEditMode(false);
    setCurrentEditData(null);
  };

  const handleBrandSort = (column, direction) => {
    if (typeof column.selector === 'string') {
      setBrandSortedField(column.selector);
    }
    setBrandSortDirection(direction);
  };

  const handleFranchiseSort = (column, direction) => {
    if (typeof column.selector === 'string') {
      setFranchiseSortedField(column.selector);
    }
    setFranchiseSortDirection(direction);
  };

  // --- Custom Styles for DataTable ---
  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  // --- Columns Definitions for Brands DataTable ---
  const brandColumns = [
    {
      name: 'Brand Name',
      selector: (row) => row.name,
      sortable: true,
    },
    {
      name: 'Logo',
      cell: (row) => (
        <>
          {row.logo && (typeof row.logo === 'string' || (Array.isArray(row.logo) && row.logo.length > 0 && row.logo[0]?.preview)) ? (
            <img src={typeof row.logo === 'string' ? row.logo : row.logo[0].preview} alt={row.name} className="h-8 w-8 object-cover rounded" />
          ) : null}
        </>
      ),
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            row.status ? 'bg-success-500/10 text-success-600' : 'bg-gray-500/10 text-gray-600'
          }`}
        >
          {row.status ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleEdit(row, 'brand')}
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            onClick={() => handleDelete(row.id, 'brand')}
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>
        </div>
      ),
    },
  ];

  // --- Columns Definitions for Franchises DataTable ---
  const franchiseColumns = [
    {
      name: 'Franchise Name',
      selector: (row) => row.name,
      sortable: true,
    },
    {
      name: 'Parent Brand',
      selector: (row) => brands.find((b) => b.id === row.parentBrandId)?.name,
      sortable: true,
    },
    {
      name: 'Status',
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <span
          className={`px-2 py-1 rounded-full text-xs ${
            row.status ? 'bg-success-500/10 text-success-600' : 'bg-gray-500/10 text-gray-600'
          }`}
        >
          {row.status ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    {
      name: 'Actions',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <button
            onClick={() => handleEdit(row, 'franchise')}
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-primary-500/10 hover:text-primary-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            onClick={() => handleDelete(row.id, 'franchise')}
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
          >
            <span className="icon icon-trash text-base" />
          </button>
        </div>
      ),
    },
  ];

  return (
    <div>
      <TabGroup
        selectedIndex={selectedTab}
        onChange={(index) => {
          setSelectedTab(index);
          if (index === 0) {
            setSelectedFranchises([]);
          } else if (index === 1) {
            setSelectedBrands([]);
          }
          setIsAddModalOpen(false);
          setIsEditMode(false);
          setCurrentEditData(null);
        }}
      >
        <TabList>
          <TabButton selected={selectedTab === 0} onClick={() => setSelectedTab(0)}>
            Brands
          </TabButton>
          <TabButton selected={selectedTab === 1} onClick={() => setSelectedTab(1)}>
            Franchises
          </TabButton>
        </TabList>

        <TabPanels selectedIndex={selectedTab}>
          <TabPanel>
            <div className="flex items-center justify-between gap-2.5 bg-white py-2 px-3 rounded-tl-xl rounded-tr-xl border border-border-color ">
              <h3 className="text-sm font-semibold">Brand Management</h3>
              <button
                onClick={() => {
                  setIsAddModalOpen(true);
                  setIsEditMode(false);
                  setCurrentEditData(null);
                }}
                className="btn flex items-center gap-1"
              >
                <span className="icon icon-plus text-base" />
                Add Brand
              </button>
            </div>

            <DataTable
              columns={brandColumns}
              data={brands}
              customStyles={customStyles}
              selectableRows
              selectableRowsComponent={CustomCheckbox}
              className="custom-table auto-height-table"
              sortIcon={<SortIcon sortDirection={brandSortDirection} />}
              onSort={handleBrandSort}
              sortField={brandSortedField}
              defaultSortAsc={true}
              onSelectedRowsChange={handleSelectedBrandRowChange}
              pagination
              paginationPerPage={brandPagination.perPage}
              selectableRowsHighlight
              paginationRowsPerPageOptions={[10]}
              paginationComponentOptions={{
                rowsPerPageText: 'Rows per page:',
                rangeSeparatorText: 'of',
                noRowsPerPage: true,
              }}
              paginationComponent={() => (
                <CommonPagination
                  selectedCount={selectedBrands.length}
                  total={brandPagination.total}
                  page={brandPagination.page}
                  perPage={brandPagination.perPage}
                  onPageChange={(page) =>
                    setBrandPagination((prev) => ({
                      ...prev,
                      page,
                    }))
                  }
                />
              )}
            />
          </TabPanel>

          <TabPanel>
            <div className="flex items-center justify-between gap-2.5 bg-white py-2 px-3 rounded-tl-xl rounded-tr-xl border border-border-color ">
              <h3 className="text-sm font-semibold">Franchise Management</h3>
              <button
                onClick={() => {
                  setIsAddModalOpen(true);
                  setIsEditMode(false);
                  setCurrentEditData(null);
                }}
                className="btn flex items-center gap-1"
              >
                <span className="icon icon-plus text-base" />
                Add Franchise
              </button>
            </div>
            <DataTable
              columns={franchiseColumns}
              data={franchises}
              customStyles={customStyles}
              selectableRows
              selectableRowsComponent={CustomCheckbox}
              className="custom-table auto-height-table"
              sortIcon={<SortIcon sortDirection={franchiseSortDirection} />}
              onSort={handleFranchiseSort}
              sortField={franchiseSortedField}
              defaultSortAsc={true}
              onSelectedRowsChange={handleSelectedFranchiseRowChange}
              pagination
              paginationPerPage={franchisePagination.perPage}
              selectableRowsHighlight
              paginationRowsPerPageOptions={[10]}
              paginationComponentOptions={{
                rowsPerPageText: 'Rows per page:',
                rangeSeparatorText: 'of',
                noRowsPerPage: true,
              }}
              paginationComponent={() => (
                <CommonPagination
                  selectedCount={selectedFranchises.length}
                  total={franchisePagination.total}
                  page={franchisePagination.page}
                  perPage={franchisePagination.perPage}
                  onPageChange={(page) =>
                    setFranchisePagination((prev) => ({
                      ...prev,
                      page,
                    }))
                  }
                />
              )}
            />
          </TabPanel>
        </TabPanels>
      </TabGroup>

      <AddBrandFranchiseModal
        isOpen={isAddModalOpen}
        onClose={handleModalClose}
        selectedTab={selectedTab}
        isEditMode={isEditMode}
        editData={currentEditData}
        handleBrandSubmit={handleBrandFormSubmit}
        handleFranchiseSubmit={handleFranchiseFormSubmit}
        brandOptions={brands.map((brand) => ({
          label: brand.name,
          value: brand.id,
        }))}
      />
    </div>
  );
};

export default BrandFranchiseManagement;