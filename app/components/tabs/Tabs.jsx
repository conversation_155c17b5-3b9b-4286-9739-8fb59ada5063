import React from 'react';

export const TabGroup = ({ children, selectedIndex, onChange }) => {
  return <div className="w-full">{children}</div>;
};

export const TabList = ({ children }) => {
  return (
    <div className="flex gap-5 py-4 pb-0 border-b border-border-color sticky top-0 bg-surface-100 z-10">
      {children}
    </div>
  );
};

export const TabButton = ({ selected, onClick, children }) => {
  return (
    <button
      onClick={onClick}
      className={`pb-3 px-4 text-sm font-semibold border-b-2
        ${selected 
          ? 'border-primary-500 text-primary-500'
          : 'border-transparent text-gray-400 hover:text-primary-500'
        }`}
    >
      {children}
    </button>
  );
};

export const TabPanels = ({ children, selectedIndex }) => {
  return (
    <div className="mt-4">
      {React.Children.toArray(children)[selectedIndex]}
    </div>
  );
};

export const TabPanel = ({ children }) => {
  return <div>{children}</div>;
};