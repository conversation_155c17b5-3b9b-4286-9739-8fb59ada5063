import React from 'react';
import Image from 'next/image';

const TreeView = ({ data, onSelect, selectedId }) => {
  const [expandedItems, setExpandedItems] = React.useState({});

  const toggleExpand = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const renderTreeItem = (item) => {
    const hasSubcategories = item.subcategories && item.subcategories.length > 0;
    const isExpanded = expandedItems[item.id];
    const isSelected = selectedId === item.id;

    return (
      <div key={item.id} className="not-last:border-b border-border-color">
        <div
          className={`flex items-center py-2 px-3 cursor-pointer hover:bg-surface-100 ${
            isSelected ? 'bg-surface-200' : ''
          }`}
          onClick={() => onSelect(item)}
        >
          {hasSubcategories && (
            <button
              className="p-1 hover:bg-surface-200 rounded-full mr-1"
              onClick={(e) => {
                e.stopPropagation();
                toggleExpand(item.id);
              }}
            >
              {isExpanded ? (
                <span className="icon icon-caret-down" />
              ) : (
                <span className="icon icon-caret-right" />
              )}
            </button>
          )}
          {!hasSubcategories && <div className="w-7" />}
          
          <div className="flex items-center flex-1">
            {item.image && item.image.length > 0 && (
              <Image 
              src={item.image[0].preview} 
                alt={item.name}
                className="w-10 h-10 mr-2 object-cover rounded-lg"
                width={40}
                height={40}
                priority
              />
            )}
            <span className={`text-sm font-semibold ${!item.status ? 'text-gray-400' : ''}`}>
              {item.name}
            </span>
          </div>
          
          <div className="flex items-center gap-2">
            {item.displayOnHomepage && (
              <span className="text-xs bg-primary-500/5 text-primary-500 px-2 py-0.5 rounded">
                Homepage
              </span>
            )}
            <span className={`w-2 h-2 rounded-full ${
              item.status ? 'bg-success-500' : 'bg-gray-300'
            }`} />
          </div>
        </div>

        {hasSubcategories && isExpanded && (
          <div className="ml-6 pl-4 border-l border-border-color">
            {item.subcategories.map(subItem => (
              <div
                key={subItem.id}
                className={`flex items-center py-2 px-3 rounded-lg cursor-pointer hover:text-primary-500 transition-base ${
                  selectedId === subItem.id ? 'bg-surface-200' : ''
                }`}
                onClick={() => onSelect(subItem)}
              >
                {subItem.image && subItem.image.length > 0 && (
                  <Image 
                    src={subItem.image[0].preview} 
                    alt={subItem.name}
                    className="w-8 h-8 mr-2 object-cover rounded-lg"
                    width={32}
                    height={32}
                    priority
                  />
                )}
                <span className={`text-sm ${!subItem.status ? 'text-gray-400' : ''}`}>
                  {subItem.name}
                </span>
                <span className={`ml-auto w-2 h-2 rounded-full ${
                  subItem.status ? 'bg-success-500' : 'bg-gray-300'
                }`} />
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="overflow-y-auto max-h-[calc(100vh-235px)]">
      {data.map(renderTreeItem)}
      {data.length === 0 && (
        <div className="text-center text-gray-500 py-8 text-sm">
          No categories found
        </div>
      )}
    </div>
  );
};

export default TreeView;