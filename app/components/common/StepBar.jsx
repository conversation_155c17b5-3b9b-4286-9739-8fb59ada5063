import React from 'react';

const StepBar = ({ steps, activeStep, completedSteps = [], onStepClick }) => {
  const handleStepClick = (index) => {
    // Allow navigation to any previous step or completed step
    if (completedSteps.includes(index) || index <= activeStep) {
      onStepClick(index);
    }
  };

  return (
    <div className="flex flex-col justify-between h-full mt-8">
      <div className="flex flex-col gap-y-2">
        {steps.map((step, index) => (
          <React.Fragment key={index}>
            <div 
              className={`flex items-center p-2 rounded-lg transition-all duration-200 ${
                (completedSteps.includes(index) || index <= activeStep) 
                  ? 'cursor-pointer hover:bg-gray-50 active:bg-gray-100' 
                  : 'cursor-not-allowed opacity-60'
              }`}
              onClick={() => handleStepClick(index)}
              role="button"
              tabIndex={0}
              aria-label={`Go to ${step} step`}
            >
              <div className={`w-5 h-5 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                completedSteps.includes(index)
                  ? 'bg-success-500 text-white'
                  : index <= activeStep 
                    ? 'bg-primary-500 text-white' 
                    : 'bg-gray-100 text-gray-400'
              }`}>
                {completedSteps.includes(index) ? (
                  <span className="icon icon-check text-[10px]" />
                ) : (
                  index + 1
                )}
              </div>
              <span className={`ml-2 text-sm font-medium transition-colors ${
                completedSteps.includes(index)
                  ? 'text-success-500'
                  : index <= activeStep 
                    ? 'text-dark-500' 
                    : 'text-gray-400'
              }`}>
                {step}
              </span>
            </div>
            {index < steps.length - 1 && (
              <div className={`h-[20px] w-[2px] mx-2 transition-colors ${
                completedSteps.includes(index)
                  ? 'bg-success-500'
                  : index < activeStep 
                    ? 'bg-primary-500' 
                    : 'bg-gray-200'
              }`} />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

export default StepBar;