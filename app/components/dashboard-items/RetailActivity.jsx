import React from 'react';

const activityData = [
  {
    time: '2 mins ago',
    message: 'Buyer requested refund for Order #HSU-19384',
  },
  {
    time: '1 hr ago',
    message: 'Buyer cancelled item in Order #HSU-19384',
  },
  {
    time: '2 mins ago',
    message: 'Return approved for SKU-24BM04-BLK-LEA',
  },
  {
    time: '1 min ago',
    message: 'New order #HSU-19384 placed',
  },
];

const RetailActivity = () => {
  return (
    <div className="card !p-0">
      <div className="flex items-center gap-2 p-4">
        <h2 className="text-sm font-bold">Retail Activity</h2>
      </div>

      <ul className="flex flex-col max-h-[235px] overflow-y-auto">
        {activityData.map((activity, index) => (
          <li
            key={index}
            className="flex items-center justify-between gap-2 px-4 2xl:px-5 py-1.5 2xl:py-2.5 not-[:first-child]:border-t border-border-color"
          >
            <div className="flex gap-4">
              <span className="text-sm font-medium min-w-[100px]">
                {activity.time}
              </span>
              <span className="text-sm">{activity.message}</span>
            </div>
            <button className="btn btn-outline-gray !font-medium !px-4 text-nowrap">
              View Request
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default RetailActivity;
