import React from 'react';

const PermissionGroup = ({ title, permissions, onChange }) => {
  return (
    <div className="mb-6 last:mb-0">
      <div className="flex items-center gap-2 mb-3">
        <h3 className="text-sm font-semibold capitalize">
          {title.replace(/([A-Z])/g, ' $1').trim()}
        </h3>
      </div>
      <div className="flex flex-wrap gap-3">
        {permissions.map((permission) => (
          <label
            key={`${title}-${permission}`}
            className="inline-flex items-center gap-2 cursor-pointer"
          >
            <input
              type="checkbox"
              className="form-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              onChange={(e) => onChange(title, permission, e.target.checked)}
            />
            <span className="text-sm text-gray-700">{permission}</span>
          </label>
        ))}
      </div>
    </div>
  );
};

export default PermissionGroup;