'use client';

import React from 'react';
import dynamic from 'next/dynamic';

const ApexCharts = dynamic(
  () => import('react-apexcharts').then((mod) => mod.default),
  {
    ssr: false,
  }
);

const StatCard = ({
  title,
  value,
  percentage,
  isPositive,
  chartData,
  bgColor,
}) => {
  const chartOptions = {
    chart: {
      type: 'area',
      toolbar: {
        show: false,
      },
      sparkline: {
        enabled: true,
      },
    },
    grid: {
      show: false,
      padding: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      },
    },
    stroke: {
      curve: 'smooth',
      width: 3,
    },
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.2,
        stops: [0, 90, 100],
      },
    },
    colors: [isPositive ? '#10B981' : '#EF4444'],
    tooltip: {
      enabled: false,
    },
    xaxis: {
      labels: {
        show: false,
      },
      axisBorder: {
        show: false,
      },
      axisTicks: {
        show: false,
      },
    },
    yaxis: {
      show: false,
    },
    dataLabels: {
      enabled: false,
    },
    markers: {
      size: 0,
    },
  };

  const series = [
    {
      name: title,
      data: chartData,
    },
  ];

  return (
    <div className="card flex gap-2 justify-between items-center">
      <div className="flex flex-col gap-3 2xl:gap-5">
        <h3 className="text-xs font-semibold text-dark-500">{title}</h3>
        <span className="text-xl 2xl:text-3xl font-bold">{value}</span>
      </div>
      <div className="flex flex-col justify-between items-end gap-3 2xl:gap-5">
        <span
          className={`inline-flex items-center gap-1 text-sm font-medium text-nowrap ${isPositive ? 'text-green-500' : 'text-red-500'}`}
        >
          <span
            className={`icon ${isPositive ? 'icon-trend-up' : 'icon-trend-down'}`}
          />
          {percentage}
          <span className="text-dark-500">% this month</span>
        </span>
        <ApexCharts
          options={chartOptions}
          series={series}
          type="area"
          height="32px"
          width="80px"
        />
      </div>
    </div>
  );
};

const TransactionStats = () => {
  const stats = [
    {
      title: 'Total Earnings',
      value: '$387,500',
      percentage: '25',
      isPositive: true,
      chartData: [10, 12, 9, 14, 15, 13, 14],
    },
    {
      title: 'Pending',
      value: '$85,000',
      percentage: '25',
      isPositive: true,
      chartData: [5, 7, 4, 5, 6, 4, 5],
    },
    {
      title: 'Disputed Amount',
      value: '$120,000',
      percentage: '10',
      isPositive: false,
      chartData: [100, 110, 105, 115, 110, 120, 115],
    },
    {
      title: 'Platform Fees',
      value: '$25,000',
      percentage: '5',
      isPositive: true,
      chartData: [3, 4, 3, 5, 4, 5, 5],
    },
    {
      title: 'Net Receivable',
      value: '$15,000',
      percentage: '55',
      isPositive: true,
      chartData: [3, 4, 3, 5, 4, 5, 5],
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-5 gap-3 mb-3">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};

export default TransactionStats;
