'use client';
import React, { useState, useMemo } from 'react';
import Image from 'next/image';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import TreeView from '../common/TreeView';
import ImageUpload from '../Inputs/ImageUpload';
import InputField from '../Inputs/InputField';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import ToggleSwitch from '../Inputs/ToggleSwitch';
import SelectField from '../Inputs/SelectField';
import { showErrorToast, showSuccessToast } from '@/utils/function';

// Utility functions for category management
const generateSlug = (name) => {
  return name
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

const validateSlug = (slug) => {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
};

const checkCircularNesting = (categories, parentId, childId) => {
  if (parentId === childId) return true;

  const findCategory = (cats, id) => {
    for (const cat of cats) {
      if (cat.id === id) return cat;
      const found = findCategory(cat.subcategories || [], id);
      if (found) return found;
    }
    return null;
  };

  const hasCircularReference = (currentId, targetId) => {
    const category = findCategory(categories, currentId);
    if (!category) return false;

    for (const sub of category.subcategories || []) {
      if (sub.id === targetId) return true;
      if (hasCircularReference(sub.id, targetId)) return true;
    }
    return false;
  };

  return hasCircularReference(childId, parentId);
};

const canDeleteCategory = (category, allCategories) => {
  // Check if category has products (mock check - replace with actual API call)
  const hasProducts = false; // This would be an API call

  // Check if category has attributes mapped (mock check)
  const hasAttributes = false; // This would be an API call

  return { canDelete: !hasProducts && !hasAttributes, reason: hasProducts ? 'Category has products mapped' : hasAttributes ? 'Category has attributes mapped' : null };
};

const canActivateCategory = (category) => {
  // A category can only be active if it has at least one subcategory
  return category.subcategories && category.subcategories.length > 0;
};

const CategoryManagement = () => {
  const [images, setImages] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [formType, setFormType] = useState('category');
  const [editingCategory, setEditingCategory] = useState(null);

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [parentFilter, setParentFilter] = useState('all');
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Enhanced Validation Schema
  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .required('Name is required')
      .max(100, 'Name must be less than 100 characters')
      .test('unique-name', 'Name already exists', function(value) {
        if (!value) return true;
        const { parent } = this;
        const currentId = parent.id;

        if (formType === 'category') {
          return !categories.some(cat =>
            cat.id !== currentId &&
            cat.name.toLowerCase() === value.toLowerCase()
          );
        } else {
          if (!selectedCategory) return true;
          return !selectedCategory.subcategories.some(sub =>
            sub.id !== currentId &&
            sub.name.toLowerCase() === value.toLowerCase()
          );
        }
      }),
    slug: Yup.string()
      .test('valid-slug', 'Slug must be URL-friendly (lowercase letters, numbers, and hyphens only)', function(value) {
        if (!value) return true;
        return validateSlug(value);
      })
      .test('unique-slug', 'Slug already exists', function(value) {
        if (!value) return true;
        const { parent } = this;
        const currentId = parent.id;
        const slug = value || generateSlug(parent.name);

        if (formType === 'category') {
          return !categories.some(cat =>
            cat.id !== currentId &&
            cat.slug === slug
          );
        } else {
          if (!selectedCategory) return true;
          return !selectedCategory.subcategories.some(sub =>
            sub.id !== currentId &&
            sub.slug === slug
          );
        }
      }),
    description: Yup.string()
      .max(500, 'Description must be less than 500 characters'),
    displayOnHomepage: Yup.boolean(),
    status: Yup.boolean()
      .test('category-activation', 'Category must have at least one subcategory to be activated', function(value) {
        if (formType !== 'category' || !value) return true;
        const { parent } = this;
        const currentId = parent.id;

        if (editingCategory) {
          return canActivateCategory(editingCategory);
        }
        return true; // Allow for new categories
      }),
    // image: Yup.mixed()
    //   .test('file-size', 'Image must be less than 2MB', function(value) {
    //     if (!value || !value.length) return true;
    //     return value[0].size <= 2048576; // 2MB
    //   })
    //   .test('file-type', 'Only image files are allowed', function(value) {
    //     if (!value || !value.length) return true;
    //     return value[0].type.startsWith('image/');
    //   }),
    // icon: Yup.mixed()
    //   .test('file-size', 'Icon must be less than 2MB', function(value) {
    //     if (!value || !value.length) return true;
    //     return value[0].size <= 2048576; // 2MB
    //   })
    //   .test('file-type', 'Only image files are allowed', function(value) {
    //     if (!value || !value.length) return true;
    //     return value[0].type.startsWith('image/');
    //   })
  });

  const getInitialValues = () => {
    if (editingCategory) {
      return {
        id: editingCategory.id,
        name: editingCategory.name || '',
        slug: editingCategory.slug || '',
        image: editingCategory.image || null,
        icon: editingCategory.icon || null,
        description: editingCategory.description || '',
        displayOnHomepage: editingCategory.displayOnHomepage || false,
        status: editingCategory.status !== undefined ? editingCategory.status : true,
        sortOrder: editingCategory.sortOrder || 0
      };
    }
    return {
      name: '',
      slug: '',
      image: null,
      icon: null,
      description: '',
      displayOnHomepage: false,
      status: true,
      sortOrder: 0
    };
  };

  // Filtered categories based on search and filters
  const filteredCategories = useMemo(() => {
    let filtered = [...categories];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(cat =>
        cat.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cat.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        cat.subcategories?.some(sub =>
          sub.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.description?.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      const isActive = statusFilter === 'active';
      filtered = filtered.filter(cat => cat.status === isActive);
    }

    return filtered;
  }, [categories, searchTerm, statusFilter]);

  // Filter options
  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' }
  ];

  const parentOptions = [
    { value: 'all', label: 'All Categories' },
    ...categories.map(cat => ({ value: cat.id, label: cat.name }))
  ];

  const handleFormSubmit = (values, { resetForm }) => {
    try {
      // Generate slug if not provided
      const slug = values.slug || generateSlug(values.name);

      if (formType === 'category') {
        if (editingCategory) {
          // Update existing category
          const updatedCategories = categories.map(cat => {
            if (cat.id === editingCategory.id) {
              return {
                ...cat,
                ...values,
                slug,
                updatedAt: new Date().toISOString()
              };
            }
            return cat;
          });
          setCategories(updatedCategories);
          showSuccessToast('Category updated successfully');
        } else {
          // Create new category
          const newCategory = {
            id: Date.now(),
            ...values,
            slug,
            subcategories: [],
            sortOrder: categories.length,
            createdAt: new Date().toISOString()
          };
          setCategories([...categories, newCategory]);
          showSuccessToast('Category created successfully');
        }
      } else {
        if (!selectedCategory) {
          showErrorToast('Please select a parent category first');
          return;
        }

        if (editingCategory) {
          // Update existing subcategory
          const updatedCategories = categories.map(cat => {
            if (cat.id === selectedCategory.id) {
              return {
                ...cat,
                subcategories: cat.subcategories.map(sub => {
                  if (sub.id === editingCategory.id) {
                    return {
                      ...sub,
                      ...values,
                      slug,
                      updatedAt: new Date().toISOString()
                    };
                  }
                  return sub;
                })
              };
            }
            return cat;
          });
          setCategories(updatedCategories);
          showSuccessToast('Subcategory updated successfully');
        } else {
          // Create new subcategory
          const newSubcategory = {
            id: Date.now(),
            ...values,
            slug,
            parentId: selectedCategory.id,
            sortOrder: selectedCategory.subcategories.length,
            createdAt: new Date().toISOString()
          };

          const updatedCategories = categories.map(cat => {
            if (cat.id === selectedCategory.id) {
              return {
                ...cat,
                subcategories: [...cat.subcategories, newSubcategory]
              };
            }
            return cat;
          });
          setCategories(updatedCategories);
          showSuccessToast('Subcategory created successfully');
        }
      }

      setIsDrawerOpen(false);
      setEditingCategory(null);
      resetForm();
    } catch (error) {
      showErrorToast('An error occurred while saving');
      console.error('Form submission error:', error);
    }
  };

  // Handler functions
  const handleEdit = (item, isSubcategory = false) => {
    setEditingCategory(item);
    setFormType(isSubcategory ? 'subcategory' : 'category');
    setIsDrawerOpen(true);
  };

  const handleDelete = (itemId, isSubcategory = false) => {
    if (isSubcategory) {
      if (!selectedCategory) return;

      const subcategory = selectedCategory.subcategories.find(sub => sub.id === itemId);
      if (!subcategory) return;

      const { canDelete, reason } = canDeleteCategory(subcategory, categories);
      if (!canDelete) {
        showErrorToast(reason);
        return;
      }

      if (window.confirm('Are you sure you want to delete this subcategory?')) {
        const updatedCategories = categories.map(cat => {
          if (cat.id === selectedCategory.id) {
            return {
              ...cat,
              subcategories: cat.subcategories.filter(sub => sub.id !== itemId)
            };
          }
          return cat;
        });
        setCategories(updatedCategories);
        showSuccessToast('Subcategory deleted successfully');
      }
    } else {
      const category = categories.find(cat => cat.id === itemId);
      if (!category) return;

      const { canDelete, reason } = canDeleteCategory(category, categories);
      if (!canDelete) {
        showErrorToast(reason);
        return;
      }

      if (window.confirm('Are you sure you want to delete this category and all its subcategories?')) {
        setCategories(categories.filter(cat => cat.id !== itemId));
        if (selectedCategory && selectedCategory.id === itemId) {
          setSelectedCategory(null);
        }
        showSuccessToast('Category deleted successfully');
      }
    }
  };

  const handleStatusToggle = (itemId, isSubcategory = false) => {
    if (isSubcategory) {
      if (!selectedCategory) return;

      const updatedCategories = categories.map(cat => {
        if (cat.id === selectedCategory.id) {
          return {
            ...cat,
            subcategories: cat.subcategories.map(sub => {
              if (sub.id === itemId) {
                return { ...sub, status: !sub.status };
              }
              return sub;
            })
          };
        }
        return cat;
      });
      setCategories(updatedCategories);
    } else {
      const category = categories.find(cat => cat.id === itemId);
      if (!category) return;

      // If deactivating, also deactivate all subcategories
      const updatedCategories = categories.map(cat => {
        if (cat.id === itemId) {
          const newStatus = !cat.status;
          return {
            ...cat,
            status: newStatus,
            subcategories: newStatus ? cat.subcategories : cat.subcategories.map(sub => ({ ...sub, status: false }))
          };
        }
        return cat;
      });
      setCategories(updatedCategories);
    }
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (!over || active.id === over.id) return;

    // Handle category reordering
    const activeIndex = categories.findIndex(cat => cat.id === active.id);
    const overIndex = categories.findIndex(cat => cat.id === over.id);

    if (activeIndex !== -1 && overIndex !== -1) {
      const newCategories = [...categories];
      const [movedCategory] = newCategories.splice(activeIndex, 1);
      newCategories.splice(overIndex, 0, movedCategory);

      // Update sort orders
      const updatedCategories = newCategories.map((cat, index) => ({
        ...cat,
        sortOrder: index
      }));

      setCategories(updatedCategories);
      showSuccessToast('Category order updated');
    }
  };

  const openAddForm = (type) => {
    setEditingCategory(null);
    setFormType(type);
    setIsDrawerOpen(true);
  };

  return (
    <>
      <div className="flex flex-col h-full bg-white rounded-xl border border-border-color">
        {/* Filters and Search Bar */}
        <div className="flex items-center justify-between gap-4 p-4 border-b border-border-color">
          <div className="flex items-center gap-3 flex-1">
            <InputField
              type="search"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              leftIcon="icon-search"
              marginBottom="mb-0"
              inputClassName="form-control !rounded-lg !min-h-[36px] !max-h-[36px] !py-2 !px-3"
            />

            <SelectField
              options={statusOptions}
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              placeholder="Filter by status"
              className="single-select min-w-[150px]"
            />

            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className={`btn btn-outline-gray flex items-center gap-2 ${isFilterOpen ? 'bg-surface-100' : ''}`}
            >
              <span className="icon icon-funnel text-base" />
              Filters
            </button>
          </div>

          <button
            onClick={() => openAddForm('category')}
            className="btn btn-primary flex items-center gap-2"
          >
            <span className="icon icon-plus text-base" />
            Add Category
          </button>
        </div>

        {/* Advanced Filters Panel */}
        {isFilterOpen && (
          <div className="p-4 bg-surface-50 border-b border-border-color">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Parent Category:</span>
                <SelectField
                  options={parentOptions}
                  value={parentFilter}
                  onChange={(e) => setParentFilter(e.target.value)}
                  placeholder="All Categories"
                  className="single-select min-w-[200px]"
                />
              </div>

              <button
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setParentFilter('all');
                }}
                className="btn btn-outline-danger text-sm"
              >
                Clear Filters
              </button>
            </div>
          </div>
        )}

        <div className="flex flex-1 overflow-hidden">
          {/* Left Column - Category Tree */}
          <div className="w-1/3 border-r border-border-color flex flex-col">
            <div className="flex justify-between items-center px-4 py-4 border-b border-border-color min-h-[55px]">
              <h2 className="text-base font-bold">
                Categories ({filteredCategories.length})
              </h2>
            </div>

            <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
              <SortableContext items={filteredCategories.map(cat => cat.id)} strategy={verticalListSortingStrategy}>
                <TreeView
                  data={filteredCategories}
                  onSelect={setSelectedCategory}
                  selectedId={selectedCategory?.id}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onStatusToggle={handleStatusToggle}
                  canDelete={canDeleteCategory}
                />
              </SortableContext>
            </DndContext>
          </div>

          {/* Right Panel - Subcategories */}
          <div className="w-2/3 flex flex-col">
            <div className="flex justify-between items-center px-4 py-4 border-b border-border-color">
              <h2 className="text-base font-bold">
                {selectedCategory ? (
                  <div className="flex items-center gap-2">
                    <span>{selectedCategory.name} - Subcategories</span>
                    <span className="text-xs bg-surface-100 text-gray-400 px-2 py-1 rounded">
                      {selectedCategory.subcategories?.length || 0} items
                    </span>
                  </div>
                ) : (
                  'Select a Category'
                )}
              </h2>
              {selectedCategory && (
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleStatusToggle(selectedCategory.id, false)}
                    className={`btn btn-sm ${selectedCategory.status ? 'btn-outline-danger' : 'btn-outline-success'}`}
                  >
                    {selectedCategory.status ? 'Deactivate' : 'Activate'}
                  </button>
                  <button
                    onClick={() => openAddForm('subcategory')}
                    className="btn btn-sm btn-primary"
                  >
                    Add Subcategory
                  </button>
                </div>
              )}
            </div>
            {selectedCategory ? (
              selectedCategory.subcategories && selectedCategory.subcategories.length > 0 ? (
                <div className="flex-1 overflow-y-auto">
                  <div className="grid grid-cols-[repeat(auto-fit,minmax(280px,1fr))] gap-4 p-4">
                    {selectedCategory.subcategories.map(sub => {
                      const { canDelete, reason } = canDeleteCategory(sub, categories);
                      return (
                        <div
                          key={sub.id}
                          className="bg-white p-4 rounded-lg border border-border-color hover:shadow-sm transition-all"
                        >
                          <div className="flex items-start gap-3">
                            {sub.image && sub.image.length > 0 && (
                              <div className="w-16 h-16 flex-shrink-0 rounded-lg bg-surface-100 flex items-center justify-center overflow-hidden">
                                <Image
                                  src={sub.image[0].preview}
                                  alt={sub.name}
                                  className="w-16 h-16 object-cover"
                                  width={64}
                                  height={64}
                                  priority
                                />
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-start justify-between gap-2">
                                <div className="flex flex-col">
                                  <h3 className="font-semibold text-sm text-dark-500 truncate">{sub.name}</h3>
                                  {sub.slug && (
                                    <p className="text-xs text-gray-400 mt-1">/{sub.slug}</p>
                                  )}

                                  {sub.description && (
                                    <p className="text-xs text-gray-400 mt-2 line-clamp-2">{sub.description}</p>
                                  )}
                                </div>
                                <div className="flex flex-col items-center gap-1 flex-shrink-0">
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    sub.status ? 'bg-success-500/10 text-success-500' : 'bg-gray-500/10 text-gray-400'
                                  }`}>
                                    {sub.status ? 'Active' : 'Inactive'}
                                  </span>

                                  <div className="flex items-center gap-1">
                                    <button
                                      onClick={() => handleEdit(sub, true)}
                                      className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
                                      title="Edit subcategory"
                                    >
                                      <span className="icon icon-pencil-line text-base" />
                                    </button>
                                    <button
                                      onClick={() => handleDelete(sub.id, true)}
                                      disabled={!canDelete}
                                      className={`flex justify-center items-center h-8 w-8 p-2 text-lg rounded-lg cursor-pointer transition-base ${
                                        canDelete
                                          ? 'hover:bg-danger-500/10 hover:text-danger-500'
                                          : 'opacity-50 cursor-not-allowed'
                                      }`}
                                      title={canDelete ? 'Delete subcategory' : reason}
                                    >
                                      <span className="icon icon-trash text-base" />
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center text-center p-8 flex-1">
                  <div className="w-16 h-16 bg-surface-100 rounded-full flex items-center justify-center mb-4">
                    <span className="icon icon-folder text-2xl text-gray-400" />
                  </div>
                  <p className="text-gray-500 mb-2 font-medium">No subcategories available</p>
                  <p className="text-sm text-gray-400 mb-4">Create subcategories to organize your products better</p>
                  <button
                    onClick={() => openAddForm('subcategory')}
                    className="btn btn-primary"
                  >
                    Add First Subcategory
                  </button>
                </div>
              )
            ) : (
              <div className="flex flex-col items-center justify-center text-center p-8 flex-1">
                <div className="w-16 h-16 bg-surface-100 rounded-full flex items-center justify-center mb-4">
                  <span className="icon icon-list-bullets text-2xl text-gray-400" />
                </div>
                <p className="text-gray-500 text-sm font-medium mb-2">Select a category to view subcategories</p>
                <p className="text-xs text-gray-400">Choose a category from the left panel to manage its subcategories</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Add/Edit Drawer */}
      <BaseOffCanvas
        isOpen={isDrawerOpen}
        onClose={() => {
          setIsDrawerOpen(false);
          setEditingCategory(null);
        }}
        size="sm"
        title={`${editingCategory ? 'Edit' : 'Add'} ${formType === 'category' ? 'Category' : 'Subcategory'}`}
      >
        <Formik
          initialValues={getInitialValues()}
          validationSchema={validationSchema}
          onSubmit={handleFormSubmit}
          enableReinitialize
        >
          {({ values, errors, touched, setFieldValue, handleChange }) => (
            <Form>
              <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
                <InputField
                  id="name"
                  type="text"
                  label={formType === 'category' ? 'Category Name' : 'Subcategory Name'}
                  name="name"
                  value={values.name}
                  onChange={(e) => {
                    handleChange(e);
                    // Auto-generate slug if slug field is empty
                    if (!values.slug) {
                      setFieldValue('slug', generateSlug(e.target.value));
                    }
                  }}
                  error={touched.name && errors.name}
                  required
                  maxLength={100}
                />

                <InputField
                  id="slug"
                  type="text"
                  label="Slug"
                  placeholder="URL-friendly identifier"
                  name="slug"
                  value={values.slug}
                  onChange={handleChange}
                  error={touched.slug && errors.slug}
                  helpText="Used in URLs. Only lowercase letters, numbers, and hyphens allowed."
                />

                <div className="grid grid-cols-1 gap-4">
                  <ImageUpload
                    heading={`${formType === 'category' ? 'Category' : 'Subcategory'} Image`}
                    value={values.image}
                    setImages={(images) => setFieldValue('image', images)}
                    onChange={(file) => setFieldValue('image', file)}
                    aspectRatio={1}
                    tooltipContent="Upload a square image (1:1 ratio), max 2MB"
                    maxSize={2048576}
                    formik={Formik}
                    name="image"
                    // error={touched.image && errors.image}
                  />

                  <ImageUpload
                    heading={`${formType === 'category' ? 'Category' : 'Subcategory'} Icon`}
                    value={values.icon}
                    setImages={(images) => setFieldValue('icon', images)}
                    onChange={(file) => setFieldValue('icon', file)}
                    tooltipContent="Upload an icon, max 2MB"
                    maxSize={2048576}
                    formik={Formik}
                    name="icon"
                    // error={touched.icon && errors.icon}
                  />
                </div>

                <div>
                  <label className="form-label">Description</label>
                  <textarea
                    name="description"
                    value={values.description}
                    onChange={handleChange}
                    className="form-control"
                    rows={4}
                    placeholder={`Enter ${formType === 'category' ? 'category' : 'subcategory'} description...`}
                    maxLength={500}
                  />
                  <div className="flex justify-between items-center mt-1">
                    {touched.description && errors.description ? (
                      <div className="text-danger-500 text-sm">{errors.description}</div>
                    ) : (
                      <div className="text-xs text-gray-400">Optional description for this {formType}</div>
                    )}
                    <div className="text-xs text-gray-400">
                      {values.description?.length || 0}/500
                    </div>
                  </div>
                </div>

                {formType === 'category' && (
                  <CustomCheckbox
                    id="displayOnHomepage"
                    name="displayOnHomepage"
                    label="Display on Homepage"
                    checked={values.displayOnHomepage}
                    onChange={(e) => setFieldValue('displayOnHomepage', e.target.checked)}
                    helpText="Show this category on the homepage"
                  />
                )}

                <div className="mb-0">
                  <label className="block text-sm font-medium mb-2">Status</label>
                  <div className="flex items-center gap-3">
                    <ToggleSwitch
                      checked={values.status}
                      onChange={(e) => {
                        const newStatus = e.target.checked;
                        setFieldValue('status', newStatus);

                        // Show warning for category activation
                        if (formType === 'category' && newStatus && editingCategory && !canActivateCategory(editingCategory)) {
                          showErrorToast('Category must have at least one subcategory to be activated');
                          setFieldValue('status', false);
                        }
                      }}
                    />
                    <span className="text-sm text-gray-400">
                      {values.status ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  {formType === 'category' && values.status && (
                    <div className="text-xs text-gray-500 mt-1">
                      Note: Deactivating a category will also deactivate all its subcategories
                    </div>
                  )}
                  {touched.status && errors.status && (
                    <div className="text-danger-500 text-sm mt-1">{errors.status}</div>
                  )}
                </div>
              </div>

              <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setIsDrawerOpen(false);
                    setEditingCategory(null);
                  }}
                  className="btn btn-outline-gray"
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingCategory ? 'Update' : 'Create'} {formType === 'category' ? 'Category' : 'Subcategory'}
                </button>
              </div>
            </Form>
          )}
        </Formik>
      </BaseOffCanvas>
    </>
  );
};

export default CategoryManagement;