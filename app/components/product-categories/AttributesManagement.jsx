'use client';
import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import AttributeModal from '../modals/AttributeModal';
import SortIcon from '../table/SortIcon';
import CommonPagination from '../table/CommonPagination';
import { Tooltip } from 'react-tooltip';
import { deleteAttributesAsync } from '@/store/api/catalogueManagementApi';
import { useDispatch } from 'react-redux';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${rest.checked ? 'bg-primary-500 border-primary-500' : 'border-gray-200 hover:border-gray-100'
      }`}
  >
    {rest.checked && <span className="icon icon-check-2 text-white text-[8px]" />}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const AttributesManagement = ({ isModalOpen, setIsModalOpen, data }) => {
  const dispatch = useDispatch();

  const [attributes, setAttributes] = useState(data);
  const [selectedRows, setSelectedRows] = useState([]);
  const [sortedField, setSortedField] = useState(null);
  const [sortDirection, setSortDirection] = useState(null);
  const [formData, setFormData] = useState({
    attribute_name: '',
    type: '',
    displayInFilters: false,
    useInComparison: false,
    requiredForProducts: false,
    status: true,
    values: []
  });
  const [error, setError] = useState('');
  const [valueFormData, setValueFormData] = useState({
    label: '',
    code: '',
    value: ''
  });

  const typeOptions = [
    { value: 'text', label: 'Text Label' },
    { value: 'color', label: 'Color Code' },
    { value: 'image', label: 'Image' }
  ];

  const columns = [
    {
      name: 'Attribute Name',
      selector: row => row.attribute_name,
      sortable: true,
    },
    {
      name: 'Type',
      selector: row => row.type,
      sortable: true,
      cell: row => (
        <span className="capitalize">{row.type}</span>
      )
    },
    {
      name: 'Values',
      selector: row => row?.values?.length,
      sortable: true,
      cell: row => (
        <span>
          {attributes?.map((item) =>
            item?.attribute_values?.map((i) => (
              <React.Fragment key={i?.id || i?.attribute_values}>
                {i?.attribute_values},
              </React.Fragment>
            ))
          )}
        </span>
      )
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span className={`px-2 py-1 rounded-full text-xs ${row.is_visible ? 'bg-success-500/10 text-success-500' : 'bg-gray-500/10 text-gray-600'
          }`}>
          {row.is_visible ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      name: 'Actions',
      sortable: false,
      grow: 0,
      cell: row => (
        <div className="flex gap-2">
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-dark-500/10 hover:text-dark-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit"
            onClick={() => handleEdit(row)}
          >
            <span className="icon icon-pencil-line text-base" />
          </button>
          <button
            className="flex justify-center items-center h-8 w-8 p-2 text-lg hover:bg-danger-500/10 hover:text-danger-500 rounded-lg cursor-pointer transition-base"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete"
            onClick={() => handleDelete(row.id)}
          >
            <span className="icon icon-trash text-base" />
          </button>
          <Tooltip id="edit-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
          <Tooltip id="delete-tooltip" className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded" />
        </div>
      )
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate attribute name (case-insensitive)
    const isDuplicate = attributes.some(
      attr => attr.attribute_name.toLowerCase() === formData.attribute_name.toLowerCase()
    );

    if (isDuplicate) {
      setError('Attribute name already exists');
      return;
    }

    if (formData?.values?.length === 0) {
      setError('At least one value is required');
      return;
    }

    setAttributes([
      ...attributes,
      {
        id: Date.now(),
        ...formData
      }
    ]);

    setIsModalOpen(false);
    setFormData({
      attribute_name: '',
      type: '',
      displayInFilters: false,
      useInComparison: false,
      requiredForProducts: false,
      status: true,
      values: []
    });
    setError('');
  };

  const handleAddValue = () => {
    if (!valueFormData.label) {
      setError('Value label is required');
      return;
    }

    const isDuplicateValue = formData.values.some(
      val => val.label.toLowerCase() === valueFormData.label.toLowerCase()
    );

    if (isDuplicateValue) {
      setError('Value label already exists');
      return;
    }

    setFormData({
      ...formData,
      values: [...formData.values, { ...valueFormData }]
    });
    setValueFormData({ label: '', code: '', value: '' });
    setError('');
  };

  const handleDelete = (id) => {
    // setAttributes(attributes.filter(attr => attr.id !== id));
    dispatch(deleteAttributesAsync(id));
  };

  const handleEdit = (attribute) => {
    setFormData(attribute);
    setIsModalOpen(true);
  };

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#FBFAFA',
        borderRadius: '0',
        height: '37px',
        minHeight: '37px',
        borderBottom: '1px solid #E5E7EB',
      },
    },
    headCells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
        fontWeight: '500',
      },
    },
    cells: {
      style: {
        paddingLeft: '12px',
        paddingRight: '12px',
        fontSize: '13px',
      },
    },
    rows: {
      style: {
        minHeight: '52px',
      },
    },
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={attributes}
        customStyles={customStyles}
        selectableRows
        highlightOnHover
        selectableRowsComponent={CustomCheckbox}
        className="custom-table"
        pagination
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={(column, direction) => {
          setSortedField(column.selector);
          setSortDirection(direction);
        }}
        paginationPerPage={8}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
          noRowsPerPage: true,
        }}
        paginationComponent={(props) => (
          <CommonPagination
            selectedCount={props?.selectedRows?.length}
            total={props.totalRows}
            page={props.currentPage}
            perPage={props.rowsPerPage}
            onPageChange={props.onChangePage}
          />
        )}
      />

      <AttributeModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setFormData({
            attribute_name: '',
            type: '',
            displayInFilters: false,
            useInComparison: false,
            requiredForProducts: false,
            status: true,
            values: []
          });
          setError('');
        }}
        formData={formData}
        setFormData={setFormData}
        error={error}
        valueFormData={valueFormData}
        setValueFormData={setValueFormData}
        handleSubmit={handleSubmit}
        handleAddValue={handleAddValue}
        typeOptions={typeOptions}
      />
    </>
  );
};

export default AttributesManagement;