import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import InputField from '../Inputs/InputField';
import FileUpload from '../Inputs/FileUpload';
import BaseModal from './BaseModal';

const validationSchema = Yup.object({
  ship_company: Yup.string(),
  ship_time: Yup.string(),
  track_number: Yup.string(),
  track_url: Yup.string().url('Please enter a valid URL'),
  delivery_date: Yup.string(),
  policyFile: Yup.mixed()
});

const ShippingDetailModal = ({ isOpen, onClose }) => {

  const initialValues = {
    ship_company: '',
    ship_time: '',
    track_number: '',
    track_url: '',
    delivery_date: '',
    policyFile: null
  };

  const handleSubmit = (values, { setSubmitting }) => {
    setSubmitting(false);
    onClose();
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Add Shipping Details"
      size="md"
    >
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {(formik) => (
          <Form>
            <div className="p-4 max-h-[calc(100dvh_-_140px)] overflow-y-auto">
              <InputField
                id="ship_company"
                name="ship_company"
                type="text"
                label="Shipping Company"
                required={false}
                length={150}
              />

              <InputField
                id="ship_time"
                name="ship_time"
                type="text"
                label="Shipping Time (Days)"
                required={false}
                length={150}
              />

              <InputField
                id="track_number"
                name="track_number"
                type="text"
                label="Tracking Number"
                required={false}
                length={150}
              />

              <InputField
                id="track_url"
                name="track_url"
                type="text"
                label="Tracking URL"
                required={false}
                length={150}
              />

              <InputField
                id="delivery_date"
                name="delivery_date"
                type="text"
                label="Estimated Delivery Date"
                required={false}
                length={150}
              />

              <FileUpload
                name="policyFile"
                value={formik.values.policyFile}
                label="Upload Receipt"
                formik={formik}
                onChange={(file) =>
                  formik.setFieldValue('policyFile', file)
                }
              />
            </div>

            <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
              <button
                type="button"
                className="btn btn-outline-gray"
                onClick={onClose}
              >
                Cancel
              </button>
              <button type="button" className="btn" onClick={onClose}>
                Save
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </BaseModal>
  );
};

export default ShippingDetailModal;
