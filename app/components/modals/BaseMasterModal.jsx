import React from 'react';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import ToggleSwitch from '../Inputs/ToggleSwitch';

const BaseMasterModal = ({ isOpen, onClose, title, fields, formik }) => {
  return (
    <BaseOffCanvas isOpen={isOpen} onClose={onClose} title={title} size="sm">
      <form onSubmit={formik.handleSubmit}>
        <div className="space-y-4 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
          {fields?.map((field) => {
            switch (field.type) {
              case 'text':
                return (
                  <InputField
                    key={field.name}
                    name={field.name}
                    id={field.name}
                    label={field.label}
                    placeholder={field.placeholder}
                    formik={formik}
                  />
                );
              case 'select':
                return (
                  <div className="flex flex-col flex-1 mb-4" key={field.name}>
                    <span className="form-label">
                      {field.label}
                    </span>
                    <SelectField
                      name={field.name}
                      label={field.label}
                      className="single-select"
                      placeholder={field.placeholder}
                      options={field.options}
                      formik={formik}
                    />
                  </div>
                );
              case 'toggle':
                return (
                  <div className="mb-4" key={field.name}>
                    <label className="block text-sm font-medium mb-1">
                      {field.label}
                    </label>
                    <ToggleSwitch
                      checked={formik.values[field.name]}
                      onChange={(e) =>
                        formik.setFieldValue(field.name, e.target.checked)
                      }
                    />
                    {formik.touched[field.name] && formik.errors[field.name] && (
                      <div className="text-danger-500 text-xs mt-1">
                        {formik.errors[field.name]}
                      </div>
                    )}
                  </div>
                );
              default:
                return null;
            }
          })}
        </div>

        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
          >
            Cancel
          </button>
          <button type="submit" className="btn btn-primary">
            Save
          </button>
        </div>
      </form>
    </BaseOffCanvas>
  );
};

export default BaseMasterModal;