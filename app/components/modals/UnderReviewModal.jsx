import React from 'react';
import Image from 'next/image';

const UnderReviewModal = ({ handleLogout }) => {
  return (
    <div className="flex flex-col items-center text-center">
      <Image
        src="/images/review-loader.svg"
        width={100}
        height={100}
        className="object-contain"
        alt="Review profile loader"
        priority
      />

      <h2 className="mt-6 mb-2 text-xl font-bold">
        Your profile is under review
      </h2>
      <p className="text-gray-500 text-sm">
        We're verifying your details to ensure a smooth experience. This process
        may take some time, and we’ll notify you once your profile is approved.
      </p>

      <button
        className="btn mt-6 w-full min-w-[150px]"
        onClick={() => handleLogout()}
      >
        Logout
      </button>
    </div>
  );
};

export default UnderReviewModal;
