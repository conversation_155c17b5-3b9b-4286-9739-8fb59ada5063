'use client'; // Ensure this runs only on the client side

import React, { useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

// Base Modal Component for Next.js
const BaseModal = (props) => {
  const {
    isOpen,
    onClose,
    title,
    paragraph,
    children,
    customClass,
    size = 'md',
  } = props;

  const sizeClasses = {
    sm: 'max-w-[516px]',
    md: 'max-w-[646px]',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
  };

  // Close modal on Escape key press
  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === 'Escape') {
        onClose();
      }
    },
    [onClose]
  );

  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('overflow-hidden'); // Prevent scrolling
      window.addEventListener('keydown', handleKeyDown);
    } else {
      document.body.classList.remove('overflow-hidden');
    }

    return () => {
      document.body.classList.remove('overflow-hidden');
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, handleKeyDown]);

  if (!isOpen) return null;

  return (
    <>
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black/20 z-50 backdrop-blur-[3px]"
        onClick={onClose}
      />

      {/* Modal */}
      <div
        className="fixed inset-0 z-50 flex items-center justify-center p-4"
        aria-modal="true"
        role="dialog"
      >
        <div
          className={`bg-white rounded-xl shadow-xl w-full ${sizeClasses[size]} ${customClass} mx-auto relative`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Modal Header */}
          {(title || paragraph) && (
            <div className="flex justify-between items-start bg-dark-500/5 p-4">
              <div className="flex flex-col w-full gap-2">
                {title && <h2 className="text-sm font-bold">{title}</h2>}
                {paragraph && (
                  <p className="text-sm text-gray-500 max-w-[442px] mx-auto">
                    {paragraph}
                  </p>
                )}
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-danger-500 transition-base cursor-pointer leading-1"
              >
                <span className="icon icon-x text-base align-middle" />
              </button>
            </div>
          )}

          {/* Modal Content */}
          <div>{children}</div>
        </div>
      </div>
    </>
  );
};

// PropTypes for BaseModal
BaseModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  title: PropTypes.string.isRequired,
  children: PropTypes.node,
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
};

export default BaseModal;
