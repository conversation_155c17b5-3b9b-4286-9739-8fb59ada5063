import React from 'react';
import BaseModal from './BaseModal';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';

const AddNewMemberModal = ({ isOpen, onClose, location = null }) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={location ? 'Edit Team Member' : 'Add a New Team Member'}
      size="md"
    >
      <div className="space-y-4 p-4">
        <p className="text-sm text-gray-500 mb-4">Invite a new user, assign a predefined role, and manage their access to your store's operations.</p>
        <InputField
          id="name"
          type="text"
          label="Name"
          placeholder=""
          name="name"
          defaultValue={location?.name}
        />

        <InputField
          id="email"
          type="email"
          label="Email"
          placeholder=""
          name="email"
          defaultValue={location?.email}
        />
        <InputField
          id="mobileNumber"
          type="number"
          label="Mobile Number"
          optional={true}
          placeholder=""
          name="mobileNumber"
          defaultValue={location?.mobileNumber}
        />

        <div className="flex flex-col">
          <span className="form-label">Role</span>
          <SelectField
            name="role"
            className="single-select"
            placeholder=""
            defaultValue={location?.role}
            options={[
              { value: 'admin', label: 'Admin' },
              { value: 'member', label: 'Member' },
            ]}
          />
        </div>
      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button type="button" className="btn" onClick={onClose}>
          Save
        </button>
      </div>
    </BaseModal>
  );
};

export default AddNewMemberModal;
