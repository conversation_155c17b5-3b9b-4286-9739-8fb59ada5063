import React from 'react';
import BaseModal from './BaseModal';
import InputField from '../Inputs/InputField';

const ReviewReturnModal = ({ 
  isOpen, 
  onClose, 
  onConfirm,
  title = "Review Return or Replacement Request",
  message = "You're about to approve the buyer's return or replacement request. Please confirm restocking/handling details before proceeding.",
  confirmButtonText = "Submit",
  cancelButtonText = "Cancel",
  filterText = '',
  setFilterText = () => {},
  location = null 
}) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
    >
      <div className="space-y-4 p-4">
        <p className="text-sm text-gray-500 mb-4">{message}</p>
        <p className="text-sm text-warning-500 mb-4">"Reason: Item is damaged."</p>
        <InputField
          id="restockingFees"
          name="restockingFees"
          type="number"
          label="Enter restocking/handling fees"
          marginBottom="mb-0"
          placeholder=""
          value={'0'}
          required={false}
        />
      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          {cancelButtonText}
        </button>
        <button 
          type="button" 
          className="btn"
          onClick={() => {
            if (onConfirm) {
              onConfirm();
            }
            onClose();
          }}
        >
          {confirmButtonText}
        </button>
      </div>
    </BaseModal>
  );
};

export default ReviewReturnModal;
