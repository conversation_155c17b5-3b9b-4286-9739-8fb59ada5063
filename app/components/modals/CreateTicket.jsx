'use client';
import React, { useState } from 'react';
import BaseModal from './BaseModal';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import ImageUpload from '../Inputs/ImageUpload';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const CreateTicket = ({ isOpen, onClose }) => {
  const [images, setImages] = useState([]);
  const formik = useFormik({
    initialValues: {
      title: '',
      category: '',
      priority: '',
      description: '',
      image: []
    },
    validationSchema: Yup.object({
      title: Yup.string().required('Title is required'),
      category: Yup.string().required('Category is required'),
      priority: Yup.string().required('Priority is required'),
      description: Yup.string().required('Description is required')
    }),
    onSubmit: (values) => {
      onClose();
    }
  });

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Create Ticket"
      size="md"
    >
      <form onSubmit={formik.handleSubmit}>
        <div className="space-y-4 p-4 max-h-[calc(100vh-140px)] overflow-y-auto">
          <p className="text-sm text-gray-500 mb-4">Need help? Submit a support ticket and our team will get back to you shortly. Please provide as many details as possible for faster resolution.</p>
          <InputField
            id="title"
            type="text"
            label="Title/Subject"
            placeholder=""
            name="title"
            formik={formik}
          />

          <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col">
              <span className="form-label">Category</span>
              <SelectField
                name="category"
                className="single-select"
                placeholder=""
                formik={formik}
                options={[
                  { value: 'admin', label: 'Admin' },
                  { value: 'member', label: 'Member' },
                ]}
              />
            </div>
            <div className="flex flex-col">
              <span className="form-label">Priority</span>
              <SelectField
                name="priority"
                className="single-select"
                placeholder=""
                formik={formik}
                options={[
                  { value: 'low', label: 'Low' },
                  { value: 'medium', label: 'Medium' },
                  { value: 'high', label: 'High' },
                ]}
              />
            </div>
          </div>

          <div className="flex flex-col">
            <label className="form-label">Description</label>
            <textarea
              className="form-control"
              name="description"
              rows={4}
              placeholder="Enter description"
              value={formik.values.description}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            />
          </div>

          <ImageUpload
            name="image"
            className="mt-4"
            placeholder="Upload image"
            heading="Attachments"
            showFileInfo={true}
            setImages={setImages}
            maxFiles={3}
            maxSize={3000000}
            uploadText="Add attachments"
            tooltipContent="Upload up to 3 files, each up to 3MB, in .jpg, .jpeg, or .png format."
            formik={formik}
          />

          <div className="inline-flex gap-1.5 items-center text-sm mt-2 bg-info-500/10 text-info-500 py-2 px-4 rounded-lg">
            <span className="icon icon-info text-base" /> 
            Our team typically responds within 24-48 business hours.
          </div>
        </div>
        <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
          <button
            type="button"
            className="btn btn-outline-gray"
            onClick={onClose}
          >
            Cancel
          </button>
          <button type="submit" className="btn">
          Submit Ticket
          </button>
        </div>
      </form>
    </BaseModal>
  );
};

export default CreateTicket;
