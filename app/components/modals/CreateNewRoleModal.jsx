'use client';
import React, { useState, useEffect } from 'react';
import BaseModal from './BaseModal';
import InputField from '../Inputs/InputField';
import CustomCheckbox from '../Inputs/CustomCheckbox';

const INITIAL_PERMISSIONS = {
  productManagement: {
    viewProducts: false,
    addProducts: false,
    editProducts: false,
    deleteProducts: false,
    manageProductsVariants: false,
    bulkUploadProducts: false,
  },
  orderManagement: {
    viewOrders: false,
    manageOrderStatus: false,
    cancelOrders: false,
    manageReturnsAndReplacements: false,
    viewOrderNotesAndHistory: false,
  },
  reportsAndAnalytics: {
    viewSalesReports: false,
    viewRevenueAndEarnings: false,
    downloadReports: false,
  },
  inventoryManagement: {
    viewInventory: false,
    updateStockLevels: false,
    setLowStockAlerts: false,
  },
  accountAndSecurity: {
    manageRolesAndPermissions: false,
    manageProfileDetails: false,
    changePassword: false,
    enableManage2FA: false,
  },
  paymentManagement: {
    viewPayouts: false,
    viewTransactionHistory: false,
  },
  communicationAndSupport: {
    accessMessagesInbox: false,
    raiseSupportTickets: false,
    manageNotificationsAndAlerts: false,
  }
};

const CreateNewRoleModal = ({ 
  isOpen, 
  onClose, 
  onSave,
  initialRole = null,
  location = null 
}) => {
  const [roleName, setRoleName] = useState('');
  const [permissions, setPermissions] = useState(INITIAL_PERMISSIONS);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (initialRole) {
      setRoleName(initialRole.name || '');
      // Safely merge permissions with initial values
      setPermissions(prev => ({
        ...INITIAL_PERMISSIONS,
        ...(initialRole.permissions || {}),
        productManagement: {
          ...INITIAL_PERMISSIONS.productManagement,
          ...(initialRole.permissions?.productManagement || {})
        },
        orderManagement: {
          ...INITIAL_PERMISSIONS.orderManagement,
          ...(initialRole.permissions?.orderManagement || {})
        },
        reportsAndAnalytics: {
          ...INITIAL_PERMISSIONS.reportsAndAnalytics,
          ...(initialRole.permissions?.reportsAndAnalytics || {})
        },
        inventoryManagement: {
          ...INITIAL_PERMISSIONS.inventoryManagement,
          ...(initialRole.permissions?.inventoryManagement || {})
        },
        accountAndSecurity: {
          ...INITIAL_PERMISSIONS.accountAndSecurity,
          ...(initialRole.permissions?.accountAndSecurity || {})
        },
        paymentManagement: {
          ...INITIAL_PERMISSIONS.paymentManagement,
          ...(initialRole.permissions?.paymentManagement || {})
        },
        communicationAndSupport: {
          ...INITIAL_PERMISSIONS.communicationAndSupport,
          ...(initialRole.permissions?.communicationAndSupport || {})
        }
      }));
    } else {
      // Reset to initial state when no initialRole is provided
      setRoleName('');
      setPermissions(INITIAL_PERMISSIONS);
    }
  }, [initialRole, isOpen]);

  const handleRoleChange = (category, permission) => {
    setPermissions(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [permission]: !prev[category][permission]
      }
    }));
  };

  const validateForm = () => {
    const newErrors = {};
    if (!roleName.trim()) {
      newErrors.roleName = 'Role name is required';
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) return;

    const roleData = {
      name: roleName,
      permissions
    };

    onSave?.(roleData);
    handleClose();
  };

  const handleClose = () => {
    setRoleName('');
    setPermissions(INITIAL_PERMISSIONS);
    setErrors({});
    onClose();
  };

  const renderPermissionSection = (title, category, permissions) => (
    <div>
      <h3 className="text-sm text-gray-500 font-medium mb-2">{title}</h3>
      <div className="space-y-2">
        {Object.entries(permissions).map(([key, value]) => (
          <div key={key} className="flex items-center capitalize">
            <CustomCheckbox
              id={`${category}-${key}`}
              name={key}
              label={key.split(/(?=[A-Z])/).join(' ')}
              labelClassName="text-sm !font-semibold"
              checked={value}
              onChange={() => handleRoleChange(category, key)}
            />
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={handleClose}
      title={location ? 'Edit Role' : 'Create a New Role'}
      size="md"
    >
      <div className="space-y-4 p-4 max-h-[calc(100vh-200px)] overflow-y-auto">
        <p className="text-sm text-gray-500 mb-4">
          Assign a role name and choose specific permissions to control access and actions for your team members.
        </p>
        <InputField
          id="name"
          type="text"
          label="Role Name"
          placeholder="Enter role name"
          marginBottom="mb-4"
          name="name"
          value={roleName}
          onChange={(e) => setRoleName(e.target.value)}
          error={errors.roleName}
        />

        <div className="mt-4 grid grid-cols-2 gap-4 border-t border-border-color pt-4">
          {renderPermissionSection('Product Management', 'productManagement', permissions.productManagement)}
          {renderPermissionSection('Order Management', 'orderManagement', permissions.orderManagement)}
        </div>

        <div className="mt-4 grid grid-cols-2 gap-4 border-t border-border-color pt-4">
          {renderPermissionSection('Reports & Analytics', 'reportsAndAnalytics', permissions.reportsAndAnalytics)}
          {renderPermissionSection('Inventory Management', 'inventoryManagement', permissions.inventoryManagement)}
        </div>

        <div className="mt-4 grid grid-cols-2 gap-4 border-t border-border-color pt-4">
          {renderPermissionSection('Account & Security', 'accountAndSecurity', permissions.accountAndSecurity)}
          {renderPermissionSection('Payment Management', 'paymentManagement', permissions.paymentManagement)}
        </div>
        <div className="mt-4 grid grid-cols-2 gap-4 border-t border-border-color pt-4">
          {renderPermissionSection('Communication & Support', 'communicationAndSupport', permissions.communicationAndSupport)}
        </div>
      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={handleClose}
        >
          Cancel
        </button>
        <button 
          type="button" 
          className="btn btn-primary"
          onClick={handleSave}
        >
          {location ? 'Update' : 'Save'}
        </button>
      </div>
    </BaseModal>
  );
};

export default CreateNewRoleModal;
