'use client';

import React, { useState } from 'react';
import BaseModal from './BaseModal';
import Input<PERSON>ield from '../Inputs/InputField';

const ChangePasswordModal = ({ isOpen, onClose }) => {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState(['', '', '', '', '', '']);
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  const handleVerificationCodeChange = (index, value) => {
    if (value.length <= 1) {
      const newCode = [...verificationCode];
      newCode[index] = value;
      setVerificationCode(newCode);

      // Auto-focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`code-${index + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  const handleContinue = () => {
    if (step === 1) {
      setStep(2);
    } else if (step === 2) {
      setStep(3);
    } else {
      onClose();
    }
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Change Password"
      size="sm"
    >
      <div className="p-4">
        {step === 1 && (
          <>
            <p className="text-sm text-gray-500 mb-4">
              Kindly enter your email to change the password, we will send you a verification code on your registered email address.
            </p>
            <InputField
              id="email"
              name="email"
              type="email"
              label="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
            />
          </>
        )}

        {step === 2 && (
          <>
            <p className="text-sm text-gray-500 mb-1">
              Enter verification code
            </p>
            <div className="flex gap-2 mb-4">
              {verificationCode.map((code, index) => (
                <input
                  key={index}
                  id={`code-${index}`}
                  type="text"
                  maxLength={1}
                  className="form-control text-center !font-semibold"
                  value={code}
                  onChange={(e) => handleVerificationCodeChange(index, e.target.value)}
                />
              ))}
            </div>
          </>
        )}

        {step === 3 && (
          <>
            <p className="text-sm text-gray-500 mb-4">
              Set a new password to keep your account safe and secure.
            </p>
            <div className="space-y-4">
              <InputField
                id="password"
                name="password"
                type="password"
                label="New Password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
              />
              <InputField
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                marginBottom='mb-0'
                label="Confirm New Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
          </>
        )}

      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button
          className="btn btn-primary"
          onClick={handleContinue}
        >
          {step === 3 ? 'Change password' : 'Continue'}
        </button>
      </div>
    </BaseModal>
  );
};

export default ChangePasswordModal;