import Image from 'next/image';
import React, { useState } from 'react';
import HappyEmoji from '@/public/images/happy-emoji.svg';
import NeutralEmoji from '@/public/images/neutral-emoji.svg';
import SadEmoji from '@/public/images/sad-emoji.svg';
import { useDispatch } from 'react-redux';
import { vendorFeedbackAsync } from '@/store/api/onboardingApi';
import { setCookie } from '@/utils/function';
import { cookiesKey } from '@/utils/cookiesStorageKeys';

const RatingOnboardingModal = (props) => {
  const { type, userType, setIsRatingModalOpen, setIsThankYouModalOpen } =
    props;

  const dispatch = useDispatch();

  const [selectedMood, setSelectedMood] = useState('neutral');
  const [feedbackComment, setFeedbackComment] = useState('');
  const [isCommentError, setIscommentError] = useState(false);

  const moods = [
    { id: 'sad', emoji: SadEmoji, label: 'Sad' },
    { id: 'neutral', emoji: NeutralEmoji, label: 'Neutral' },
    { id: 'happy', emoji: HappyEmoji, label: 'Happy' },
  ];

  const handleMoodChange = (moodId) => {
    setSelectedMood(moodId);
    if (moodId !== 'sad') setIscommentError(false);
  };

  const handleChangeComment = (event) => {
    setFeedbackComment(event.target.value);
    setIscommentError(false);
  };

  const handleSubmitRating = async () => {
    if (selectedMood === 'sad' && !feedbackComment) {
      setIscommentError(true);
    } else {
      const feedbackPayload = {
        rating: selectedMood,
        feedback: feedbackComment,
        type: type,
        user_type: userType,
      };
      const response = await dispatch(vendorFeedbackAsync(feedbackPayload));
      if (response?.payload?.ok) {
        setIsRatingModalOpen(false);
        setCookie(cookiesKey.feedbackVendor, btoa(true));
        // setIsThankYouModalOpen(true);
      }
    }
  };

  return (
    <>
      <div className="mt-8 p-6">
        <div className="flex flex-col text-center gap-2 max-w-[450px] mx-auto mb-8">
          <h2 className="text-xl font-bold">
            How was your onboarding experience?
          </h2>
          <p className="text-gray-500 text-sm">
            Your feedback helps us improve and create a better experience for
            you. Let us know how we did!
          </p>
        </div>
        <div
          className="flex justify-center gap-[76px] mb-8 py-[50px] bg-no-repeat bg-center bg-cover"
          style={{ backgroundImage: 'url(/images/review-bg-lines.svg)' }}
        >
          {moods.map((mood) => (
            <label
              key={mood.id}
              className={`
                    relative cursor-pointer 
                    transition-all duration-200 ease-in-out
                    ${
                      selectedMood === mood.id
                        ? 'after:content-[""] after:absolute after:-top-4.5 after:-left-4.5 after:w-28 after:h-28 after:rounded-full after:transition-all after:duration-200 after:ease-in-out scale-110 bg-custom-gradient'
                        : 'filter grayscale hover:filter-none hover:scale-105'
                    }
                  `}
            >
              <input
                type="radio"
                name="mood"
                value={mood.id}
                checked={selectedMood === mood.id}
                onChange={() => handleMoodChange(mood.id)}
                className="absolute opacity-0"
              />
              <Image
                src={mood.emoji}
                width={75}
                height={75}
                alt="emoji"
                className="relative z-[2]"
              />
              {/* <span 
                    role="img" 
                    aria-label={mood.label}
                    className="relative z-[1] text-7xl"
                  >
                    {mood.emoji}
                  </span> */}
            </label>
          ))}
        </div>

        <textarea
          placeholder="Add comment"
          rows="4"
          className="w-full py-3 px-4 border border-border-color rounded-lg placeholder:text-dark-500/40 resize-none focus:outline-none focus:border-dark-500"
          onChange={(event) => {
            handleChangeComment(event);
          }}
        />
        {isCommentError && (
          <div className="text-danger-500 text-sm mt-1">
            Comment is required
          </div>
        )}

        <button
          type="button"
          onClick={() => {
            handleSubmitRating();
          }}
          className="btn w-full mt-6"
        >
          Submit
        </button>
      </div>
    </>
  );
};

export default RatingOnboardingModal;
