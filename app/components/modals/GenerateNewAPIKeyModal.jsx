import React, { useState } from 'react';
import BaseModal from './BaseModal';
import InputField from '../Inputs/InputField';

const GenerateNewAPIKeyModal = ({ isOpen, onClose }) => {
  const [apiKey, setApiKey] = useState('');
  const [isGenerated, setIsGenerated] = useState(false);
  const [copyLabel, setCopyLabel] = useState('Copy');
  
  const generateNewKey = () => {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random()
      .toString(36)
      .substring(2, 5)
      .toUpperCase();
    const newAPIKey = `API-${timestamp}-${random}`;
    setApiKey(newAPIKey);
    setIsGenerated(true);
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey);
    setCopyLabel('Copied!');
    setTimeout(() => {
      setCopyLabel('Copy');
    }, 2000);
  };

  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title="Generate New API Key"
      size="md"
    >
      <div className="space-y-4 p-4">
        <p className="text-sm text-gray-500 mb-4">Click below to generate a secure API key for system integration. Keep this key safe—it's shown only once.</p>
        {!isGenerated ? (
          <InputField
            id="apiKey"
            type="text"
            label={null}
            placeholder=""
            marginBottom="mb-0"
            name="apiKey"
            value={apiKey}
            generateLabel="Generate code"
            onGenerateValue={generateNewKey}
          />
        ) : (
          <div className="relative">
            <input
              type="text"
              className="form-control pr-24"
              value={apiKey}
              readOnly
            />
            <button
              onClick={handleCopy}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-sm font-semibold cursor-pointer text-primary-500 hover:text-primary-600 transition-all duration-300 ease-in-out"
            >
              {copyLabel}
            </button>
          </div>
        )}
      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button 
          type="button" 
          className="btn" 
          onClick={generateNewKey}
        >
          Re-generate key
        </button>
      </div>
    </BaseModal>
  );
};

export default GenerateNewAPIKeyModal;
