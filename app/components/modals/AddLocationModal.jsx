import React from 'react';
import BaseModal from './BaseModal';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';

const AddLocationModal = ({ isOpen, onClose, location = null }) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={location ? 'Edit Location' : 'Add Location'}
      size="md"
    >
      <div className="space-y-4 p-4">
        <InputField
          label="Label"
          placeholder="Enter location label"
          name="label"
          defaultValue={location?.label}
        />

        <InputField
          label="Area or Place"
          placeholder="Enter area or place"
          name="area"
          defaultValue={location?.location}
        />

        <div className="flex flex-col">
          <span className="form-label">Status</span>
          <SelectField
            name="status"
            className="single-select"
            placeholder="Select status"
            defaultValue={location?.status}
            options={[
              { value: 'Active', label: 'Active' },
              { value: 'Inactive', label: 'Inactive' },
            ]}
          />
        </div>
      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Cancel
        </button>
        <button type="button" className="btn" onClick={onClose}>
          Save
        </button>
      </div>
    </BaseModal>
  );
};

export default AddLocationModal;
