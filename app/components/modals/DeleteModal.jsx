import React from 'react';
import BaseModal from './BaseModal';

const DeleteModal = ({ 
  isOpen, 
  onClose, 
  onConfirm,
  title = "Delete Item",
  message = "Are you sure you want to delete this item?",
  confirmButtonText = "Delete",
  cancelButtonText = "Cancel",
  confirmButtonClass = "btn btn-danger",
  location = null 
}) => {
  return (
    <BaseModal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      size="sm"
    >
      <div className="space-y-4 p-4">
        <p className="text-sm text-gray-500 mb-4">{message}</p>
      </div>
      <div className="flex justify-end p-4 gap-2.5 border-t border-border-color pt-4">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          {cancelButtonText}
        </button>
        <button 
          type="button" 
          className={confirmButtonClass}
          onClick={() => {
            if (onConfirm) {
              onConfirm();
            }
            onClose();
          }}
        >
          {confirmButtonText}
        </button>
      </div>
    </BaseModal>
  );
};

export default DeleteModal;
