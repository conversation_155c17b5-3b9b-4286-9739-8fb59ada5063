'use client';

import { LOGIN_2FA_SCHEMA } from '@/utils/schema';
import { Form, Formik } from 'formik';
import React, { useRef, useState } from 'react';
import OTPInput from '../Inputs/OTPInput';
import Image from 'next/image';
import { loginUserAsync, verify2FAAsync } from '@/store/api/authApi';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { setCookie } from '@/utils/function';
import { BUSINESS_DETAILS, DASHBOARD } from '@/routes/urls';
import { cookiesKey } from '@/utils/cookiesStorageKeys';
import { onboardingStatus } from '@/utils/constant';

export default function GoogleTwoFactor({ qrCodeUrl, user }) {
  const length = 6;
  const [otp, setOtp] = useState(Array(length).fill(''));
  const router = useRouter();
  const dispatch = useDispatch();

  const inputRefs = useRef([]);
  const handleChange = (index, value, setFieldValue) => {
    if (!/^[0-9]$/.test(value) && value !== '') return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setFieldValue('code', newOtp.join(''));
    if (value && index < otp.length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };
  return (
    <div>
      <div className="flex flex-col items-center gap-5 mb-6">
        {qrCodeUrl && (
          <Image
            src={qrCodeUrl}
            alt="QR Code"
            className="border border-border-color rounded-lg p-2"
            width={175}
            height={175}
          />
        )}
        <span className="text-sm font-medium text-gray-400">
          Scan the QR code and enter the 6-digit code from{' '}
          <span className="font-semibold text-dark-500">
            Google Authenticator
          </span>
          .
        </span>
      </div>
      <Formik
        initialValues={{
          code: '',
        }}
        validationSchema={LOGIN_2FA_SCHEMA}
        onSubmit={async (values) => {
          const { payload } = await dispatch(
            verify2FAAsync({
              email: user?.businessEmailId,
              code: values.code,
              password: user?.password,
            })
          );
          if (payload?.data?.status) {
            const { payload } = await dispatch(
              loginUserAsync({
                email: user?.businessEmailId,
                password: user?.password,
              })
            );
            setCookie(
              cookiesKey.onboardingStatus,
              btoa(payload.data.user.onboarding_status)
            );
            setCookie(
              cookiesKey.feedbackVendor,
              btoa(payload.data.user.feedback_status ? true : '')
            );
            setCookie(cookiesKey.tourStatus, true);
            if (
              payload.data.user.onboarding_status &&
              payload.data.user.onboarding_status !== onboardingStatus.DRAFT
            ) {
              router.push(DASHBOARD);
            } else {
              router.push(BUSINESS_DETAILS);
            }
          } else {
            return;
          }
        }}
      >
        {({ handleSubmit, setFieldValue, errors, touched }) => (
          <Form action="/search" onSubmit={handleSubmit}>
            {/* <label>Enter the 6 digit code from Google Authenticator *</label> */}
            <OTPInput
              length={6}
              handleChange={(index, value) =>
                handleChange(index, value, setFieldValue)
              }
              otp={otp}
              inputRefs={inputRefs}
              error={errors.code}
              touched={touched.code}
            />

            <button type="submit" className="btn w-full mt-6">
              Verify code
            </button>
          </Form>
        )}
      </Formik>
    </div>
  );
}
