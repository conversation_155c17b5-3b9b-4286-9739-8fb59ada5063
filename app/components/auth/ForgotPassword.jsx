'use client';

import Form from 'next/form';
import Link from 'next/link';
import Image from 'next/image';
import InputField from '../Inputs/InputField';
import { useDispatch } from 'react-redux';
import { forgetPasswordAsync } from '@/store/api/authApi';
import { Formik } from 'formik';
import { FORGET_PASSWORD_SCHEMA } from '@/utils/schema';
import { useRouter } from 'next/navigation';
import { LOGIN } from '@/routes/urls';
import ReCAPTCHA from 'react-google-recaptcha';
import { GOOGLE_RECAPTHA_API_KEY } from '../config';
import { useRef } from 'react';

const footerLinks = [
  { href: '/about-us', label: 'About Us' },
  { href: '/contact-us', label: 'Contact Us' },
  { href: '/faq', label: 'FAQs' },
  { href: '/terms-and-conditions', label: 'Terms & Conditions' },
  { href: '/privacy-policy', label: 'Privacy Policy' },
];

function ForgotPassword({ handleOtp }) {
  const dispatch = useDispatch();
  const router = useRouter();
  const recaptchaRef = useRef();

  return (
    <div className="flex items-stretch justify-between h-full min-h-screen">
      <div className="relative flex-[0_0_calc(100%_-_50%)] p-12 hidden lg:flex items-center justify-center min-h-screen">
        <div className="relative w-full max-w-[600px] aspect-square">
          <Image
            src="/images/welcome-img1.svg"
            alt="welcome image"
            fill
            className="object-contain"
            priority
          />
        </div>
        <div className="absolute top-0 left-0 -z-10 w-full h-full">
          <svg
            className="w-full h-full"
            viewBox="0 0 1920 1080"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g opacity="0.2" filter="url(#filter0_f)">
              <path
                fillRule="evenodd"
                d="M1461.49 1439.38C1242.66 1439.38 1063.61 1260.34 1063.61 1041.5V38.5C1063.61 -180.34 1242.65 -359.38 1461.49 -359.38C1680.33 -359.38 1859.38 -180.32 1859.38 38.5V1041.5C1859.38 1260.33 1680.31 1439.38 1461.49 1439.38Z"
                fill="#C31A5A"
              />
              <path
                fillRule="evenodd"
                d="M60.62 38.51C60.62 -180.31 239.66 -359.38 458.5 -359.38H1461.49C1680.33 -359.38 1859.38 -180.32 1859.38 38.5C1859.38 257.34 1680.33 436.4 1461.5 436.4H458.5C239.68 436.4 60.62 257.33 60.62 38.51Z"
                fill="#006CB5"
              />
              <path
                fillRule="evenodd"
                d="M1461.49-359.38C1680.33-359.38 1859.38-180.32 1859.38 38.5C1859.38 257.34 1680.33 436.4 1461.5 436.4H1063.61V38.5C1063.61-180.34 1242.65-359.38 1461.49-359.38Z"
                fill="#322B6A"
              />
            </g>
            <defs>
              <filter
                id="filter0_f"
                x="-877.7"
                y="-1297.7"
                width="3675.4"
                height="3675.41"
                filterUnits="userSpaceOnUse"
                colorInterpolationFilters="sRGB"
              >
                <feGaussianBlur stdDeviation="469.16" />
              </filter>
            </defs>
          </svg>
        </div>
      </div>

      <div className="flex flex-col justify-center min-h-screen flex-1 p-6">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <Image
            src="/images/logo-slim.svg"
            alt="Hubsups Logo"
            width={180}
            height={40}
            className="object-contain"
            priority
          />
        </div>
        <div className="bg-white rounded-2xl py-6 px-8 border border-border-color shadow-[0px_6px_12px_0px_#1C27310D] w-full max-w-[500px] mx-auto">
          {/* Header Text */}
          <div className="text-center mb-8">
            <h1 className="text-xl font-semibold mb-1">
              Forgot Your Password?
            </h1>
            <p className="text-sm text-gray-500">
              Reset your password securely using your registered email address.
            </p>
          </div>

          {/* Auth Form */}
          <Formik
            initialValues={{
              email: '',
            }}
            validationSchema={FORGET_PASSWORD_SCHEMA.EMAIL}
            validateOnChange={true}
            validateOnBlur={true}
            onSubmit={async (values) => {
              const recaptcha_token = await recaptchaRef.current.executeAsync();
              recaptchaRef.current.reset();
              const { payload } = await dispatch(
                forgetPasswordAsync({ ...values, recaptcha_token })
              );
              if (payload.ok) {
                handleOtp();
              }
            }}
          >
            {(formik) => (
              <Form onSubmit={formik.handleSubmit}>
                <InputField
                  id="email"
                  name="email"
                  type="text"
                  label="Email Address"
                  required={false}
                  formik={formik}
                />

                <button
                  type="submit"
                  className="btn w-full mt-1"
                >
                  Reset Password
                </button>
                <ReCAPTCHA
                  sitekey={GOOGLE_RECAPTHA_API_KEY}
                  size="invisible"
                  ref={recaptchaRef}
                />

                <div className="flex items-center justify-center gap-1 text-sm mt-4">
                  Back to
                  <Link
                    href={LOGIN}
                    className="font-semibold hover:text-primary-500 transition-base"
                  >
                    Login
                  </Link>
                </div>
              </Form>
            )}
          </Formik>
        </div>
        {/* Footer links */}
        {/* <ul className="flex justify-center gap-6 mt-8 text-xs text-gray-500">
          {footerLinks.map(({ href, label }, index) => (
            <li
              key={href}
              className={
                index !== footerLinks.length - 1
                  ? 'relative after:content-[""] after:absolute after:right-0 after:top-1/2 after:h-3 after:w-[1px] after:bg-gray-100 after:-translate-y-1/2 pr-2 lg:pr-2'
                  : ''
              }
            >
              <Link
                href={href}
                title={label}
                className="text-xs font-medium hover:text-primary-500 transition-base"
              >
                {label}
              </Link>
            </li>
          ))}
        </ul> */}
      </div>
    </div>
  );
}

export default ForgotPassword;
