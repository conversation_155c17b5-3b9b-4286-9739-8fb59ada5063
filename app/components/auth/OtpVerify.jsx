// 'use client';
import React, { useRef, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import OTPInput from '../Inputs/OTPInput';
import {
  BUSINESS_DETAILS,
  FORGET_PASSWORD,
  HOME,
  RESET_PASSWORD,
} from '@/routes/urls';
import { useDispatch, useSelector } from 'react-redux';
import {
  getOtpAsync,
  sendOtpAsync,
  verifyForgotPasswordOtp,
} from '@/store/api/authApi';
import { useRouter } from 'next/navigation';
import { resetUserDetailsAction } from '@/store/actions/authActions';
import { usePathname } from 'next/navigation';
import { Form, Formik } from 'formik';
import { OTP_VERIFICATION_SCHEMA } from '@/utils/schema';
import ReCAPTCHA from 'react-google-recaptcha';
import { GOOGLE_RECAPTHA_API_KEY } from '../config';

const footerLinks = [
  { href: '/about-us', label: 'About Us' },
  { href: '/contact-us', label: 'Contact Us' },
  { href: '/faq', label: 'FAQs' },
  { href: '/terms-and-conditions', label: 'Terms & Conditions' },
  { href: '/privacy-policy', label: 'Privacy Policy' },
];

export default function OtpVerify({ otpType, handleOtp }) {
  const recaptchaRef = useRef();
  const length = 6;
  const [otp, setOtp] = useState(Array(length).fill(''));
  const inputRefs = useRef([]);
  const pathname = usePathname();

  const dispatch = useDispatch();
  const router = useRouter();
  const { userDetails, forgetPasswordOtpSent } = useSelector(
    (state) => state.auth
  );

  const handleChange = (index, value, setFieldValue) => {
    if (/^[0-9]?$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);
      setFieldValue('otp', newOtp.join(''));

      if (value && index < otp.length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  return (
    <>
      <div className="flex items-stretch justify-between h-full min-h-screen">
        <div className="relative flex-[0_0_calc(100%_-_50%)] p-12 hidden lg:flex items-center justify-center min-h-screen">
          <div className="relative w-full max-w-[600px] aspect-square">
            <Image
              src="/images/welcome-img1.svg"
              alt="welcome image"
              fill
              className="object-contain"
              priority
            />
          </div>
          <div className="absolute top-0 left-0 -z-10 w-full h-full">
            <svg
              className="w-full h-full"
              viewBox="0 0 1920 1080"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g opacity="0.2" filter="url(#filter0_f)">
                <path
                  fillRule="evenodd"
                  d="M1461.49 1439.38C1242.66 1439.38 1063.61 1260.34 1063.61 1041.5V38.5C1063.61 -180.34 1242.65 -359.38 1461.49 -359.38C1680.33 -359.38 1859.38 -180.32 1859.38 38.5V1041.5C1859.38 1260.33 1680.31 1439.38 1461.49 1439.38Z"
                  fill="#C31A5A"
                />
                <path
                  fillRule="evenodd"
                  d="M60.62 38.51C60.62 -180.31 239.66 -359.38 458.5 -359.38H1461.49C1680.33 -359.38 1859.38 -180.32 1859.38 38.5C1859.38 257.34 1680.33 436.4 1461.5 436.4H458.5C239.68 436.4 60.62 257.33 60.62 38.51Z"
                  fill="#006CB5"
                />
                <path
                  fillRule="evenodd"
                  d="M1461.49-359.38C1680.33-359.38 1859.38-180.32 1859.38 38.5C1859.38 257.34 1680.33 436.4 1461.5 436.4H1063.61V38.5C1063.61-180.34 1242.65-359.38 1461.49-359.38Z"
                  fill="#322B6A"
                />
              </g>
              <defs>
                <filter
                  id="filter0_f"
                  x="-877.7"
                  y="-1297.7"
                  width="3675.4"
                  height="3675.41"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feGaussianBlur stdDeviation="469.16" />
                </filter>
              </defs>
            </svg>
          </div>
        </div>

        <div className="flex flex-col justify-center min-h-screen flex-1 p-6">
          {/* Logo */}
          <div className="flex justify-center mb-8">
            <Image
              src="/images/logo-slim.svg"
              alt="Hubsups Logo"
              width={180}
              height={40}
              className="object-contain"
              priority
            />
          </div>
          <div className="bg-white rounded-2xl py-6 px-8 border border-border-color shadow-[0px_6px_12px_0px_#1C27310D] w-full max-w-[500px] mx-auto">
            {/* Header Text */}
            <div className="text-center mb-9">
              <h1 className="text-xl font-semibold mb-1">
                Verify Email Address
              </h1>
              <p className="text-sm text-gray-500">
                Enter the verification code that we sent you on{' '}
                <Link
                  href={otpType === 'forget-password-otp' ? '#' : HOME}
                  className="font-bold text-primary-500 hover:underline transition-base"
                  onClick={(e) => {
                    if (otpType === 'forget-password-otp') {
                      e.preventDefault();
                      handleOtp();
                    } else {
                      dispatch(resetUserDetailsAction());
                    }
                  }}
                >
                  Change
                </Link>
              </p>
            </div>

            {/* OTP Input */}
            <Formik
              initialValues={{ otp: '' }}
              validationSchema={OTP_VERIFICATION_SCHEMA}
              onSubmit={async (values) => {
                const { otp: otpValue } = values;
                if (otpType == 'forget-password-otp') {
                  const recaptcha_token =
                    await recaptchaRef.current.executeAsync();
                  recaptchaRef.current.reset();
                  let { payload } = await dispatch(
                    verifyForgotPasswordOtp({
                      otp: Number(otpValue),
                      email: userDetails.email,
                      recaptcha_token,
                    })
                  );
                  if (payload.ok) {
                    router.push(RESET_PASSWORD);
                  }
                } else {
                  const recaptcha_token =
                    await recaptchaRef.current.executeAsync();
                  recaptchaRef.current.reset();
                  let { payload } = await dispatch(
                    sendOtpAsync({
                      otp: Number(otpValue),
                      recaptcha_token,
                      ...userDetails,
                    })
                  );
                  if (payload?.ok) {
                    router.push(BUSINESS_DETAILS);
                  } // redirect to business details page
                }
              }}
            >
              {({ handleSubmit, setFieldValue, errors, touched }) => (
                <Form onSubmit={handleSubmit}>
                  {/* OTP Verify filed */}
                  <OTPInput
                    length={6}
                    handleChange={(index, value) =>
                      handleChange(index, value, setFieldValue)
                    }
                    otp={otp}
                    inputRefs={inputRefs}
                    error={errors.otp}
                    touched={touched.otp}
                  />

                  <button
                    type="submit"
                    className="btn w-full mt-9"
                  >
                    Verify code
                  </button>
                  <div className="flex justify-center gap-1 mt-4 text-sm text-gray-400">
                    Didn’t received the code yet?
                    <button
                      type="button"
                      className="bg-transparent border-0 cursor-pointer font-bold text-dark-500 hover:text-primary-500 transition-base"
                      onClick={() =>
                        dispatch(getOtpAsync({ email: userDetails?.email }))
                      }
                    >
                      Resend
                    </button>
                  </div>
                  <ReCAPTCHA
                    sitekey={GOOGLE_RECAPTHA_API_KEY}
                    size="invisible"
                    ref={recaptchaRef}
                  />
                </Form>
              )}
            </Formik>
          </div>
          {/* Footer links */}
          {/* <ul className="flex justify-center gap-6 mt-8 text-xs text-gray-500">
            {footerLinks.map(({ href, label }, index) => (
              <li
                key={href}
                className={
                  index !== footerLinks.length - 1
                    ? 'relative after:content-[""] after:absolute after:right-0 after:top-1/2 after:h-3 after:w-[1px] after:bg-gray-100 after:-translate-y-1/2 pr-2 lg:pr-2'
                    : ''
                }
              >
                <Link
                  href={href}
                  title={label}
                  className="text-xs font-medium hover:text-primary-500 transition-base"
                >
                  {label}
                </Link>
              </li>
            ))}
          </ul> */}
        </div>
      </div>
    </>
  );
}
