import React, { useRef, useEffect } from 'react';

const BankAccountItem = ({ 
  account, 
  isDefault, 
  onSetDefault, 
  onRemove,
  isMenuOpen,
  onToggleMenu 
}) => {
  const menuRef = useRef();

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onToggleMenu(null);
      }
    };

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMenuOpen, onToggleMenu]);

  return (
    <div className="flex justify-between items-center p-4 not-last:border-b border-border-color ">
      <div className="flex items-start justify-between gap-2">
        <div className="w-10 h-10 flex justify-center items-center rounded-xl bg-gray-500/5 text-xl">
          <span className="icon icon-bank" />
        </div>
        <div className="flex items-start justify-between flex-col gap-0.5">
          <span className="text-sm font-semibold text-dark-500">
            {account.name}
          </span>
          <ul className="flex items-center gap-1 text-xs text-gray-300">
            <li className="relative after:content-[''] after:absolute after:right-0 after:top-1/2 after:h-3 after:w-[1px] after:bg-gray-100 after:-translate-y-1/2 pr-1">
              **{account.lastFour}
            </li>
            <li>{account.holderName}</li>
          </ul>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {isDefault && (
          <span className="px-1.5 py-1 rounded-lg text-xs font-semibold bg-success-500/10 text-success-600">
            Default
          </span>
        )}
        <div ref={menuRef} className="relative">
          <button
            onClick={() => onToggleMenu(account.id)}
            className="w-8 h-8 flex justify-center items-center hover:bg-gray-500/10 active:bg-gray-500/10 focus:bg-gray-500/10 text-xl rounded-lg transition-base"
          >
            <span className="icon icon-dots-horizontal" />
          </button>
          <div
            className={`absolute top-full right-0 mt-1.5 transform transition-all duration-200 z-10 ease-in-out ${
              isMenuOpen
                ? 'opacity-100 translate-y-0 pointer-events-auto'
                : 'opacity-0 -translate-y-2 pointer-events-none'
            }`}
          >
            <ul className="flex flex-wrap gap-1 p-2 rounded-lg bg-white shadow-custom min-w-[120px]">
              {!isDefault && (
                <li className="w-full">
                  <button
                    onClick={() => {
                      onSetDefault(account.id);
                      onToggleMenu(null);
                    }}
                    className="inline-flex items-center gap-2 py-1.5 px-2 text-left rounded-lg w-full text-sm whitespace-nowrap hover:bg-primary-500/10 hover:text-primary-500 transition-base"
                  >
                    Set as default
                  </button>
                </li>
              )}
              <li className="w-full">
                <button
                  onClick={() => {
                    onRemove(account.id);
                    onToggleMenu(null);
                  }}
                  className="inline-flex items-center gap-2 py-1.5 px-2 text-left rounded-lg w-full text-sm whitespace-nowrap hover:bg-primary-500/10 hover:text-primary-500 transition-base"
                >
                  Remove
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankAccountItem;