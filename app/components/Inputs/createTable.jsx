import React, { useEffect, useState } from 'react';
import CreatableSelect from 'react-select/creatable';

const components = {
  DropdownIndicator: null,
};

const createOption = (label) => ({
  label,
  value: label,
});

function CreatableMultiSelect(props) {
  const [mounted, setMounted] = useState(false);
  const [inputValue, setInputValue] = useState('');

  useEffect(() => {
    setMounted(true);
  }, []);

  const {
    classNames,
    placeholder = 'Type and press enter...',
    isClearable = true,
    isDisabled = false,
    formik,
    name,
    onChange,
    ...otherProps
  } = props;

  const currentValue = formik?.values[name] || [];

  const handleKeyDown = (event) => {
    if (!inputValue) return;

    switch (event.key) {
      case 'Enter':
      case 'Tab': {
        const newOption = createOption(inputValue);
        const updatedValue = [...currentValue, newOption];

        formik.setFieldTouched(name, true);
        formik.setFieldValue(name, updatedValue);
        setInputValue('');
        event.preventDefault();
        break;
      }
      default:
        break;
    }
  };

  const handleChange = (newValue) => {
    formik.setFieldTouched(name, true);
    formik.setFieldValue(name, newValue);

    if (onChange) {
      onChange(newValue);
    }
  };

  return mounted ? (
    <>
      <CreatableSelect
        className={classNames}
        classNamePrefix="custom_select"
        components={components}
        inputValue={inputValue}
        isClearable={isClearable}
        isMulti
        isDisabled={isDisabled}
        menuIsOpen={false}
        onChange={handleChange}
        onInputChange={(val) => setInputValue(val)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        value={currentValue}
        name={name}
        {...otherProps}
      />
    </>
  ) : null;
}

export default CreatableMultiSelect;
