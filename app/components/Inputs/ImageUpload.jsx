'use client';

import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Tooltip } from 'react-tooltip';
import { useFormikContext } from 'formik';
import Image from 'next/image';
import CustomCheckbox from './CustomCheckbox';

const ImageUpload = ({
  name,
  setImages,
  defaultImageIndex,
  setDefaultImageIndex,
  maxFiles = 1,
  maxSize = 5000000,
  className = '',
  heading = '',
  showFileInfo = true,
  thumbnailInfo = false,
  showError,
  uploadText = 'Add image',
  tooltipContent = 'Upload an image that meets the specified requirements.',
  nameCoverImage,
}) => {
  const formik = useFormikContext()
  const getImageFromPath = (path) => {
    const data = formik?.values;
    if (typeof path !== 'string') {
      // console.error('Expected path to be a string, but got', typeof path);
      return null; // Return null or handle this case appropriately
    }
    
    const pathParts = path.split('.'); // Split the path string into parts
    
    let result = data; // Start with the data object


    // Traverse the object based on the parts of the path
    for (let part of pathParts) {
      if (result && result[part] !== undefined) {
        result = result[part];
      } else {
        return null; // Return null if path doesn't exist
      }
    }

    // Check if the result is an array and has images
    if (Array.isArray(result) && result.length > 0) {
      return result; // Return the preview of the first image
    }

    return null; // Return null if no image found
  };

  const imageUrl = getImageFromPath(name);

  const images = formik?.values[name] || imageUrl || [];
  const [previewImage, setPreviewImage] = useState(null);
  const [error, setError] = useState(null);
  // Remove this line as it's already declared in props
  // const [defaultImageIndex, setDefaultImageIndex] = useState(null);

  const handleDefaultChange = (index) => {
    setDefaultImageIndex(defaultImageIndex === index ? null : index);
    // Update the formik values to include the default image information
    const updatedImages = images.map((img, i) => ({
      ...img,
      isDefault: i === index && defaultImageIndex !== index
    }));
    formik?.setFieldValue(nameCoverImage, updatedImages);
  };

  const onDrop = (acceptedFiles, fileRejections) => {
    setError(null); // clear previous error
console.log(acceptedFiles,"$$$$$$$$$")
    const totalSelected = images.length + acceptedFiles.length;
    if (totalSelected > maxFiles) {
      setError(`You can upload a maximum of ${maxFiles} images.`);
      return;
    }
    if (fileRejections.length > 0) {
      const rejection = fileRejections[0];
      const { errors } = rejection;
      if (errors.some((err) => err.code === 'file-too-large')) {
        setError('One or more files exceed the 3MB limit.');
      } else if (errors.some((err) => err.code === 'file-invalid-type')) {
        setError('Only JPG, JPEG, and PNG files are allowed.');
      } else {
        setError('Some files were rejected. Please check their type and size.');
      }
      return;
    } else {
      setError(null);
    }

    // All good, add images
    const newImages = acceptedFiles.map((file) => ({
      file,
      preview: URL.createObjectURL(file),
    }));

    setImages((prev) => [...prev, ...newImages]);

    const updatedImages = [...images, ...newImages];
    if(formik){
      formik?.setFieldValue(name, updatedImages);
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png'],
    },
    maxFiles,
    maxSize,
  });

  return (
    <>
      <div className={`${className}`}>
        {heading && (
          <div className="flex items-center gap-1 mb-2">
            <label className="form-label inline-block !mb-0">{heading}</label>
            {tooltipContent && (
            <span
              data-tooltip-id={`file-upload-${heading}`}
              className="icon icon-info text-base text-gray-400 cursor-help"
            />
            )}
            <Tooltip
              id={`file-upload-${heading}`}
              place="top"
              className="!bg-white !opacity-100 !z-50 !text-black !shadow-lg !p-3 !rounded-xl"
            >
              <div className="flex justify-center items-center text-center max-w-[340px] text-dark-500 text-sm">
                <p>{tooltipContent}</p>
              </div>
            </Tooltip>
          </div>
        )}

        {images?.length !== maxFiles && (
          <div {...getRootProps()} className="border-2 border-dashed border-dark-500/10 bg-lighter-100 rounded-xl px-6 py-8 2xl:py-11 text-center cursor-pointer hover:border-dark-500 hover:bg-dark-500/5 transition-base">
            <input {...getInputProps()} className="hidden" />
            <div className="btn btn-outline-gray inline-block !font-medium">
              {maxFiles > 1 ? `${uploadText} (${images.length}/${maxFiles})` : uploadText}
            </div>
          </div>
        )}

        {showFileInfo && images?.length !== maxFiles && (
          <div className="flex justify-between items-center mt-2">
            <p className="text-xs text-dark-gray-200">
              Supported formats: .jpg, .jpeg, .png
            </p>
            <span className="text-xs text-dark-gray-200">
              Maximum file size: 5MB
            </span>
          </div>
        )}

        {images.length > 0 && (
          <div className="mt-4 grid grid-cols-4 lg:grid-cols-5 gap-2">
            {images.map((image, index) => (
              <div key={index} className="relative aspect-square group">
                <Image
                  src={image.preview}
                  alt={`Preview ${index + 1}`}
                  fill
                  className="object-cover rounded-lg"
                />
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg flex flex-col gap-2 items-center justify-center">
                  <div className="flex gap-2">
                    <button
                      onClick={(e) => {
                        e?.preventDefault();
                        setPreviewImage(image.preview)}}
                      className="bg-black/60 h-8 w-8 rounded-lg flex items-center justify-center text-white gap-2 px-3 py-1.5 backdrop-blur-sm hover:bg-white/30 transition-base cursor-pointer"
                    >
                      <span className="icon icon-eye text-sm" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        URL.revokeObjectURL(image.preview);
                        setImages(images.filter((_, i) => i !== index));
                        const updated = images.filter((_, i) => i !== index);
                        if (defaultImageIndex === index) {
                          setDefaultImageIndex(null);
                        }
                        formik.setFieldValue(name, updated?.length === 0 ? null : updated);
                        setError(null);
                      }}
                      className="bg-black/60 h-8 w-8 rounded-lg flex items-center justify-center text-white gap-2 px-3 py-1.5 backdrop-blur-sm hover:bg-white/30 transition-base cursor-pointer"
                    >
                      <span className="icon icon-trash text-xs" />
                    </button>
                  </div>
                </div>
                {(defaultImageIndex === null || defaultImageIndex === index) && (
                  <div className="absolute top-1.5 right-1.5">
                    <CustomCheckbox
                      id={`default-image-${index}`}
                      name={`default-image-${index}`}
                      label={null}
                      checked={defaultImageIndex === index}
                      onChange={() => handleDefaultChange(index)}
                      // className="bg-black/60 h-8 w-8 rounded-lg flex items-center justify-center text-white"
                      labelClassName="text-xs text-white"
                    />
                  </div>
                )}
              </div>
            ))}
            {thumbnailInfo && (
              <div className="inline-flex col-span-full items-center gap-1 text-xs text-gray-300 italic w-full mt-2"><span className="icon icon-info text-base" />Choose a Cover Image</div>
            )}
          </div>
        )}

        {/* Show Formik or local error */}
        {formik?.touched?.[name] && formik?.errors?.[name] ? (
  <p className="text-danger-500 text-xs mt-1">{formik.errors[name]}</p>
) : formik?.touched?.[nameCoverImage] && formik?.errors?.[nameCoverImage] ? (
  <p className="text-danger-500 text-xs mt-1">{formik.errors[nameCoverImage]}</p>
) : error ? (
  <p className="text-danger-500 text-xs mt-1">{error}</p>
) : null}

      </div>

      {showError ? (
        <p className="text-danger-500 text-xs mt-1">
          Please upload at least one product image.
        </p>
      ) : error ? (
        <p className="text-danger-500 text-xs mt-1">{error}</p>
      ) : null}

      {/* Image Preview Modal */}
      {previewImage && (
        <div
          className="fixed inset-0 z-[100] flex items-center justify-center fadeIn"
          onClick={() => setPreviewImage(null)}
        >
          <div className="fixed inset-0 bg-black/80 fadeIn" />
          <div
            className="relative max-w-xl w-full mx-4 bg-white p-5 rounded-2xl scaleUp"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              onClick={() => setPreviewImage(null)}
              className="absolute -top-10 right-0 text-white hover:text-danger-500 transition-colors duration-300 fadeIn cursor-pointer"
            >
              <span className="icon icon-x text-2xl" />
            </button>
            <div className="relative aspect-square w-full rounded-2xl overflow-hidden">
              <Image
                src={previewImage}
                alt="Preview"
                fill
                className="object-contain rounded-2xl fadeIn"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ImageUpload;
