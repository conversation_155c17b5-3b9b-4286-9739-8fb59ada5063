import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

const PhoneInputField = ({
  name,
  countryCodeName,
  formik,
  containerClass = '',
  outerFormik,
  placeholder = 'Enter your phone number',
}) => {
  const handleChange = (phoneNumber, country) => {
    const reducedPhone = phoneNumber.replace(country.dialCode, '');

    formik?.setFieldValue(name, reducedPhone);
    outerFormik?.setFieldValue('phoneNumberExists', reducedPhone.length > 0);
  };

  return (
    <div className={containerClass}>
      <PhoneInput
        country={'us'}
        onlyCountries={['us']}
        disableDropdown
        value={`${formik.values[countryCodeName]} + - +${formik.values[name]}`}
        countryCodeEditable={false}
        onChange={handleChange}
        placeholder={placeholder}
        inputClass="!w-full !pr-14"
        containerClass="z-20 phone-input"
      />
    </div>
  );
};

export default PhoneInputField;
