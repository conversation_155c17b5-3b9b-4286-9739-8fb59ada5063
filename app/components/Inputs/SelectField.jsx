import React, { useEffect, useState } from 'react';
import Select from 'react-select';

function SelectField(props) {
  const [mounted, setMounted] = useState(false);
  const [internalValue, setInternalValue] = useState(null);
  const [forceMenuClose, setForceMenuClose] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const {
    options = [],
    defaultValue,
    classNames,
    placeholder,
    isSearchable = false,
    isClearable = false,
    isMulti = false,
    isDisabled = false,
    onChange,
    components,
    formik,
    closeMenuOnSelect = true,
    menuPortalClassName = 'custom-menu-portal',
    name,
    defaultMenuIsOpen = false,
    onEmptyClick,
    useMenuPortal = true,
    value,
    ...otherProps
  } = props;

  const handleChange = (selectedOption) => {
    if (formik && name) {
      formik.setFieldTouched(name, true);
      formik.setFieldValue(
        name,
        isMulti ? selectedOption?.map((opt) => opt?.value) : selectedOption?.value
      );
    }

    if (onChange) {
      onChange(selectedOption);
    }

    if (!formik) {
      setInternalValue(selectedOption);
    }
  };

  const handleMenuOpen = () => {
    if (name === 'productAttributes' && options.length === 0) {
      setForceMenuClose(true);
      if (onEmptyClick) {
        onEmptyClick();
      }
    } else {
      setForceMenuClose(false);
    }
  };

  const getValue = () => {
    if (formik && name) {
      const formikValue = formik.values[name];
      if (isMulti) {
        return options.filter(option => formikValue?.includes(option.value));
      }
      return options.find(option => option.value === formikValue) || null;
    }

    if (value) {
      if (isMulti) {
        return options.filter(option => value.includes(option.value));
      }
      return options.find(option => option.value === value) || null;
    }
    return internalValue;
  };

  return mounted ? (
    <Select
      className={classNames}
      classNamePrefix="custom_select"
      isClearable={isClearable}
      isSearchable={isSearchable}
      isMulti={isMulti}
      menuPlacement="auto"
      closeMenuOnSelect={closeMenuOnSelect}
      defaultValue={
        defaultValue
          ? options.find((option) => option.value === defaultValue)
          : null
      }
      options={options}
      placeholder={placeholder}
      onChange={handleChange}
      onMenuOpen={handleMenuOpen}
      menuIsOpen={forceMenuClose ? false : undefined}
      menuPortalTarget={useMenuPortal ? (typeof window !== 'undefined' ? document.body : null) : null}
      isDisabled={isDisabled}
      components={components}
      name={name}
      hideSelectedOptions={false}
      defaultMenuIsOpen={defaultMenuIsOpen}
      value={getValue()}
      classNames={{
        menuPortal: () => menuPortalClassName,
      }}
      {...otherProps}
    />
  ) : null;
}

export default SelectField;