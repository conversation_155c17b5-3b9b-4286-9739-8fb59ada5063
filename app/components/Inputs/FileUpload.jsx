'use client';

import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Tooltip } from 'react-tooltip';

const FileUpload = ({
  label,
  accept = '.pdf',
  onChange,
  error,
  value,
  maxFileSizeMB = 5,
  tooltipContent = '',
  name,
  formik
}) => {
  const [preview, setPreview] = useState(null);
  const [isFileReplaced,SetisFileReplaced] =useState(false)

  const getAcceptedTypes = () => {
    const types = {};
    if (accept.includes('.pdf')) {
      types['application/pdf'] = ['.pdf'];
    }
    if (accept.includes('.csv')) {
      types['text/csv'] = ['.csv'];
    }
    return types;
  };

  const onDrop = useCallback(
    (acceptedFiles) => {
      SetisFileReplaced(false)
      const file = acceptedFiles?.[0];
      
      if (!file) return;

      if (file.size > maxFileSizeMB * 1024 * 1024) {
        alert(`File is too large. Maximum allowed size is ${maxFileSizeMB}MB.`);
        return;
      }

      // Create preview URL for PDF files only
      if (file.type === 'application/pdf') {
        const previewUrl = URL.createObjectURL(file);
        setPreview(previewUrl);
      formik.setFieldValue(name,file)      } else {
        setPreview(null);
      }

      if (onChange) {
        onChange(file);
      }   
    },
    [onChange, maxFileSizeMB]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: getAcceptedTypes(),
    multiple: false,
  });

  return (
    <div className="flex flex-col">
      {label && (
        <div className="flex items-center gap-1">
          <label className="form-label mb-0">{label}</label>
          {tooltipContent && (
            <>
              <span
                data-tooltip-id={`file-upload-${label}`}
                className="icon icon-info text-base text-gray-400 cursor-help"
              />

              <Tooltip
                id={`file-upload-${label}`}
                place="top"
                className="!bg-white !opacity-100 !z-50 !text-black !shadow-lg !p-3 !rounded-xl"
              >
              
                  <div className="flex justify-center items-center text-center max-w-[340px] text-dark-500 text-sm">
                    <p>{tooltipContent}</p>
                  </div>
              </Tooltip>
            </>
          )}
        </div>
      )}


      {value && !isFileReplaced ? (
        <div className="flex flex-col gap-3">
          <div className="flex items-center justify-between w-full p-4 bg-gray-500/5 border border-border-color rounded-xl">
            <div className="flex items-center gap-3">
              <span className="w-10 h-10 flex items-center justify-center rounded-xl bg-gray-500/5 text-dark-500 icon icon-paperclip text-xl" />
              <div className="flex flex-col">
                <span className="text-sm font-semibold text-dark-500">
                  {value.name}
                </span>
                <span className="text-xs text-dark-500/40">
                  Size: {Math.round(value.size / 1024)} KB
                </span>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {preview && (
                <a
                  href={preview}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="btn btn-outline-gray"
                >
                  View
                </a>
              )}
              <button
                onClick={() => {SetisFileReplaced(true)}}
                className="btn btn-outline-gray"
              >
                Replace file
              </button>
              <button
                onClick={() => {
                  formik.setFieldValue(name,null)
                  setPreview(null);
                }}
                className="w-8 h-8 flex items-center justify-center rounded-md hover:bg-danger-500/5 cursor-pointer transition-base"
              >
                <span className="icon icon-trash text-lg text-danger-500" />
              </button>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div
            {...getRootProps()}
            className={`flex flex-col items-center justify-center w-full px-5 lg:px-[30] py-5 lg:py-8 2xl:py-11 bg-lighter-100 border-2 border-dashed rounded-xl cursor-pointer transition-base
              ${isDragActive ? 'border-dark-500 bg-dark-500/5' : 'border-border-color hover:border-dark-500 hover:bg-dark-500/5'}
              ${(formik?.touched[name] &&
                formik?.errors[name]) ? 'border-danger-500' : ''}
            `}
          >
            <input {...getInputProps()}/>
            <div className="flex flex-col items-center gap-2">
              <div className="btn btn-outline-gray">
              Add File
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center mt-2">
            <p className="text-xs text-dark-gray-200">
              Supported formats: {accept.split(',').join(', ')}
            </p>
            <span className="text-xs text-dark-gray-200">
              Maximum file size: {maxFileSizeMB}MB
            </span>
          </div>
        </>
      )}

      {(formik?.touched[name] &&
                formik?.errors[name]) && <span className="text-danger-500 text-xs mt-1">{formik?.errors[name]}</span>}
    </div>
  );
};

export default FileUpload;
