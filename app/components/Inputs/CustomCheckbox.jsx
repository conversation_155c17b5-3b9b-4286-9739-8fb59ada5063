import React from 'react';

const CustomCheckbox = ({
  id,
  name,
  label,
  subText,
  checked,
  onChange,
  className = '',
  disabled = false,
  labelClassName = 'text-sm',
  subTextClassName = 'text-xs text-gray-300',
}) => {
  return (
    <div className="inline-flex items-center gap-2">
      <label
        className={`flex items-center cursor-pointer gap-2 ${className}`}
        htmlFor={id}
      >
        <div className="relative flex items-center justify-center">
          <input
            type="checkbox"
            id={id}
            disabled={disabled}
            name={name}
            checked={checked}
            onChange={onChange}
            className="peer h-4 w-4 cursor-pointer transition-base appearance-none rounded-sm border-2 border-border-color checked:bg-primary-500 checked:border-primary-500"
          />

          <span className="absolute text-white opacity-0 peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-3 w-3"
              viewBox="0 0 20 20"
              fill="currentColor"
              stroke="currentColor"
              strokeWidth="1"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </span>
        </div>

        {/* Label and Subtext */}
        <span className={`inline-block text-xs font-medium ${labelClassName}`}>{label}</span>
        {subText && <span className={subTextClassName}>{subText}</span>}
      </label>
    </div>
  );
};

export default CustomCheckbox;
