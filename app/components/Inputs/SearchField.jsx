'use client';
import React, { useEffect, useRef, useState } from 'react';
import InputField from '../Inputs/InputField';
import Image from 'next/image';
import Link from 'next/link';

const SearchBox = ({ items }) => {
  const inputRef = useRef(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        if (inputRef.current) {
          inputRef.current.focus();
          setSelectedIndex(-1);
        }
      }

      if (searchResults.length > 0) {
        if (event.key === 'ArrowDown') {
          event.preventDefault();
          setSelectedIndex((prev) =>
            prev < searchResults.length - 1 ? prev + 1 : 0
          );
        } else if (event.key === 'ArrowUp') {
          event.preventDefault();
          setSelectedIndex((prev) =>
            prev > 0 ? prev - 1 : searchResults.length - 1
          );
        } else if (event.key === 'Enter' && selectedIndex >= 0) {
          event.preventDefault();
          window.location.href = searchResults[selectedIndex].link;
        } else if (event.key === 'Escape') {
          setSearchTerm('');
          setSearchResults([]);
          setSelectedIndex(-1);
          inputRef.current?.blur();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [searchResults, selectedIndex]);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setSearchTerm(value);

    if (value.trim()) {
      const filteredResults = items.reduce((acc, item) => {
        if (item.title.toLowerCase().includes(value.toLowerCase())) {
          acc.push({
            type: 'main',
            title: item.title,
            icon: item.icon,
            link: `/${item.title.toLowerCase().replace(/\s+/g, '-')}`,
          });

          if (item.title === 'Products') {
            acc.push(
              {
                type: 'sub',
                title: 'Add new product',
                parentTitle: item.title,
                link: '/add-new-product',
              },
              {
                type: 'sub',
                title: 'Create new bundle',
                parentTitle: item.title,
                link: '/create-bundles',
              }
            );
          }
        }
        return acc;
      }, []);

      setSearchResults(filteredResults);
    } else {
      setSearchResults([]);
    }
  };

  return (
    <div className="max-w-xl mx-auto w-full sm:min-w-[350px] xl:min-w-[400px] ">
      <div className="flex items-center gap-2.5 rounded-lg pl-4 pr-1.5 py-0 border border-black/10 focus-within:border-border-color bg-surface-100">
        <span className="icon icon-search text-gray-400" />
        <InputField
          ref={inputRef}
          id="searchBox"
          name="searchBox"
          type="text"
          label={false}
          required={false}
          marginBottom="mb-0 w-full"
          placeholder="Search"
          value={searchTerm}
          inputClassName=" w-full !min-h-[36px] text-sm text-gray-300 placeholder:text-gray-300 border-0 bg-transparent focus:border-0 focus:outline-none"
          onChange={handleInputChange}
        />
        <span className="hidden sm:inline-flex items-center gap-1 px-2 py-1 text-[11px] font-[Inter, sans-serif] font-semibold text-gray-300 bg-white rounded-lg  ">
          <span className="inline-flex text-xs text-nowrap">Ctrl + K</span>
        </span>
      </div>

      <div
        id="search-result"
        className={`search-result absolute top-full w-full max-w-[350px] xl:max-w-[400px] bg-white rounded-lg border border-border-color shadow-xl z-10 ${searchTerm.trim() ? 'block' : 'hidden'}`}
      >
        <ul>
          {searchResults.length > 0 ? (
            searchResults.map((result, index) => (
              <li
                key={index}
                className={`border-t border-dark-500/10 ${selectedIndex === index ? 'bg-primary-500/5' : ''}`}
              >
                <Link
                  href={result.link}
                  className={`flex gap-2.5 items-center py-4 px-5 font-bold hover:text-primary-500 transition-base ${
                    selectedIndex === index ? 'text-primary-500' : ''
                  }`}
                >
                  {result.type === 'main' ? (
                    <>
                      <Image
                        src={result.icon}
                        width={32}
                        height={32}
                        alt={result.title}
                      />
                      {result.title}
                    </>
                  ) : (
                    <>
                      <span className="font-normal">{result.parentTitle}</span>
                      {' / '}
                      {result.title}
                    </>
                  )}
                </Link>
              </li>
            ))
          ) : (
            <li className="py-4 px-5 text-sm text-gray-500 text-center">
              No results found
            </li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default SearchBox;
