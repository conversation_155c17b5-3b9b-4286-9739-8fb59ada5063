'use client';

import { useFormikContext } from 'formik';
import React, { useState } from 'react';

const InputField = ({
  id,
  type,
  label,
  required,
  optional,
  placeholder,
  extraInfo,
  showTooltip,
  tooltipId,
  marginBottom = 'mb-3 xl:mb-4',
  inputClassName = 'form-control',
  name,
  value,
  onChange,
  onGenerateValue,
  generateLabel,
  rightText,
  rightTextClassName = 'absolute right-4 text-sm font-medium text-dark-500/40',
  leftIcon, // New prop for left icon
  length = null,
  disabled = false, // Add disabled prop with default value
  readonly =false,
  formik
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const togglePasswordVisibility = () => setShowPassword(!showPassword);
  return (
    <div className={`relative ${marginBottom}`}>
      <label
        htmlFor={id}
        className={`form-label flex items-center w-full ${label ? '' : 'sr-only'}`}
      >
        {label} {required && <span className="text-danger-500">*</span>}{' '}
        {optional && (
          <span className="text-dark-500/40 text-xs font-medium ml-0.5">
            (Optional)
          </span>
        )}
        {showTooltip && (
          <span
            data-tooltip-id={tooltipId}
            className="icon icon-question text-gray-400 text-base font-bold ml-1"
          />
        )}
      </label>

      <div className="relative">
        {leftIcon && (
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-dark-500/60">
            <span
              className={`inline-block icon mt-1.5 text-base ${leftIcon}`}
            />
          </span>
        )}
        <input
          type={type === 'password' && showPassword ? 'text' : type}
          disabled={disabled}
          readOnly={readonly} 
          onKeyDown={(e) => {
            if (type === 'number' && ['e', 'E', '-', '+'].includes(e.key)) {
              e.preventDefault();
            }
          }}
          id={id}
          className={`${inputClassName} ${rightText ? 'pr-10' : ''} 
            ${onGenerateValue ? 'pr-32' : ''} 
            ${leftIcon ? '!pl-10' : ''}
            ${disabled ? 'cursor-not-allowed pointer-events-none !bg-gray-500/5 !text-gray-200' : ''}`}
          placeholder={placeholder}
          value={value}
          onChange={(e) => {
            const inputValue = e.target.value;
            if (length && inputValue.length > length) return;
            if (formik) {
              formik.setFieldValue(name, e.target.value);
              formik.setFieldTouched(name, true, false);
              formik.validateField(name);
            } else if (onChange) {
              onChange(e);
            }
          }}
          onBlur={formik && formik?.handleBlur}
        />

        {/* Dynamic right text */}
        {rightText && (
          <span
            className={`absolute right-3 top-1/2 -translate-y-1/2 text-sm font-medium text-dark-500/40 ${rightTextClassName || ''}`}
          >
            {rightText}
          </span>
        )}

        {/* Password show/hide button */}
        {(id === 'password' && formik?.values['password']) ||
        (id === 'confirmPassword' && formik?.values['confirmPassword']) ? (
          <span
            className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer inline-flex text-dark-500 hover:text-dark-500/60 transition-all duration-300 ease-in-out"
            onClick={togglePasswordVisibility}
          >
            <span
              className={`icon ${showPassword ? 'icon-eye-slash' : 'icon-eye'} text-base align-middle`}
            />
          </span>
        ) : null}

        {/* Generate button */}
        {onGenerateValue && (
          <span
            onClick={onGenerateValue}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-sm font-semibold cursor-pointer text-primary-500 hover:text-primary-600 transition-all duration-300 ease-in-out"
          >
            {generateLabel || 'Generate'}
          </span>
        )}
      </div>

      {extraInfo && (
        <span
          className={`block mt-2 text-xs font-normal ${
            formik &&
            formik?.touched['password'] &&
            formik?.errors['password'] &&
            formik?.values['password'] !== ''
              ? 'text-danger-500'
              : 'text-gray-500'
          }`}
        >
          {extraInfo}
        </span>
      )}

      {formik && formik?.touched?.[id] && formik?.errors[id] && (
        <span className="block text-danger-500 text-xs mt-1">
          {formik?.errors[id]}
        </span>
      )}
    </div>
  );
};

export default InputField;
