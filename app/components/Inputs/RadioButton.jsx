const RadioButton = ({
  values = [],
  name,
  formik,
  labelClassName = '',
  onChange,
}) => {
  if (values.length === 0) {
    return <p className="text-danger-500">No options available</p>;
  }

  const handleChange = (value) => {
    formik.setFieldValue(name, value);
    if (onChange) {
      // If onChange is provided, use it
      onChange(value);
    } 
  };

  return (
    <>
      {values.map((item) => {
        // Check if `item` is an object { label, value } or just a string
        const label = typeof item === 'object' ? item.label : item;
        const value = typeof item === 'object' ? item.value : item;

        return (
          <div
            key={value}
            className="flex items-center space-x-2 cursor-pointer"
            onClick={() => handleChange(value)} // Trigger onChange with value
          >
            {/* Hidden Native Input */}
            <input
              type="radio"
              name={name}
              value={value}
              checked={formik.values[name] === value}
              onChange={() => handleChange(value)} // Trigger onChange with value
              onBlur={formik.handleBlur}
              className="hidden"
            />

            {/* Custom Styled Radio */}
            <div
              className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all
              ${formik.values[name] === value ? 'border-primary-500' : 'border-gray-200'}`}
            >
              {formik.values[name] === value && (
                <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
              )}
            </div>

            {/* Label */}
            <label
              className={`font-medium text-sm text-gray-700 cursor-pointer ${labelClassName}`}
            >
              {label}
            </label>
          </div>
        );
      })}
    </>
  );
};

export default RadioButton;
