'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

const DatePicker = dynamic(() => import('react-datepicker'), {
  ssr: false,
});

import 'react-datepicker/dist/react-datepicker.css';

function YearPicker({
  placeholder = "Select Year",
  onChange,
  className,
  name,
  formik
}) {
  const [selectedYear, setSelectedYear] = useState(null);

  useEffect(() => {
    // Only set the year if there's an actual value in formik
    if (formik.values[name]) {
      const year = new Date(formik.values[name], 0);
      setSelectedYear(year);
    } else {
      setSelectedYear(null);
    }
  }, [formik.values[name]]);

  const handleChange = (date) => {
    const yearValue = date ? date.getFullYear() : null;
    setSelectedYear(date);
    
    formik.setFieldTouched(name, true, false);
    formik.setFieldValue(name, yearValue, true);
    
    if (onChange) {
      onChange(yearValue);
    }
  };

  return (
    <div className="custom-datepicker">
      <DatePicker
        className={`form-control calendar-icon ${className}`}
        selected={selectedYear}
        onChange={handleChange}
        placeholderText={placeholder}
        isClearable={true}
        showYearPicker
        dateFormat="yyyy"
        yearItemNumber={9}
        maxDate={new Date()} // Prevent future years
        minDate={new Date(1900, 0)} // Reasonable minimum year
        showPopperArrow={false}
        popperClassName="popper-box year-picker-popper"
        popperPlacement="top-start"
        closeOnScroll={true}
      />
    </div>
  );
}

export default YearPicker;