'use client';
import React from 'react';
import Link from 'next/link';

const Breadcrumb = ({ items }) => {
  return (
    <nav className="flex items-center gap-1 text-xs">
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <span className="icon icon-caret-right text-gray-300 text-xs"></span>
          )}
          {item.link ? (
            <Link
              href={item.link}
              className="text-gray-300 hover:text-primary-500 transition-base"
            >
              {item.label}
            </Link>
          ) : (
            <span className={`${index === items.length - 1 ? 'text-dark-500' : 'text-gray-300'} font-medium`}>
              {item.label}
            </span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

export default Breadcrumb;