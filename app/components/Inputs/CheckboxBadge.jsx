'use-client';
import React from 'react';

const CheckboxBadge = ({
  categories,
  selectedCategories,
  setFieldValue,
  touched,
  errors,
  id,
  fieldName,
  setFieldTouched,
  formik,
}) => {
  const toggleCategory = async (category) => {
    // Check if category is already selected
    const isSelected = selectedCategories.some(
      (item) => item.slug === category.slug
    );

    let updatedCategories;
    if (isSelected) {
      // Remove category if already selected
      updatedCategories = selectedCategories.filter(
        (item) => item.slug !== category.slug
      );
    } else {
      // Add category if not selected
      updatedCategories = [...selectedCategories, category];
    }

    // Update Formik state
    setFieldValue(fieldName, updatedCategories);
  };
  return (
    <>
      <div className="flex flex-wrap gap-2">
        {categories &&
          categories?.map((category, index) => (
            <label
              key={index}
              className={`relative inline-flex items-center px-3 py-1.5 rounded-lg font-medium text-xs cursor-pointer border border-border-color transition-base
                ${
                  selectedCategories.some((item) => item.slug === category.slug)
                    ? 'border-primary-500 text-primary-500'
                    : 'bg-white  hover:border-primary-500 hover:text-primary-500 text-gray-400'
                }
            `}
            >
              <input
                type="checkbox"
                checked={selectedCategories.some(
                  (item) => item.slug === category.slug
                )}
                onChange={() => toggleCategory(category)}
                className="absolute opacity-0 cursor-pointer h-0 w-0"
              />
              <div className="flex items-center gap-2">
                {/* {selectedCategories.some(
                  (item) => item.slug === category.slug
                ) ? (
                  <span className="icon icon-check-3 font-bold text-xs" />
                ) : null} */}
                <span className="capitalize">{category.Label}</span>
              </div>
            </label>
          ))}
      </div>
      {/* Error message */}
      {touched[id] && errors[id] && (
        <div className="text-danger-500 text-sm mt-1">{errors[id]}</div>
      )}
    </>
  );
};

export default CheckboxBadge;
