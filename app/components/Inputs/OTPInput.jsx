'use client';

import { useEffect } from 'react';

export default function OTPInput({
  length = 6,
  otp,
  handleChange,
  inputRefs,
  className = '',
  error,
  touched,
}) {
  const handleKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  return (
    <>
      <div className={`flex gap-2 ${className}`}>
        {otp.map((digit, index) => (
          <input
            key={index}
            ref={(el) => (inputRefs.current[index] = el)}
            type="text"
            maxLength="1"
            value={digit}
            onChange={(e) => handleChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            className="form-control form-text-bold text-center !py-2 max-h-9"
          />
        ))}
      </div>

      {error && touched && (
        <div className="text-danger-500 text-sm mt-1">{error}</div>
      )}
    </>
  );
}
