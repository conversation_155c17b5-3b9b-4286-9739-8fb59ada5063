import React from 'react';
import Image from 'next/image';

const AttributeField = ({
  id,
  name,
  label,
  options = [], // [{ id, label?, color_code?, image? }]
  value = [], // [{ label, value }]
  onChange,
  required = false,
  optional = false,
  marginBottom = 'mb-4',
  type = 'default',
  colorData = {},
}) => {
  const getColorStyle = (colorCode) =>
    type === 'color' ? { backgroundColor: colorCode || 'transparent' } : {};

  // Check if the option is selected based on its ID
  const isSelected = (optId) => value.some((v) => v.value === optId);

  // Handle selection or deselection of an option
  const handleSelect = (opt) => {
    const valObj = {
      label: opt.label ?? opt.value ?? opt.id, // Using opt.label, opt.value, or opt.id
      value: opt.id, // Use the opt.id as the value
    };

    // If the option is selected, remove it from the array, otherwise add it
    const updated = isSelected(opt.id)
      ? value.filter((v) => v.value !== opt.id) // Remove the selected option
      : [...value, valObj]; // Add the option to the value array

    // Update the parent component state
    onChange(updated);
  };
  return (
    <div className={marginBottom}>
      {label && (
        <label htmlFor={id} className="form-label">
          {label}
          {required && <span className="text-danger-500">*</span>}
          {optional && (
            <span className="text-dark-500/40 text-xs ml-1">(Optional)</span>
          )}
        </label>
      )}
      <div className="flex flex-wrap gap-2">
        {options.map((opt) => {
          const selected = isSelected(opt.id); // Check if the option is selected
          return (
            <div
              key={opt.id}
              onClick={() => handleSelect(opt)} // Toggle the option when clicked
              className={`relative inline-flex items-center gap-2 px-3 py-1 rounded-full font-medium text-sm cursor-pointer border transition-base ${
                selected
                  ? 'border-gray-500/5 bg-gray-500/5'
                  : 'border-border-color bg-white text-dark-500 hover:bg-gray-500/5 hover:border-dark-500/5'
              }`}
            >
              {type === 'color' && opt.color_code && (
                <span
                  className="w-3 h-3 rounded-sm border border-border-color"
                  style={getColorStyle(opt.color_code)}
                />
              )}
              {opt.image && (
                <Image
                  src={opt.image}
                  alt={opt.label ?? opt.id}
                  width={12}
                  height={12}
                  className="w-3 h-3 rounded-sm border border-border-color"
                />
              )}
              {opt.label ?? opt.value}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AttributeField;
