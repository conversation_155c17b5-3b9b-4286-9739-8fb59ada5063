import React from 'react';

const ToggleSwitch = ({ checked, onChange, disabled = false }) => {
  return (
    <label className="relative inline-flex cursor-pointer">
      <input
        type="checkbox"
        className="sr-only peer"
        checked={checked}
        onChange={onChange}
        disabled={disabled}
      />
      <div className={`
        w-9 h-5 
        bg-gray-100 
        peer-focus:outline-none 
        rounded-full 
        peer 
        peer-checked:after:translate-x-full 
        peer-checked:after:border-white 
        after:content-[''] 
        after:absolute 
        after:top-[2px] 
        after:left-[2px] 
        after:bg-white 
        after:rounded-full 
        after:h-4 
        after:w-4 
        after:transition-all
        peer-checked:bg-primary-500
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      `}></div>
    </label>
  );
};

export default ToggleSwitch;