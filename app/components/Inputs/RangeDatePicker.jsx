'use client';

import React, { useState } from 'react';
import dynamic from 'next/dynamic';

const DatePicker = dynamic(() => import('react-datepicker'), {
  ssr: false,
});

import 'react-datepicker/dist/react-datepicker.css';

import { useEffect } from 'react';

function RangeDatePicker({
  placeholder,
  useDateTimeFormat = false,
  onChange,
  className,
  nameFrom,
  nameTo,
  formik,
  disableEndDate = false,
}) {
  const [dateRange, setDateRange] = useState([null, null]);
  // const [startDate, endDate] = dateRange;

  const isYearOnly = nameFrom === 'yearEstablishment';

  useEffect(() => {
    if (disableEndDate) {
      // If end date is disabled, remove it from state and formik
      setDateRange(([start]) => [start, null]);
      formik.setFieldValue(nameTo, null);
    }
  }, [disableEndDate, nameTo]);

  useEffect(() => {
    const from = formik.values[nameFrom]
      ? new Date(formik.values[nameFrom])
      : null;
    const to = formik.values[nameTo] ? new Date(formik.values[nameTo]) : null;

    if (disableEndDate) {
      setDateRange([from, null]);
    } else {
      setDateRange([from, to]);
    }
  }, [formik.values[nameFrom], formik.values[nameTo], disableEndDate]);

  const handleChange = (update) => {
    if (isYearOnly) {
      const rawDate = Array.isArray(update) ? update[0] : update;
      const fromValue = rawDate instanceof Date ? rawDate.getFullYear() : null;
      setDateRange([rawDate, null]);
      formik.setFieldTouched(nameFrom, true, false);
      formik.setFieldValue(nameFrom, fromValue, true);
      if (onChange) onChange([fromValue, null]);
    } else {
      // Normalize update to always be an array
      let fromValue = null;
      let toValue = null;

      if (Array.isArray(update)) {
        [fromValue, toValue] = update;
      } else {
        fromValue = update;
        toValue = disableEndDate ? null : dateRange[1]; // preserve old toValue or null
      }

      if (disableEndDate) {
        toValue = null;
      }

      setDateRange([fromValue, toValue]);

      formik.setFieldTouched(nameFrom, true, false);
      formik.setFieldTouched(nameTo, true, false);
      formik.setFieldValue(nameFrom, fromValue || null, false);
      formik.setFieldValue(nameTo, toValue || null, false);

      setTimeout(() => {
        formik.validateField(nameFrom);
        formik.validateField(nameTo);
      }, 0);

      if (onChange) onChange([fromValue, toValue]);
    }
  };
  const [startDate, setStartDate] = useState(null);

  return (
    <div className="custom-datepicker">
      <DatePicker
         selected={startDate}
         onChange={(date) => {
           setStartDate(date);
           handleChange([date, null]);
         }}
         showYearPicker={isYearOnly}
         placeholderText={placeholder}
         className={`form-control calendar-icon ${className}`}
         dateFormat={isYearOnly ? 'yyyy' : 'MM-dd-yyyy'}
         yearItemNumber={9}
         minDate={new Date(1800, 0)}
         maxDate={new Date()}
         popperClassName="popper-box year-picker-popper"
         popperPlacement="top-start"
         closeOnScroll={true}
      />
    </div>
  );
}

export default RangeDatePicker;
