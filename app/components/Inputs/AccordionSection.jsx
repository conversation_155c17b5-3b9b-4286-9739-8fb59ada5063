'use client';

import React, { useState, useRef } from 'react';

const AccordionSection = ({
  icon,
  title,
  description,
  children,
  defaultOpen = false,
}) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const contentRef = useRef(null);
  const [height, setHeight] = useState(defaultOpen ? 'auto' : '0');

  const toggleAccordion = () => {
    if (isOpen) {
      setHeight(`${contentRef.current.scrollHeight}px`);
      requestAnimationFrame(() => {
        setHeight('0');
      });
      setTimeout(() => setIsOpen(false), 300);
    } else {
      setIsOpen(true);
      setHeight(`${contentRef.current.scrollHeight}px`);
      setTimeout(() => setHeight('auto'), 150);
    }
  };

  return (
    <div className="bg-white border border-border-color rounded-2xl mb-4">
      <div
        className="flex items-center gap-3 p-4 lg:p-5 cursor-pointer select-none"
        onClick={toggleAccordion}
      >
        <div className="flex items-center justify-center w-11 h-11 rounded-full bg-primary-500/5">
          <span className={`icon icon-${icon} text-xl text-primary-500`} />
        </div>
        <div className="flex-1">
          <h2 className="text-base font-bold">{title}</h2>
          <p className="text-sm text-gray-500">{description}</p>
        </div>
        <div className="flex items-center gap-2">
          <span
            className={`icon icon-chevron-down text-xl text-gray-400 transition-transform duration-150 ${isOpen ? 'rotate-180' : ''}`}
          />
          <span
            className={`icon icon-caret-down text-xl text-gray-400 transition-transform duration-150 ${isOpen ? 'rotate-180' : ''}`}
          />
        </div>
      </div>

      <div
        className="overflow-hidden transition-[height] duration-300 ease-in-out"
        style={{ height }}
      >
        <div ref={contentRef} className="pr-5 py-4 lg:py-5 pl-5 lg:pl-19">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AccordionSection;
