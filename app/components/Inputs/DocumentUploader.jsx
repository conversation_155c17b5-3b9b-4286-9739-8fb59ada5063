'use client';

import React, { useState } from 'react';

const DocumentUploader = ({
  maxSizeMB = 2,
  acceptedFormats = ['.pdf'],
  formik,
  name,
}) => {
  const [file, setFile] = useState(null);
  const handleFileUpload = (event) => {
    const uploadedFile = event.target.files?.[0];

    if (uploadedFile) {
      // Validate file size
      if (uploadedFile.size > maxSizeMB * 1024 * 1024) {
        formik.setFieldError(
          name,
          `File size should not exceed ${maxSizeMB} MB`
        );
        return;
      }

      // Validate file type
      const fileExtension =
        '.' + uploadedFile.name.split('.').pop()?.toLowerCase();
      if (!acceptedFormats.includes(fileExtension)) {
        formik.setFieldError(
          name,
          'Invalid file format. Only PDF files are allowed.'
        );
        return;
      }

      formik.setFieldError(name, '');
      // Convert file to Base64
      const reader = new FileReader();
      reader.readAsDataURL(uploadedFile); // Read file as Base64
      reader.onload = () => {
        const base64String = reader.result; // Get Base64 string

        setFile(uploadedFile); // Store file preview
        if (formik && name) {
          formik.setFieldValue(name, base64String); // Store Base64 string in Formik
        }
      };
    }
  };

  const handleFileRemove = () => {
    setFile(null);
    if (formik && name) {
      formik.setFieldValue(name, null); // Clear formik field
    }
  };

  return (
    <div className="space-y-4 w-full">
      {/* Initial Upload State */}
      {!file && (
        <div className="flex justify-between items-center w-full border border-border-color rounded-xl pr-4 lg:pr-8">
          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileUpload}
            accept={acceptedFormats.join(',')}
          />
          <label
            htmlFor="file-upload"
            className="block bg-primary-500 text-white py-3 px-3 lg:px-6 rounded-xl text-center font-medium cursor-pointer hover:bg-primary-600 transition-colors"
          >
            Upload document
          </label>
          <p className="text-sm text-gray-400 font-medium pl-2">
            PDF| Max: {maxSizeMB} MB
          </p>
        </div>
      )}

      {/* File Preview State */}
      {file && (
        <div className="flex gap-3">
          <div className="w-full flex flex-col">
            <div className="w-full border border-border-color rounded-xl py-3 px-4 flex items-center space-x-3">
              <div className="inline-flex items-center gap-2">
                <span className="icon icon-paperclip text-gray-500"></span>
                <p className="text-sm text-dark-500 font-medium">{file.name}</p>
              </div>

              <div className="flex items-center ml-auto pl-2">
                <button
                  onClick={() =>
                    document.getElementById('file-upload-replace')?.click()
                  }
                  className="text-primary-500 hover:text-primary-600 font-bold text-sm cursor-pointer"
                >
                  Replace
                </button>
                <input
                  type="file"
                  id="file-upload-replace"
                  className="hidden"
                  onChange={handleFileUpload}
                  accept={acceptedFormats.join(',')}
                />
              </div>
            </div>

            <p className="text-xs text-gray-500 mt-2">
              Size:{' '}
              {`${Math.floor(file.size / 1024)} KB | Last Updated: ${new Date().toLocaleDateString()}`}
            </p>
          </div>
          <button
            onClick={handleFileRemove}
            className="flex items-center justify-center text-primary-500 border hover:bg-primary-500 hover:text-white border-border-color hover:border-primary-500 w-11 h-11 rounded-xl flex-shrink-0 cursor-pointer transition-base"
          >
            <span className="icon icon-trash font-bold text-base"></span>
          </button>
        </div>
      )}
    </div>
  );
};

export default DocumentUploader;
