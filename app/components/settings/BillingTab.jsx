import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import BaseOffCanvas from '@/app/components/offCanvas/BaseOffCanvas';
import SelectField from '@/app/components/Inputs/SelectField';
import ResidentialAddressSection from '@/app/components/settings/ResidentialAddressSection';
import BankAccountItem from '@/app/components/payment/BankAccountItem';

const BillingTab = () => {
  const [activeMenuId, setActiveMenuId] = useState(null);
  const [showAddPaymentMethod, setShowAddPaymentMethod] = useState(false);
  const [showEditPaymentMethod, setShowEditPaymentMethod] = useState(false);
  const [paymentMethods, setPaymentMethods] = useState([
    {
      id: 1,
      type: 'card',
      name: 'Visa ending in 4242',
      lastFour: '4242',
      holderName: '<PERSON><PERSON>',
      expiryDate: '12/25',
      isDefault: true
    },
    {
      id: 2,
      type: 'bank',
      name: 'Chase Bank',
      lastFour: '1234',
      holderName: 'John Doe',
      isDefault: false
    }
  ]);

  const billingFormik = useFormik({
    initialValues: {
      companyName: 'HubSups Inc.',
      taxId: '12-3456789',
      billingEmail: '<EMAIL>',
      address: '123 Business St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'United States'
    },
    validationSchema: Yup.object({
      companyName: Yup.string().required('Company name is required'),
      billingEmail: Yup.string().email('Invalid email').required('Billing email is required'),
      address: Yup.string().required('Address is required'),
      city: Yup.string().required('City is required'),
      state: Yup.string().required('State is required'),
      postalCode: Yup.string().required('Postal code is required')
    }),
    onSubmit: (values) => {
      console.log('Billing info:', values);
    }
  });

  const paymentMethodFormik = useFormik({
    initialValues: {
      type: 'card',
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      holderName: '',
      bankName: '',
      accountNumber: '',
      routingNumber: ''
    },
    validationSchema: Yup.object({
      type: Yup.string().required('Payment type is required'),
      holderName: Yup.string().required('Holder name is required'),
      cardNumber: Yup.string().when('type', {
        is: 'card',
        then: () => Yup.string().required('Card number is required')
      }),
      expiryDate: Yup.string().when('type', {
        is: 'card',
        then: () => Yup.string().required('Expiry date is required')
      }),
      cvv: Yup.string().when('type', {
        is: 'card',
        then: () => Yup.string().required('CVV is required')
      }),
      bankName: Yup.string().when('type', {
        is: 'bank',
        then: () => Yup.string().required('Bank name is required')
      }),
      accountNumber: Yup.string().when('type', {
        is: 'bank',
        then: () => Yup.string().required('Account number is required')
      }),
      routingNumber: Yup.string().when('type', {
        is: 'bank',
        then: () => Yup.string().required('Routing number is required')
      })
    }),
    onSubmit: (values) => {
      const newPaymentMethod = {
        id: paymentMethods.length + 1,
        type: values.type,
        name: values.type === 'card' 
          ? `Card ending in ${values.cardNumber.slice(-4)}`
          : values.bankName,
        lastFour: values.type === 'card' 
          ? values.cardNumber.slice(-4)
          : values.accountNumber.slice(-4),
        holderName: values.holderName,
        expiryDate: values.expiryDate,
        isDefault: paymentMethods.length === 0
      };
      setPaymentMethods([...paymentMethods, newPaymentMethod]);
      setShowAddPaymentMethod(false);
      paymentMethodFormik.resetForm();
    }
  });

  const handleSetDefault = (id) => {
    setPaymentMethods(methods => 
      methods.map(method => ({
        ...method,
        isDefault: method.id === id
      }))
    );
  };

  const handleRemovePaymentMethod = (id) => {
    setPaymentMethods(methods => methods.filter(method => method.id !== id));
  };

  const billingHistory = [
    { id: 1, date: '2024-01-15', amount: '$99.00', status: 'Paid', invoice: 'INV-001' },
    { id: 2, date: '2023-12-15', amount: '$99.00', status: 'Paid', invoice: 'INV-002' },
    { id: 3, date: '2023-11-15', amount: '$99.00', status: 'Paid', invoice: 'INV-003' }
  ];

  return (
    <>
      <div className="space-y-6">
        {/* Payment Methods */}
        <div className="card !p-0">
          <div className="flex items-center justify-between p-4">
            <h2 className="text-base font-semibold">Payment Methods</h2>
            <button 
              onClick={() => setShowAddPaymentMethod(true)}
              className="btn btn-outline-gray btn-sm flex items-center gap-2"
            >
              <span className="icon icon-plus" />
              Add Payment Method
            </button>
          </div>
          <div className="divide-y divide-border-color">
            {paymentMethods.map((method) => (
              <BankAccountItem
                key={method.id}
                account={method}
                isDefault={method.isDefault}
                onSetDefault={handleSetDefault}
                onRemove={handleRemovePaymentMethod}
                isMenuOpen={activeMenuId === method.id}
                onToggleMenu={setActiveMenuId}
              />
            ))}
          </div>
        </div>

        {/* Billing Address */}
        <div className="card !p-0">
          <div className="flex items-center gap-3 p-4">
            <h2 className="text-base font-semibold">Billing Address</h2>
          </div>
          <form onSubmit={billingFormik.handleSubmit} className="p-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4">
              <InputField
                id="companyName"
                name="companyName"
                type="text"
                label="Company Name"
                placeholder="Enter company name"
                formik={billingFormik}
                required
              />
              <InputField
                id="taxId"
                name="taxId"
                type="text"
                label="Tax ID"
                placeholder="Enter tax ID"
                formik={billingFormik}
              />
              <InputField
                id="billingEmail"
                name="billingEmail"
                type="email"
                label="Billing Email"
                placeholder="Enter billing email"
                formik={billingFormik}
                required
              />
            </div>
            <ResidentialAddressSection 
              formik={billingFormik}
              title="Billing Address"
              showTitle={false}
            />
            <div className="flex justify-end">
              <button type="submit" className="btn btn-primary">
                Save Billing Address
              </button>
            </div>
          </form>
        </div>

        {/* Billing History */}
        <div className="card !p-0">
          <div className="flex items-center gap-3 p-4">
            <h2 className="text-base font-semibold">Billing History</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Invoice</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {billingHistory.map((item) => (
                  <tr key={item.id}>
                    <td className="px-4 py-3 text-sm text-gray-900">{item.date}</td>
                    <td className="px-4 py-3 text-sm text-gray-900">{item.amount}</td>
                    <td className="px-4 py-3">
                      <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-success-100 text-success-800">
                        {item.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">{item.invoice}</td>
                    <td className="px-4 py-3">
                      <button className="text-primary-600 hover:text-primary-900 text-sm">
                        Download
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add Payment Method Form */}
      <BaseOffCanvas
        isOpen={showAddPaymentMethod}
        onClose={() => setShowAddPaymentMethod(false)}
        title="Add Payment Method"
        size="sm"
      >
        <form onSubmit={paymentMethodFormik.handleSubmit}>
          <div className="space-y-6 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
            <SelectField
              name="type"
              className="single-select"
              placeholder="Select payment type"
              formik={paymentMethodFormik}
              options={[
                { value: 'card', label: 'Credit/Debit Card' },
                { value: 'bank', label: 'Bank Account' }
              ]}
            />
            <InputField
              id="holderName"
              name="holderName"
              type="text"
              label="Account Holder Name"
              placeholder="Enter account holder name"
              formik={paymentMethodFormik}
              required
            />
            {paymentMethodFormik.values.type === 'card' ? (
              <>
                <InputField
                  id="cardNumber"
                  name="cardNumber"
                  type="text"
                  label="Card Number"
                  placeholder="1234 5678 9012 3456"
                  formik={paymentMethodFormik}
                  required
                />
                <div className="grid grid-cols-2 gap-4">
                  <InputField
                    id="expiryDate"
                    name="expiryDate"
                    type="text"
                    label="Expiry Date"
                    placeholder="MM/YY"
                    formik={paymentMethodFormik}
                    required
                  />
                  <InputField
                    id="cvv"
                    name="cvv"
                    type="text"
                    label="CVV"
                    placeholder="123"
                    formik={paymentMethodFormik}
                    required
                  />
                </div>
              </>
            ) : (
              <>
                <InputField
                  id="bankName"
                  name="bankName"
                  type="text"
                  label="Bank Name"
                  placeholder="Enter bank name"
                  formik={paymentMethodFormik}
                  required
                />
                <InputField
                  id="accountNumber"
                  name="accountNumber"
                  type="text"
                  label="Account Number"
                  placeholder="Enter account number"
                  formik={paymentMethodFormik}
                  required
                />
                <InputField
                  id="routingNumber"
                  name="routingNumber"
                  type="text"
                  label="Routing Number"
                  placeholder="Enter routing number"
                  formik={paymentMethodFormik}
                  required
                />
              </>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t border-border-color p-4">
            <button type="submit" className="btn btn-primary">
              Add Payment Method
            </button>
            <button 
              type="button" 
              onClick={() => setShowAddPaymentMethod(false)}
              className="btn btn-outline-gray"
            >
              Cancel
            </button>
          </div>
        </form>
      </BaseOffCanvas>
    </>
  );
};

export default BillingTab;
