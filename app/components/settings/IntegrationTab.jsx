import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import ToggleSwitch from '@/app/components/Inputs/ToggleSwitch';
import BaseOffCanvas from '@/app/components/offCanvas/BaseOffCanvas';

const IntegrationTab = () => {
  
  const [apiKeys, setApiKeys] = useState([
    {
      id: 1,
      name: 'Production API Key',
      key: 'sk_live_51H7...',
      maskedKey: 'sk_live_51H7...****',
      createdAt: '2024-01-15',
      lastUsed: '2024-01-20',
      status: 'Active'
    },
    {
      id: 2,
      name: 'Development API Key',
      key: 'sk_test_51H7...',
      maskedKey: 'sk_test_51H7...****',
      createdAt: '2024-01-10',
      lastUsed: '2024-01-19',
      status: 'Active'
    }
  ]);

  const [integrations, setIntegrations] = useState([
    {
      id: 1,
      name: 'Stripe',
      description: 'Payment processing integration',
      status: 'connected',
      icon: 'icon-stripe',
      lastSync: '2024-01-20 10:30 AM'
    },
    {
      id: 2,
      name: 'Shopify',
      description: 'E-commerce platform integration',
      status: 'connected',
      icon: 'icon-shopify',
      lastSync: '2024-01-20 09:15 AM'
    },
    {
      id: 3,
      name: 'Slack',
      description: 'Team communication integration',
      status: 'disconnected',
      icon: 'icon-slack',
      lastSync: null
    },
    {
      id: 4,
      name: 'Google Analytics',
      description: 'Website analytics integration',
      status: 'connected',
      icon: 'icon-google',
      lastSync: '2024-01-20 11:45 AM'
    }
  ]);

  const [showAddApiKey, setShowAddApiKey] = useState(false);
  const [showAddWebhook, setShowAddWebhook] = useState(false);

  const apiKeyFormik = useFormik({
    initialValues: {
      name: '',
      environment: 'production',
      permissions: []
    },
    validationSchema: Yup.object({
      name: Yup.string().required('API key name is required'),
      environment: Yup.string().required('Environment is required')
    }),
    onSubmit: (values) => {
      const newApiKey = {
        id: apiKeys.length + 1,
        name: values.name,
        key: `sk_${values.environment === 'production' ? 'live' : 'test'}_${Math.random().toString(36).substr(2, 20)}`,
        maskedKey: `sk_${values.environment === 'production' ? 'live' : 'test'}_****`,
        createdAt: new Date().toISOString().split('T')[0],
        lastUsed: null,
        status: 'active'
      };
      setApiKeys([...apiKeys, newApiKey]);
      setShowAddApiKey(false);
      apiKeyFormik.resetForm();
    }
  });

  const webhookFormik = useFormik({
    initialValues: {
      url: '',
      events: [],
      description: ''
    },
    validationSchema: Yup.object({
      url: Yup.string().url('Invalid URL').required('Webhook URL is required'),
      events: Yup.array().min(1, 'Select at least one event')
    }),
    onSubmit: (values) => {
      console.log('Webhook created:', values);
      setShowAddWebhook(false);
      webhookFormik.resetForm();
    }
  });

  const handleRevokeApiKey = (id) => {
    setApiKeys(keys => keys.filter(key => key.id !== id));
  };

  const handleToggleIntegration = (id) => {
    setIntegrations(integrations => 
      integrations.map(integration => 
        integration.id === id 
          ? { 
              ...integration, 
              status: integration.status === 'connected' ? 'disconnected' : 'connected',
              lastSync: integration.status === 'disconnected' ? new Date().toLocaleString() : null
            }
          : integration
      )
    );
  };

  const eventOptions = [
    { value: 'user.created', label: 'User Created' },
    { value: 'user.updated', label: 'User Updated' },
    { value: 'order.created', label: 'Order Created' },
    { value: 'order.updated', label: 'Order Updated' },
    { value: 'payment.succeeded', label: 'Payment Succeeded' },
    { value: 'payment.failed', label: 'Payment Failed' }
  ];

  return (
    <>
      <div className="space-y-6">
        {/* API Keys Section */}
        <div className="card !p-0">
          <div className="flex items-center justify-between p-4">
            <h2 className="text-base font-semibold">API Keys</h2>
            <button 
              onClick={() => setShowAddApiKey(true)}
              className="btn btn-outline-gray btn-sm flex items-center gap-2"
            >
              <span className="icon icon-plus text-sm" />
              Generate New Key
            </button>
          </div>
          <div className="divide-y divide-border-color">
            {apiKeys.map((apiKey) => (
              <div key={apiKey.id} className="p-4 flex items-center justify-between">
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-dark-500">{apiKey.name}</h3>
                  <p className="text-sm text-gray-400 font-mono">{apiKey.maskedKey}</p>
                  <div className="flex items-center gap-4 mt-1 text-xs text-gray-400">
                    <span>Created: {apiKey.createdAt}</span>
                    <span className="h-1 w-1 rounded-full bg-gray-400" />
                    {apiKey.lastUsed && <span>Last used: {apiKey.lastUsed}</span>}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    apiKey.status === 'Active' 
                      ? 'bg-success-100 text-success-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {apiKey.status}
                  </span>
                  <button 
                    onClick={() => navigator.clipboard.writeText(apiKey.key)}
                    className="text-primary-600 hover:text-primary-900 text-sm"
                  >
                    Copy
                  </button>
                  <button 
                    onClick={() => handleRevokeApiKey(apiKey.id)}
                    className="text-danger-600 hover:text-danger-900 text-sm"
                  >
                    Revoke
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Third-party Integrations */}
        <div className="card !p-0">
          <div className="flex items-center gap-3 p-4">
            <h2 className="text-base font-semibold">Third-party Integrations</h2>
          </div>
          <div className="divide-y divide-border-color">
            {integrations.map((integration) => (
              <div key={integration.id} className="p-4 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 flex items-center justify-center rounded-lg bg-gray-100">
                    <span className={`${integration.icon} text-xl`} />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-dark-500">{integration.name}</h3>
                    <p className="text-sm text-gray-400">{integration.description}</p>
                    {integration.lastSync && (
                      <p className="text-xs text-gray-400">Last sync: {integration.lastSync}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                    integration.status === 'connected' 
                      ? 'bg-success-100 text-success-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {integration.status}
                  </span>
                  <button 
                    onClick={() => handleToggleIntegration(integration.id)}
                    className={`btn btn-sm ${
                      integration.status === 'connected' 
                        ? 'btn-outline-danger' 
                        : 'btn-primary'
                    }`}
                  >
                    {integration.status === 'connected' ? 'Disconnect' : 'Connect'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Webhook Settings */}
        <div className="card !p-0">
          <div className="flex items-center justify-between p-4">
            <h2 className="text-base font-semibold">Webhook Endpoints</h2>
            <button 
              onClick={() => setShowAddWebhook(true)}
              className="btn btn-primary btn-sm"
            >
              Add Webhook
            </button>
          </div>
          <div className="p-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">
                No webhook endpoints configured. Add a webhook endpoint to receive real-time notifications about events in your account.
              </p>
            </div>
          </div>
        </div>

        {/* Add Webhook Form */}
        {showAddWebhook && (
          <div className="card !p-0">
            <div className="flex items-center justify-between p-4">
              <h2 className="text-base font-semibold">Add Webhook Endpoint</h2>
              <button 
                onClick={() => setShowAddWebhook(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="icon icon-close text-xl" />
              </button>
            </div>
            <form onSubmit={webhookFormik.handleSubmit} className="p-4 space-y-4">
              <InputField
                id="url"
                name="url"
                type="url"
                label="Webhook URL"
                placeholder="https://your-domain.com/webhook"
                formik={webhookFormik}
                required
              />
              <div className="flex flex-col mb-4">
                <span className="form-label">Events to send</span>
                <SelectField
                  name="events"
                  className="multi-select"
                  placeholder="Select events"
                  isMulti={true}
                  formik={webhookFormik}
                  options={eventOptions}
                />
              </div>
              <InputField
                id="description"
                name="description"
                type="text"
                label="Description (Optional)"
                placeholder="Enter webhook description"
                formik={webhookFormik}
              />
              <div className="flex gap-2">
                <button type="submit" className="btn btn-primary">
                  Add Webhook
                </button>
                <button 
                  type="button" 
                  onClick={() => setShowAddWebhook(false)}
                  className="btn btn-outline-gray"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Security Settings */}
        <div className="card !p-0">
          <div className="flex items-center gap-3 p-4">
            <h2 className="text-base font-semibold">Security Settings</h2>
          </div>
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between py-2">
              <div>
                <h3 className="text-sm font-medium text-dark-500">IP Whitelist</h3>
                <p className="text-sm text-gray-400">Restrict API access to specific IP addresses</p>
              </div>
              <ToggleSwitch />
            </div>
            <div className="flex items-center justify-between py-2">
              <div>
                <h3 className="text-sm font-medium text-dark-500">Rate Limiting</h3>
                <p className="text-sm text-gray-400">Enable rate limiting for API requests</p>
              </div>
              <ToggleSwitch defaultChecked={true} />
            </div>
            <div className="flex items-center justify-between py-2">
              <div>
                <h3 className="text-sm font-medium text-dark-500">Webhook Signature Verification</h3>
                <p className="text-sm text-gray-400">Verify webhook signatures for security</p>
              </div>
              <ToggleSwitch defaultChecked={true} />
            </div>
          </div>
        </div>
      </div>

      <BaseOffCanvas
        isOpen={showAddApiKey}
        onClose={() => setShowAddApiKey(false)}
        title="Generate New API Key"
        size="sm"
      >
        <form onSubmit={apiKeyFormik.handleSubmit}>
          <div className="space-y-6 p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
            <InputField
              id="name"
              name="name"
              type="text"
              label="API Key Name"
              placeholder="Enter API key name"
              formik={apiKeyFormik}
              required
            />
            <div className="flex flex-col mb-4">
              <span className="form-label">Environment</span>
              <SelectField
                name="environment"
                className="single-select"
                placeholder="Select environment"
                formik={apiKeyFormik}
                options={[
                  { value: 'production', label: 'Production' },
                  { value: 'development', label: 'Development' }
                ]}
              />
            </div>
          </div>
          {/* Action Buttons */}
          <div className="flex justify-end gap-3 border-t border-border-color p-4">
            <button type="submit" className="btn btn-primary">
              Generate API Key
            </button>
            <button 
              type="button" 
              onClick={() => setShowAddApiKey(false)}
              className="btn btn-outline-gray"
            >
              Cancel
            </button>
          </div>
        </form>
      </BaseOffCanvas>
    </>
  );
};

export default IntegrationTab;
