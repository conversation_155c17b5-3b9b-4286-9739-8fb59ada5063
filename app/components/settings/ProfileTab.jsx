import React from 'react';
import Image from 'next/image';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import RangeDatePicker from '@/app/components/Inputs/RangeDatePicker';
import ResidentialAddressSection from '@/app/components/settings/ResidentialAddressSection';

const ProfileTab = ({ avatarPreview, fileInputRef, triggerFileInput, handleAvatarChange }) => {
  const formik = useFormik({
    initialValues: {
      firstName: 'Ron',
      lastName: 'Vargas',
      email: '<EMAIL>',
      phone: '************',
      language: { value: 'en-US', label: 'English (US)' },
      // Add other form fields initial values
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required('First name is required'),
      lastName: Yup.string().required('Last name is required'),
      email: Yup.string().email('Invalid email address').required('Email is required'),
      phone: Yup.string().required('Phone number is required'),
      language: Yup.object().required('Language is required'),
    }),
    onSubmit: (values) => {
      console.log(values);
      // Handle form submission
    },
  });

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      {/* General Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">General</h2>
        </div>
        <div className="p-4 space-y-6">
          <div>
            <label className="inline-block form-label">Avatar</label>
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 rounded-full bg-gray-100 overflow-hidden">
                <Image
                  src={avatarPreview}
                  alt="Avatar"
                  width={80}
                  height={80}
                  className="w-20 h-20 rounded-full object-cover"
                />
              </div>
              <div className="flex flex-col gap-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleAvatarChange}
                  accept="image/*"
                  className="hidden"
                />
                <button 
                  type="button" 
                  onClick={triggerFileInput}
                  className="btn btn-outline-gray"
                >
                  Change Avatar
                </button>
                <span className="text-xs text-gray-400">
                  Allowed formats: JPG, PNG, GIF (Max size: 5MB)
                </span>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-4 mb-0">
            <InputField
              id="firstName"
              name="firstName"
              type="text"
              label="First Name"
              placeholder="Enter your first name"
              formik={formik}
              required={true}
            />
            <InputField
              id="lastName"
              name="lastName"
              type="text"
              label="Last Name"
              placeholder="Enter your last name"
              formik={formik}
              required={true}
            />
            <InputField
              id="email"
              name="email"
              type="email"
              label="Email"
              placeholder="Enter your email"
              formik={formik}
              required={true}
            />
            <InputField
              id="phone"
              name="phone"
              type="tel"
              label="Phone"
              placeholder="Enter your phone number"
              formik={formik}
              required={true}
            />
            <div>
              <label className="block form-label">Role</label>
              <div className="bg-success-500/10 text-success-500 rounded-lg px-2 py-1 inline-block text-sm font-medium">
                Super Admin
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Personal details */}
      {/* <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Personal Details</h2>
        </div>
        <div className="p-4 space-y-6">
          <div className="grid grid-cols-2 gap-x-4 mb-0">
            <div>
              <label className="form-label mb-2 block">Date of Birth</label>
              <RangeDatePicker
                placeholder="Date of Birth"
                onChange={(dates) => {
                  formik.setFieldValue(
                    'dob',
                    dates?.[0] || ''
                  );
                }}
                disableEndDate={true}
                formik={formik}
                nameFrom="dob"
                nameTo="dob"
              />
            </div>
            <InputField
              id="email"
              name="email"
              type="email"
              label="Email"
              placeholder="Enter your email"
              formik={formik}
              required={true}
            />
            <InputField
              id="phone"
              name="phone"
              type="tel"
              label="Phone"
              placeholder="Enter your phone number"
              formik={formik}
              required={true}
            />
          </div>
        </div>
      </div> */}

      {/* Address Section */}
      <ResidentialAddressSection formik={formik} isResidential={true}/>

      {/* Preferences Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Preferences</h2>
        </div>
        <div className="p-4 grid grid-cols-2 gap-x-4 space-y-6">
          <div className="flex flex-col mb-4">
            <span className="form-label">Timezone</span>
            <SelectField
              name="timezone"
              className="single-select"
              placeholder="Select timezone"
              useMenuPortal={false}
              formik={formik}
              options={[
                { value: '0', label: '(UTC -06:00) Pacific Time US & Canada' },
                { value: '1', label: '(UTC -05:00) Central Time US & Canada' },
                { value: '2', label: '(UTC -04:00) Eastern Time US & Canada' },
                { value: '3', label: '(UTC -03:00) Eastern Time US & Canada' },
              ]}
            />
          </div>
          <div className="flex flex-col mb-4">
            <span className="form-label">Date Format</span>
            <SelectField
              name="date_format"
              className="single-select"
              useMenuPortal={false}
              placeholder="Select date format"
              formik={formik}
              options={[
                { value: '0', label: 'MM/DD/YYYY' },
                { value: '1', label: 'DD/MM/YYYY' },
                { value: '2', label: 'YYYY/MM/DD' },
                { value: '3', label: 'DD-MM-YYYY' },
              ]}
            />
          </div>
          <div className="flex flex-col mb-4">
            <span className="form-label">Time Format</span>
            <SelectField
              name="time_format"
              className="single-select"
              placeholder="Select time format"
              formik={formik}
              options={[
                { value: '0', label: '12-hour' },
                { value: '1', label: '24-hour' },
              ]}
            />
          </div>
          <div className="flex flex-col mb-4">
            <span className="form-label">Language</span>
            <SelectField
              name="language"
              className="single-select"
              placeholder="Select language"
              formik={formik}
              options={[
                { value: '0', label: 'English (US)' },
                { value: '1', label: 'Spanish' },
                { value: '2', label: 'French' },
                { value: '3', label: 'German' },
              ]}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button 
          type="submit" 
          className="btn btn-primary"
          disabled={!formik.isValid || formik.isSubmitting}
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};

export default ProfileTab;