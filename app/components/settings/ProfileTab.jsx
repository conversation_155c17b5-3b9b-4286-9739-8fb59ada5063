import React from 'react';
import Image from 'next/image';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import InputField from '@/app/components/Inputs/InputField';
import SelectField from '@/app/components/Inputs/SelectField';
import RangeDatePicker from '@/app/components/Inputs/RangeDatePicker';
import ResidentialAddressSection from '@/app/components/settings/ResidentialAddressSection';

const ProfileTab = ({ avatarPreview, fileInputRef, triggerFileInput, handleAvatarChange }) => {
  const formik = useFormik({
    initialValues: {
      firstName: 'Ron',
      lastName: 'Vargas',
      email: '<EMAIL>',
      phone: '************',
      jobTitle: 'Senior Product Manager',
      department: 'Product',
      employeeId: 'EMP-001',
      bio: 'Experienced product manager with a passion for creating innovative solutions.',
      website: 'https://ronvargas.com',
      linkedIn: 'https://linkedin.com/in/ronvargas',
      twitter: 'https://twitter.com/ronvargas',
      github: 'https://github.com/ronvargas',
      language: { value: 'en-US', label: 'English (US)' },
      timezone: { value: '0', label: '(UTC -06:00) Pacific Time US & Canada' },
      dateFormat: { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
      timeFormat: { value: '12', label: '12 Hour' },
      address: '123 Main St',
      city: 'New York',
      state: 'NY',
      postalCode: '10001',
      country: 'United States'
    },
    validationSchema: Yup.object({
      firstName: Yup.string().required('First name is required'),
      lastName: Yup.string().required('Last name is required'),
      email: Yup.string().email('Invalid email address').required('Email is required'),
      phone: Yup.string().required('Phone number is required'),
      jobTitle: Yup.string(),
      department: Yup.string(),
      employeeId: Yup.string(),
      bio: Yup.string().max(500, 'Bio must be less than 500 characters'),
      website: Yup.string().url('Invalid URL'),
      linkedIn: Yup.string().url('Invalid URL'),
      twitter: Yup.string().url('Invalid URL'),
      github: Yup.string().url('Invalid URL'),
      language: Yup.object().required('Language is required'),
      timezone: Yup.object().required('Timezone is required'),
      dateFormat: Yup.object().required('Date format is required'),
      timeFormat: Yup.object().required('Time format is required'),
      address: Yup.string(),
      city: Yup.string(),
      state: Yup.string(),
      postalCode: Yup.string(),
      country: Yup.string()
    }),
    onSubmit: (values) => {
      console.log(values);
      // Handle form submission
    },
  });

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      {/* General Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">General</h2>
        </div>
        <div className="p-4 space-y-6">
          <div>
            <label className="inline-block form-label">Avatar</label>
            <div className="flex items-center gap-4">
              <div className="w-20 h-20 rounded-full bg-gray-100 overflow-hidden">
                <Image
                  src={avatarPreview}
                  alt="Avatar"
                  width={80}
                  height={80}
                  className="w-20 h-20 rounded-full object-cover"
                />
              </div>
              <div className="flex flex-col gap-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleAvatarChange}
                  accept="image/*"
                  className="hidden"
                />
                <button 
                  type="button" 
                  onClick={triggerFileInput}
                  className="btn btn-outline-gray"
                >
                  Change Avatar
                </button>
                <span className="text-xs text-gray-400">
                  Allowed formats: JPG, PNG, GIF (Max size: 5MB)
                </span>
              </div>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-x-4 mb-0">
            <InputField
              id="firstName"
              name="firstName"
              type="text"
              label="First Name"
              placeholder="Enter your first name"
              formik={formik}
              required={true}
            />
            <InputField
              id="lastName"
              name="lastName"
              type="text"
              label="Last Name"
              placeholder="Enter your last name"
              formik={formik}
              required={true}
            />
            <InputField
              id="email"
              name="email"
              type="email"
              label="Email"
              placeholder="Enter your email"
              formik={formik}
              required={true}
            />
            <InputField
              id="phone"
              name="phone"
              type="tel"
              label="Phone"
              placeholder="Enter your phone number"
              formik={formik}
              required={true}
            />
            <div>
              <label className="block form-label">Role</label>
              <div className="bg-success-500/10 text-success-500 rounded-lg px-2 py-1 inline-block text-sm font-medium">
                Super Admin
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Social Links Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Social Links</h2>
        </div>
        <div className="p-4 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 mb-0">
            <InputField
              id="website"
              name="website"
              type="url"
              label="Website"
              placeholder="https://yourwebsite.com"
              formik={formik}
              leftIcon="icon-globe"
            />
            <InputField
              id="linkedIn"
              name="linkedIn"
              type="url"
              label="LinkedIn"
              placeholder="https://linkedin.com/in/username"
              formik={formik}
              leftIcon="icon-linkedin-logo"
            />
            <InputField
              id="twitter"
              name="twitter"
              type="url"
              label="X"
              placeholder="https://x.com/username"
              formik={formik}
              leftIcon="icon-x-logo"
            />
            <InputField
              id="github"
              name="github"
              type="url"
              label="GitHub"
              placeholder="https://github.com/username"
              formik={formik}
              leftIcon="icon-github-logo"
            />
          </div>
        </div>
      </div>

      {/* Address Section */}
      <ResidentialAddressSection formik={formik} isResidential={true}/>

      {/* Preferences Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Preferences</h2>
        </div>
        <div className="p-4 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 mb-0">
            <div className="flex flex-col mb-4">
              <span className="form-label">Timezone</span>
              <SelectField
                name="timezone"
                className="single-select"
                placeholder="Select timezone"
                useMenuPortal={false}
                isSearchable
                formik={formik}
                options={[
                  { value: '0', label: '(UTC -06:00) Pacific Time US & Canada' },
                  { value: '1', label: '(UTC -05:00) Central Time US & Canada' },
                  { value: '2', label: '(UTC -04:00) Eastern Time US & Canada' },
                  { value: '3', label: '(UTC -03:00) Eastern Time US & Canada' },
                ]}
              />
            </div>
            <div className="flex flex-col mb-4">
              <span className="form-label">Date Format</span>
              <SelectField
                name="dateFormat"
                className="single-select"
                placeholder="Select date format"
                formik={formik}
                useMenuPortal={false}
                options={[
                  { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY' },
                  { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY' },
                  { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD' },
                  { value: 'DD-MM-YYYY', label: 'DD-MM-YYYY' },
                ]}
              />
            </div>
            <div className="flex flex-col mb-4">
              <span className="form-label">Time Format</span>
              <SelectField
                name="timeFormat"
                className="single-select"
                placeholder="Select time format"
                formik={formik}
                options={[
                  { value: '12', label: '12 Hour (AM/PM)' },
                  { value: '24', label: '24 Hour' }
                ]}
              />
            </div>
            <div className="flex flex-col mb-4">
              <span className="form-label">Language</span>
              <SelectField
                name="language"
                className="single-select"
                placeholder="Select language"
                formik={formik}
                options={[
                  { value: 'en-US', label: 'English (US)' },
                  { value: 'es', label: 'Spanish' },
                  { value: 'fr', label: 'French' },
                  { value: 'de', label: 'German' },
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      <div className=" flex justify-end">
        <button 
          type="submit" 
          className="btn btn-primary"
          disabled={!formik.isValid || formik.isSubmitting}
        >
          Save Changes
        </button>
      </div>
    </form>
  );
};

export default ProfileTab;