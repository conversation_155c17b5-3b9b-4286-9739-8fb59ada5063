import React, { useState } from 'react';
import InputField from '@/app/components/Inputs/InputField';
import Image from 'next/image';
import ToggleSwitch from '@/app/components/Inputs/ToggleSwitch';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const PasswordTab = () => {
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [backupCodes, setBackupCodes] = useState([]);

  const formik = useFormik({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    validationSchema: Yup.object({
      currentPassword: Yup.string().required('Current password is required'),
      newPassword: Yup.string()
        .min(8, 'Password must be at least 8 characters')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
          'Password must contain uppercase, lowercase, number and special character')
        .required('New password is required'),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('newPassword'), null], 'Passwords must match')
        .required('Confirm password is required'),
    }),
    onSubmit: (values) => {
      console.log(values);
      // Handle form submission
    },
  });

  const getPasswordStrength = (password) => {
    if (!password) return { strength: 0, label: '', color: '' };

    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[@$!%*?&]/.test(password)) score++;

    const strengthMap = {
      0: { label: 'Very Weak', color: 'bg-danger-500' },
      1: { label: 'Weak', color: 'bg-danger-400' },
      2: { label: 'Fair', color: 'bg-warning-500' },
      3: { label: 'Good', color: 'bg-warning-400' },
      4: { label: 'Strong', color: 'bg-success-500' },
      5: { label: 'Very Strong', color: 'bg-success-600' }
    };

    return { strength: score, ...strengthMap[score] };
  };

  const passwordStrength = getPasswordStrength(formik.values.newPassword);

  const generateBackupCodes = () => {
    const codes = Array.from({ length: 8 }, () =>
      Math.random().toString(36).substr(2, 8).toUpperCase()
    );
    setBackupCodes(codes);
  };

  const handleTwoFactorToggle = (enabled) => {
    setTwoFactorEnabled(enabled);
    if (enabled) {
      setShowQRCode(true);
      generateBackupCodes();
    } else {
      setShowQRCode(false);
      setBackupCodes([]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Change Password Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Change Password</h2>
        </div>
        <form onSubmit={formik.handleSubmit} className="p-4 space-y-6">
          <InputField
            id="currentPassword"
            name="currentPassword"
            type="password"
            label="Current Password"
            placeholder="Enter your current password"
            formik={formik}
            required={true}
          />

          <div className="space-y-2">
            <InputField
              id="newPassword"
              name="newPassword"
              type="password"
              label="New Password"
              placeholder="Enter your new password"
              formik={formik}
              required={true}
            />
            {formik.values.newPassword && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Password Strength:</span>
                  <span className={`font-medium ${
                    passwordStrength.strength >= 4 ? 'text-success-600' :
                    passwordStrength.strength >= 2 ? 'text-warning-600' : 'text-danger-600'
                  }`}>
                    {passwordStrength.label}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${passwordStrength.color}`}
                    style={{ width: `${(passwordStrength.strength / 5) * 100}%` }}
                  />
                </div>
                <div className="text-xs text-gray-500 space-y-1">
                  <p>Password must contain:</p>
                  <ul className="list-disc list-inside space-y-0.5 ml-2">
                    <li className={formik.values.newPassword.length >= 8 ? 'text-success-600' : 'text-gray-400'}>
                      At least 8 characters
                    </li>
                    <li className={/[a-z]/.test(formik.values.newPassword) ? 'text-success-600' : 'text-gray-400'}>
                      One lowercase letter
                    </li>
                    <li className={/[A-Z]/.test(formik.values.newPassword) ? 'text-success-600' : 'text-gray-400'}>
                      One uppercase letter
                    </li>
                    <li className={/\d/.test(formik.values.newPassword) ? 'text-success-600' : 'text-gray-400'}>
                      One number
                    </li>
                    <li className={/[@$!%*?&]/.test(formik.values.newPassword) ? 'text-success-600' : 'text-gray-400'}>
                      One special character (@$!%*?&)
                    </li>
                  </ul>
                </div>
              </div>
            )}
          </div>

          <InputField
            id="confirmPassword"
            name="confirmPassword"
            type="password"
            label="Confirm Password"
            placeholder="Confirm your new password"
            formik={formik}
            required={true}
          />

          <div className="flex justify-end">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={!formik.isValid || formik.isSubmitting}
            >
              Update Password
            </button>
          </div>
        </form>
      </div>

      {/* Two-Factor Authentication Section */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Two-Factor Authentication</h2>
        </div>
        <div className="p-4 space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-dark-500">Enable Two-Factor Authentication</h3>
              <p className="text-sm text-gray-400">Add an extra layer of security to your account</p>
            </div>
            <ToggleSwitch
              checked={twoFactorEnabled}
              onChange={handleTwoFactorToggle}
            />
          </div>

          {showQRCode && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-4">
              <div className="text-center">
                <h4 className="font-medium mb-2">Scan QR Code</h4>
                <div className="w-48 h-48 bg-white border-2 border-dashed border-border-color rounded-lg flex items-center justify-center mx-auto mb-4">
                  {/* <span className="text-gray-400">QR Code Placeholder</span> */}
                  <Image
                    src="/images/qr-code.png"
                    alt="QR Code"
                    width={192}
                    height={192}
                    className="object-contain"
                  />
                </div>
                <p className="text-sm text-gray-400">
                  Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                </p>
              </div>

              <div className="text-center">
                <p className="text-sm font-medium mb-2">Or enter this code manually:</p>
                <code className="bg-gray-500/10 px-3 py-1 rounded text-sm">
                  JBSWY3DPEHPK3PXP
                </code>
              </div>
            </div>
          )}

          {/* {backupCodes.length > 0 && (
            <div className="bg-warning-50 border border-warning-200 p-4 rounded-lg">
              <h4 className="font-medium text-warning-800 mb-2">Backup Codes</h4>
              <p className="text-sm text-warning-700 mb-3">
                Save these backup codes in a safe place. You can use them to access your account if you lose your phone.
              </p>
              <div className="grid grid-cols-2 gap-2 mb-3">
                {backupCodes.map((code, index) => (
                  <code key={index} className="bg-white px-2 py-1 rounded text-sm border">
                    {code}
                  </code>
                ))}
              </div>
              <button
                onClick={() => navigator.clipboard.writeText(backupCodes.join('\n'))}
                className="btn btn-sm btn-outline-warning"
              >
                Copy All Codes
              </button>
            </div>
          )} */}
        </div>
      </div>

      {/* Security Preferences */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Security Preferences</h2>
        </div>
        <div className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-dark-500">Login Notifications</h3>
              <p className="text-sm text-gray-400">Get notified when someone logs into your account</p>
            </div>
            <ToggleSwitch defaultChecked={true} />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-dark-500">Session Timeout</h3>
              <p className="text-sm text-gray-400">Automatically log out after 30 minutes of inactivity</p>
            </div>
            <ToggleSwitch defaultChecked={true} />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-dark-500">Password Expiry</h3>
              <p className="text-sm text-gray-400">Require password change every 90 days</p>
            </div>
            <ToggleSwitch />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PasswordTab;