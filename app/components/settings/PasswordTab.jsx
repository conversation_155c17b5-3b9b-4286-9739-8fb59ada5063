import React from 'react';
import InputField from '@/app/components/Inputs/InputField';
import { useFormik } from 'formik';
import * as Yup from 'yup';

const PasswordTab = () => {
  const formik = useFormik({
    initialValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    validationSchema: Yup.object({
      currentPassword: Yup.string().required('Current password is required'),
      newPassword: Yup.string().required('New password is required'),
      confirmPassword: Yup.string().required('Confirm password is required'),
    }),
    onSubmit: (values) => {
      console.log(values);
      // Handle form submission
    },
  });

  return (
    <div className="space-y-6 pb-6">
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Change Password</h2>
        </div>
        <form onSubmit={formik.handleSubmit} className="p-4 space-y-6">
          <InputField
            type="password"
            label="Current Password"
            name="currentPassword"
            placeholder="Enter current password"
            required={true}
            formik={formik}
          />
          <InputField
            type="password"
            label="New Password"
            name="newPassword"
            placeholder="Enter new password"
            required={true}
            formik={formik}
          />
          <InputField
            type="password"
            label="Confirm Password"
            name="confirmPassword"
            placeholder="Enter confirm password"
            marginBottom="mb-0"
            required={true}
            formik={formik}
          />
        </form>
      </div>
      <div className="flex justify-end">
        <button 
          type="submit" 
          className="btn btn-primary"
          disabled={!formik.isValid || formik.isSubmitting}
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default PasswordTab;