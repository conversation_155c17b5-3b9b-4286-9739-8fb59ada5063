import React from 'react';
import <PERSON>Field from '@/app/components/Inputs/SelectField';
import { useFormik } from 'formik';



const NotificationSection = ({ title, items }) => (

  <div className="card !p-0 overflow-hidden mb-6">
    <div className="flex items-center gap-3 p-4">
      <h2 className="text-base font-semibold">{title}</h2>
    </div>
    <div className="p-4">
      {items.map((item, index) => (
        <div key={item.id} className={`flex items-center justify-between py-2 ${index !== items.length - 1 ? 'border-b border-border-color' : ''}`}>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-dark-500">{item.label}</h3>
            {item.description && <p className="text-sm text-gray-400 mt-0.5">{item.description}</p>}
          </div>
          <div className="flex items-center border border-border-color rounded-lg overflow-hidden">
            <button 
              className={`inline-flex items-center gap-2 px-4 py-2 border-r border-border-color text-sm font-medium transition-colors min-w-[115px] ${
                item.email ? 'bg-surface-300' : 'hover:bg-gray-100'
              }`}
              onClick={() => item.onToggle('email')}
            >
              <span className="icon icon-message text-base" />
              Email
            </button>
            <button 
              className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium transition-colors min-w-[115px] ${
                item.browser ? 'bg-surface-300' : 'hover:bg-gray-100'
              }`}
              onClick={() => item.onToggle('browser')}
            >
              <span className="icon icon-globe text-base" />
              Browser
            </button>
            {/* <button 
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                item.app ? 'bg-surface-300' : 'hover:bg-gray-100'
              }`}
              onClick={() => item.onToggle('app')}
            >
              <span className="icon icon-app" />
              App
            </button> */}
          </div>
        </div>
      ))}
    </div>
  </div>
);

const NotificationTab = () => {
  const [notificationSettings, setNotificationSettings] = React.useState({
    general: [
      { id: 'news', label: 'News and Updates', description: 'Product updates, feature announcements', email: true, browser: false, app: false },
      { id: 'accountActivity', label: 'Account Activity', description: 'Login attempts, profile changes', email: true, browser: true, app: false },
      { id: 'newDevice', label: 'New Device Login', description: 'Alerts when signing in from new devices', email: true, browser: false, app: false },
      { id: 'reminders', label: 'Reminders', description: 'Task reminders and follow-ups', email: false, browser: true, app: true },
      { id: 'maintenance', label: 'System Maintenance', description: 'Scheduled maintenance notifications', email: true, browser: false, app: false }
    ],
    orders: [
      { id: 'newOrders', label: 'New Orders', description: 'Notifications for new customer orders', email: true, browser: true, app: true },
      { id: 'orderUpdates', label: 'Order Status Updates', description: 'Order processing, shipping updates', email: true, browser: false, app: true },
      { id: 'paymentReceived', label: 'Payment Received', description: 'Payment confirmations and receipts', email: true, browser: false, app: false },
      { id: 'refunds', label: 'Refunds & Returns', description: 'Refund requests and return notifications', email: true, browser: true, app: false },
      { id: 'failedPayments', label: 'Failed Payments', description: 'Payment failures and retry attempts', email: true, browser: true, app: true }
    ],
    inventory: [
      { id: 'lowStock', label: 'Low Stock Alerts', description: 'When inventory falls below threshold', email: true, browser: true, app: true },
      { id: 'outOfStock', label: 'Out of Stock', description: 'When products are completely out of stock', email: true, browser: true, app: true },
      { id: 'stockReceived', label: 'Stock Received', description: 'New inventory arrivals', email: false, browser: true, app: false },
      { id: 'expiryAlerts', label: 'Expiry Alerts', description: 'Products nearing expiration date', email: true, browser: false, app: false }
    ],
    vendors: [
      { id: 'newVendors', label: 'New Vendor Applications', description: 'New vendor registration requests', email: true, browser: true, app: false },
      { id: 'vendorUpdates', label: 'Vendor Profile Updates', description: 'Changes to vendor information', email: false, browser: true, app: false },
      { id: 'vendorOrders', label: 'Vendor Orders', description: 'Orders placed with vendors', email: true, browser: false, app: true },
      { id: 'vendorPayments', label: 'Vendor Payments', description: 'Payment processing for vendors', email: true, browser: false, app: false }
    ],
    marketing: [
      { id: 'campaignUpdates', label: 'Campaign Updates', description: 'Marketing campaign performance', email: false, browser: true, app: false },
      { id: 'promotions', label: 'Promotions', description: 'New promotions and discounts', email: true, browser: false, app: false },
      { id: 'analytics', label: 'Analytics Reports', description: 'Weekly and monthly analytics reports', email: true, browser: false, app: false }
    ]
  });

  const formik = useFormik({
    initialValues: {
      notificationFrequency: 'real-time'
    },
    onSubmit: values => {
      console.log(values);
    }
  });

  const handleToggle = (section, itemId, type) => {
    setNotificationSettings(prev => ({
      ...prev,
      [section]: prev[section].map(item => 
        item.id === itemId 
          ? { ...item, [type]: !item[type] }
          : item
      )
    }));
  };

  return (
    <div className="space-y-6">
      <NotificationSection
        title="General Notifications"
        items={notificationSettings.general.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('general', item.id, type)
        }))}
      />
      <NotificationSection
        title="Order Notifications"
        items={notificationSettings.orders.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('orders', item.id, type)
        }))}
      />
      <NotificationSection
        title="Inventory Notifications"
        items={notificationSettings.inventory.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('inventory', item.id, type)
        }))}
      />
      <NotificationSection
        title="Vendor Notifications"
        items={notificationSettings.vendors.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('vendors', item.id, type)
        }))}
      />
      <NotificationSection
        title="Marketing Notifications"
        items={notificationSettings.marketing.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('marketing', item.id, type)
        }))}
      />

      {/* Notification Preferences */}
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-base font-semibold">Notification Preferences</h2>
        </div>
        <div className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-dark-500">Do Not Disturb</h3>
              <p className="text-sm text-gray-400">Pause all notifications during specified hours</p>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">10:00 PM - 8:00 AM</span>
              <button className="text-primary-600 hover:text-primary-900 text-sm">Edit</button>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-dark-500">Notification Frequency</h3>
              <p className="text-sm text-gray-400">How often to receive notification summaries</p>
            </div>
            <SelectField 
              name="notificationFrequency"
              className="single-select min-w-[150px]"
              placeholder="Select frequency"
              formik={formik}
              useMenuPortal={false}
              options={[
                { value: 'real-time', label: 'Real-time' },
                { value: 'hourly', label: 'Hourly' },
                { value: 'daily', label: 'Daily' },
                { value: 'weekly', label: 'Weekly' }
              ]}
            />
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <button
          type="submit"
          className="btn btn-primary"
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default NotificationTab;