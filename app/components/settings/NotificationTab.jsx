import React from 'react';

const NotificationSection = ({ title, items }) => (
  <div className="card !p-0 overflow-hidden mb-6">
    <div className="flex items-center gap-3 p-4">
      <h2 className="text-base font-semibold">{title}</h2>
    </div>
    <div className="p-4">
      {items.map((item, index) => (
        <div key={item.id} className={`flex items-center justify-between py-2 ${index !== items.length - 1 ? 'border-b border-border-color' : ''}`}>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-dark-500">{item.label}</h3>
            {item.description && <p className="text-sm text-gray-400 mt-0.5">{item.description}</p>}
          </div>
          <div className="flex items-center border border-border-color rounded-lg overflow-hidden">
            <button 
              className={`inline-flex items-center gap-2 px-4 py-2 border-r border-border-color text-sm font-medium transition-colors min-w-[115px] ${
                item.email ? 'bg-surface-300' : 'hover:bg-gray-100'
              }`}
              onClick={() => item.onToggle('email')}
            >
              <span className="icon icon-message text-base" />
              Email
            </button>
            <button 
              className={`inline-flex items-center gap-2 px-4 py-2 text-sm font-medium transition-colors min-w-[115px] ${
                item.browser ? 'bg-surface-300' : 'hover:bg-gray-100'
              }`}
              onClick={() => item.onToggle('browser')}
            >
              <span className="icon icon-globe text-base" />
              Browser
            </button>
            {/* <button 
              className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                item.app ? 'bg-surface-300' : 'hover:bg-gray-100'
              }`}
              onClick={() => item.onToggle('app')}
            >
              <span className="icon icon-app" />
              App
            </button> */}
          </div>
        </div>
      ))}
    </div>
  </div>
);

const NotificationTab = () => {
  const [notificationSettings, setNotificationSettings] = React.useState({
    general: [
      { id: 'news', label: 'News', email: false, browser: false, app: false },
      { id: 'accountActivity', label: 'Account activity', email: false, browser: false, app: false },
      { id: 'newDevice', label: 'New device used to sign in', email: false, browser: false, app: false },
      { id: 'reminders', label: 'Reminders', email: false, browser: false, app: false }
    ],
    project: [
      { id: 'mentions', label: 'Someone mentions you', email: false, browser: false, app: false },
      { id: 'replies', label: 'Someone replies to your message', email: false, browser: false, app: false },
      { id: 'taskStatus', label: 'Task status updated', email: false, browser: false, app: false },
      { id: 'taskAssigned', label: 'Task assigned to you', email: false, browser: false, app: false }
    ],
    sales: [
      { id: 'newProduct', label: 'New product', email: false, browser: false, app: false },
      { id: 'newOrder', label: 'New order placed', email: false, browser: false, app: false }
    ]
  });

  const handleToggle = (section, itemId, type) => {
    setNotificationSettings(prev => ({
      ...prev,
      [section]: prev[section].map(item => 
        item.id === itemId 
          ? { ...item, [type]: !item[type] }
          : item
      )
    }));
  };

  return (
    <div>
      <NotificationSection 
        title="General Notification" 
        items={notificationSettings.general.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('general', item.id, type)
        }))}
      />
      <NotificationSection 
        title="Project Notification" 
        items={notificationSettings.project.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('project', item.id, type)
        }))}
      />
      <NotificationSection 
        title="Sales Notification" 
        items={notificationSettings.sales.map(item => ({
          ...item,
          onToggle: (type) => handleToggle('sales', item.id, type)
        }))}
      />
      <div className="flex justify-end">
        <button 
          type="submit" 
          className="btn btn-primary"
        >
          Save Changes
        </button>
      </div>
    </div>
  );
};

export default NotificationTab;