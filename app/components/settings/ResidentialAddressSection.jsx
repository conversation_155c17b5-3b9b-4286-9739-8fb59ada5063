import { useLoadScript, Autocomplete } from '@react-google-maps/api';
import { useRef, useState } from 'react';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import { usaCityOptions, usaStateOptions } from '@/utils/constant';
import { useFormikContext } from 'formik';

const GOOGLE_MAPS_API_KEY = ''; // Replace with your key
const libraries = ['places'];

const AddressAutocomplete = ({ formik }) => {
  const [autocomplete, setAutocomplete] = useState(null);
  const inputRef = useRef(null);

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    libraries,
  });

  const onLoad = (autocompleteInstance) => {
    setAutocomplete(autocompleteInstance);
  };

  const onPlaceChanged = () => {
    if (!autocomplete) return;

    const place = autocomplete.getPlace();
    if (!place.address_components) return;

    let address = place.formatted_address;
    let city = '';
    let state = '';
    let postalCode = '';

    place.address_components.forEach((component) => {
      if (component.types.includes('locality')) {
        city = component.long_name;
      }
      if (component.types.includes('administrative_area_level_1')) {
        state = component.long_name;
      }
      if (component.types.includes('postal_code')) {
        postalCode = component.long_name;
      }
    });

    // Update Formik values
    formik.setValues({
      ...formik.values,
      address,
      city,
      state,
      postalCode,
    });
  };

  return (
    <>
      {/* {isLoaded && (
         <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}   options={{ componentRestrictions: { country: 'us' } }}> */}
      <InputField
        id="address"
        name="address"
        type="text"
        label="Address"
        placeholder="Enter address"
        required={false}
        value={formik.values.address}
        formik={formik}
      />
      {/* </Autocomplete>
      )} */}
    </>
  );
};

export default function ResidentialAddressSection({
  className = 'mb-4',
  title = 'Address',
  showTitle = true,
  formik // Accept formik prop directly
}) {
  // Remove useFormikContext since we're passing formik directly
  // const formik = useFormikContext();

  // Add null check for formik
  const handleBlur = (e) => {
    if (formik && formik.handleBlur) {
      formik.handleBlur(e);
    }
  };

  return (
    <div className={`card !p-0 ${className}`}>
      {showTitle && title && (
        <div className="flex items-center gap-3 p-4">
          <h4 className="text-base font-semibold">{title}</h4>
        </div>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-x-4 p-4">
        <div className="col-span-full">
          <AddressAutocomplete formik={formik} />
        </div>
        <div className="flex flex-col mb-4 xl:mb-0">
          <span className="form-label">
            City / Town
          </span>
          <SelectField
            className="single-select"
            name="city"
            placeholder="Select city / town"
            useMenuPortal={false}
            // isDisabled = {formik.values.city ? false :true}
            options={
              // { value: formik.values.city, label: formik.values.city }
              usaCityOptions
            }
            formik={formik}
          />
          {formik?.touched?.city && formik?.errors?.city && (
            <div className="text-danger-500 text-xs mt-1">
              {formik?.errors?.city}
            </div>
          )}
        </div>

        <div className="flex flex-col mb-4 xl:mb-0">
          <span className="form-label">
            State / Region
          </span>
          <SelectField
            className="single-select"
            name="state"
            useMenuPortal={false}
            placeholder="Select state / region"
            // isDisabled = {formik.values.state ? false :true}
            options={
              // { value: formik.values.state, label: formik.values.state },
              usaStateOptions
            }
            formik={formik}
          />
          {formik?.touched?.state && formik?.errors?.state && (
            <div className="text-danger-500 text-xs mt-1">
              {formik?.errors?.state}
            </div>
          )}
        </div>

        <div className="flex flex-col mb-4 xl:mb-0">
          <InputField
            id="postalCode"
            name="postalCode"
            type="text"
            label="ZIP / Postal Code"
            placeholder="Enter postal code"
            marginBottom="mb-0"
            inputClassName="form-control w-full"
            required={false}
            onBlur={handleBlur} // Use the safe handler
            value={formik?.values?.postalCode || ''}
            formik={formik}
          />
        </div>
      </div>
    </div>
  );
}