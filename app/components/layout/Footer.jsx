import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  ABOUT_US,
  CONTACT_US,
  FAQ,
  TERMS_AND_CONDITIONS,
  PRIVACY_POLICY,
} from '@/routes/urls';

const footerLinks = [
  { type: ABOUT_US, label: 'About Us' },
  { type: CONTACT_US, label: 'Contact Us' },
  { type: FAQ, label: 'FAQs' },
  { type: TERMS_AND_CONDITIONS, label: 'Terms & Conditions' },
  { type: PRIVACY_POLICY, label: 'Privacy Policy' },
];

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative z-10 mt-auto bg-surface-200">
      <div className="max-w-(--container-8xl) mx-auto py-2.5 px-4">
        <div className="flex justify-between items-center gap-1.5">
          {/* Secure SSL encryption */}
          <div>
            <Image
              src="/images/secure-ssl.png"
              width={90}
              height={40}
              className="object-contain"
              alt="Secure SSL encryption"
              title="Secure SSL encryption"
            />
          </div>

          {/* Copyright content */}
          <span className="text-xs">
            All rights reserved &copy; {currentYear},{' '}
            <Link href="/" title="Hubsups">
              Hubsups.com
            </Link>
          </span>

          {/* Footer menu */}
          <ul className="flex space-x-2 lg:space-x-2">
            {footerLinks.map(({ type, label }, index) => (
              <li
                key={type}
                className={
                  index !== footerLinks.length - 1
                    ? 'relative after:content-[""] after:absolute after:right-0 after:top-1/2 after:h-3 after:w-[1px] after:bg-gray-100 after:-translate-y-1/2 pr-2 lg:pr-2'
                    : ''
                }
              >
                <Link
                  href={type}
                  title={label}
                  className="text-xs font-medium hover:text-primary-500 transition-base"
                >
                  {label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </footer>
  );
}
