'use client';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useDispatch } from 'react-redux';
import dynamic from 'next/dynamic';
import BaseModal from '../modals/BaseModal';
import { logoutUserAsync } from '@/store/api/authApi';
import { LOGIN } from '@/routes/urls';
import { getCookie } from '@/utils/function';
import { cookiesKey } from '@/utils/cookiesStorageKeys';
import { onboardingStatus } from '@/utils/constant';
import UnderReviewModal from '../modals/UnderReviewModal';
import PrivateHeader from './PrivateHeader';

// const PrivateHeader = dynamic(() => import('./PrivateHeader'), {
//   ssr: false,
// });

function PrivateLayout({ children }) {
  const dispatch = useDispatch();
  const router = useRouter();

  const onboardingStatusCookie = useMemo(() => {
    const encodedStatus = getCookie(cookiesKey.onboardingStatus) || '';
    return encodedStatus
      ? Buffer.from(encodedStatus, 'base64').toString('utf-8')
      : '';
  }, []);

  const [isReviewModal, setIsReviewModal] = useState(null);

  const handleLogout = useCallback(async () => {
    try {
      const { payload } = await dispatch(logoutUserAsync());
      if (payload.ok) {
        router.push(LOGIN);
      }
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [dispatch, router]);

  useEffect(() => {
    if (!isReviewModal) {
      setIsReviewModal(onboardingStatusCookie !== onboardingStatus.APPROVED);
    }
  }, [onboardingStatusCookie]);

  return (
    <>
      <PrivateHeader />
      {children}
      <BaseModal
        isOpen={false}
        onClose={() => {}}
        title=""
        paragraph=""
        customClass="p-8"
        size="sm"
      >
        <UnderReviewModal handleLogout={handleLogout} />
      </BaseModal>
    </>
  );
}

export default React.memo(PrivateLayout);
