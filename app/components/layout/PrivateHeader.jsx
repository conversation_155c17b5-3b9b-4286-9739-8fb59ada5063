'use client';
import React, { useState, useEffect, useRef, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import SearchBox from '../Inputs/SearchField';
import { useDispatch } from 'react-redux';
import { useRouter } from 'next/navigation';
import { logoutUserAsync } from '@/store/api/authApi';
import {
  ACTIVITY_LOG,
  LOGIN,
  LOGOUT,
  PROFILE,
  SELLER_GUIDE,
  GENERAL,
  UPGRADE_PLAN,
} from '@/routes/urls';
import { Tooltip } from 'react-tooltip';

const PrivateHeader = () => {
  const dispatch = useDispatch();
  const router = useRouter();

  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [notificationMenuOpen, setNotificationMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const profileRef = useRef(null);
  const notificationRef = useRef(null);

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
  };


  // Function to toggle profile menu
  const toggleProfileMenu = () => {
    setProfileMenuOpen(!profileMenuOpen);
  };

  // Function to toggle notification menu
  const toggleNotificationMenu = () => {
    setNotificationMenuOpen(!notificationMenuOpen);
  };

  // Update click outside handler to include notification menu
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        profileRef.current &&
        !profileRef.current.contains(event.target) &&
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setProfileMenuOpen(false);
        setNotificationMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Close menus when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        profileRef.current &&
        !profileRef.current.contains(event.target)
      ) {
        setProfileMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const accountItems = [
    { type: PROFILE, icon: 'icon-user', label: 'Profile' },
    { type: GENERAL, icon: 'icon-gear', label: 'General Settings' },
    { type: ACTIVITY_LOG, icon: 'icon-activity', label: 'Activity Log' },
    // { divider: true },
    // { type: UPGRADE_PLAN, icon: 'icon-crown-simple', label: 'Upgrade Plan' },
    { divider: true },
    { type: LOGOUT, icon: 'icon-sign-out', label: 'Log out' },
  ];

  // Function to logout user

  const handleLogout = useCallback(async () => {
    try {
      const { payload } = await dispatch(logoutUserAsync());
      if (payload.ok) {
        router.push(LOGIN);
      }
    } catch (error) {
      console.error('Logout failed:', error);
    }
  }, [dispatch, router]);

  const handleProfileAction = (type) => {
    if (type === LOGOUT) {
      handleLogout();
    } else {
      router.push(type);
    }
  };

  return (
    // border-gradient-image
    <div className="px-3 sm:px-6 py-1.5 min-h-[60px] fixed top-0 left-0 right-0 z-40 w-full bg-white border-b-1 border-border-color">
      <header className="flex justify-between items-center ">
        <div className="logo-container">
          <Link href="/">
            <Image
              src="/images/logo.svg" // Use absolute path for Next.js Image
              width={116}
              height={44}
              alt="Hubsups"
              title="Hubsups"
              // className="h-auto"
            />
          </Link>
        </div>

        {/* Search Field */}
        <span
          className="icon icon-search text-gray-400 text-2xl block sm:hidden"
          onClick={toggleSearch}
        />
        <div
          className={`${isSearchOpen ? 'block absolute w-full sm:w-auto top-full sm:top-auto left-0 sm:left-auto sm:relative p-4 sm:p-0 bg-lighter-100 sm:bg-transparent border-t border-dark-500/10' : 'hidden '} sm:block`}
        >
          <SearchBox items={[]} />
        </div>

        <div className="">
          <div className="flex items-center gap-x-2 lg:gap-x-4 w-full justify-end">

            <Link 
              href="/support-helpdesk" 
              className="relative flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8 rounded-lg cursor-pointer hover:bg-dark-500/10 transition-base"
              // data-tooltip-id="support-tooltip"
              // data-tooltip-content="Support & Helpdesk"
            >
              <span className="icon icon-help text-xl align-middle" />
              {/* <span className="absolute w-2 h-2 bg-primary-500 rounded-full right-1.5 top-1.5" /> */}
              <small className="inline-flex justify-center items-center bg-primary-500 w-3.5 h-3.5 text-white font-medium absolute px-1 text-[10px] rounded-full right-0 -top-0">
                5
              </small>
            </Link>
            {/* <Tooltip
              id="support-tooltip"
              className="!bg-dark-500 !px-2 !py-1 !text-xs !font-medium !rounded"
            /> */}


            {/* Notification */}
            <div className="notification-wrapper flex items-center relative" ref={notificationRef}>
              <div 
                className="relative flex justify-center items-center w-6 lg:w-8 h-6 lg:h-8 rounded-lg cursor-pointer hover:bg-dark-500/10 transition-base"
                onClick={toggleNotificationMenu}
              >
                <span className="icon icon-bell text-xl align-middle" />
                {/* Added dot indicator for new notifications */}
                {/* <span className="absolute w-2 h-2 bg-primary-500 rounded-full right-1.5 top-1.5" /> */}
                <small className="inline-flex justify-center items-center bg-primary-500 w-3.5 h-3.5 text-white font-medium absolute px-1 text-[10px] rounded-full right-0 -top-0">
                  1
                </small>
              </div>
              
              <div className={`absolute top-full right-0 mt-2.5 transform transition-all duration-200 ease-in-out ${
                notificationMenuOpen 
                  ? 'opacity-100 translate-y-0 pointer-events-auto' 
                  : 'opacity-0 -translate-y-2 pointer-events-none'
              }`}>
                <div className="bg-white w-[400px] rounded-xl shadow-[0px_25px_40px_-10px_#1C273114] border border-border-color">
                  <div className="flex items-center justify-between p-4 border-b-1 border-border-color">
                    <div className="flex flex-col">
                      <h4 className="text-sm font-semibold">Notifications</h4>
                      {/* <span className="text-sm text-gray-400">Stay updated with your latest notifications</span> */}
                    </div>
                    <button
                      onClick={toggleNotificationMenu}
                      className="text-gray-400 hover:text-danger-500 transition-base cursor-pointer leading-1"
                    >
                      <span className="icon icon-x text-md align-middle" />
                    </button>
                  </div>

                  <div className="space-y-1">
                    <div className="max-h-[322px] overflow-y-auto">
                      {[5, 7, 10, 12].map((hours) => (
                        <div 
                          key={hours} 
                          className={`flex items-start gap-3 p-3 hover:bg-gray-500/5 cursor-pointer not-first:border-t border-border-color ${
                            hours < 8 ? 'bg-gray-500/5' : ''  // Highlight unread notifications (less than 8 hours old)
                          }`}
                        >
                          <div className={`relative flex flex-shrink-0 items-center justify-center w-9 h-9 rounded-lg bg-primary-500/5 ${
                              hours < 8 ? '' : ''
                            }`}>
                            <span className="icon icon-bell-ringing text-primary-500 text-lg" />
                          </div>
                          <div className="w-full flex flex-col gap-1">
                            <div className="flex items-center justify-between gap-2">
                              <h6 className="text-sm font-medium text-dark-500 flex items-center gap-2">
                                John Doe has received an order
                              </h6>
                              <span className={`w-2 h-2 bg-primary-500 border border-white rounded-full ${hours < 8 ? '' : 'hidden'}`} />
                            </div>
                            <p className="text-xs text-gray-400 line-clamp-2">
                              Lorem Ipsum is simply dummy text of the printing and typesetting industry.
                            </p>
                            <span className="text-xs text-gray-300 whitespace-nowrap">{hours}h ago</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <Link 
                    href="/notifications"
                    className="block text-center text-sm font-medium text-dark-500 hover:text-primary-600 p-3 border-t border-border-color transition-base"
                  >
                    View all notifications
                  </Link>
                </div>
              </div>
            </div>

            {/* Profile */}
            <div className="profile-wrapper relative" ref={profileRef}>
              <button
                type="button"
                className="bg-gray-100 w-6 lg:w-8 h-6 lg:h-8 rounded-lg flex justify-center items-center text-xs font-bold uppercase cursor-pointer"
                onClick={toggleProfileMenu}
              >
                jd
              </button>
              <div className={`absolute top-full right-0 mt-2.5 transform transition-all duration-200 ease-in-out ${
                profileMenuOpen 
                  ? 'opacity-100 translate-y-0 pointer-events-auto' 
                  : 'opacity-0 -translate-y-2 pointer-events-none'
              }`}>
                <ul className="bg-white w-[270px] rounded-xl p-3 -right-2.5 shadow-[0px_25px_40px_-10px_#1C273114] border border-border-color flex flex-wrap gap-1">
                  <li className="flex items-center gap-2 w-full pb-3 mb-2 border-b-1 border-border-color">
                    <span className="bg-gray-100 w-6 lg:w-8 h-6 lg:h-8 rounded-lg flex justify-center items-center text-xs font-bold uppercase cursor-pointer flex-shrink-0" >
                      jd
                    </span>
                    <div className="flex flex-col">
                      <span className="text-sm font-semibold text-dark-500">John Doe</span>
                      <span className="text-xs text-gray-400 whitespace-nowrap overflow-hidden text-ellipsis inline-block max-w-[204px]"><EMAIL></span>
                    </div>
                  </li>
                  {accountItems.map((item, index) =>
                    item.divider ? (
                      <hr
                        key={index}
                        className="border-t border-dark-500/10 my-1 w-full"
                      />
                    ) : (
                      <li key={item.label} className="w-full">
                        <button
                          onClick={() => handleProfileAction(item.type)}
                          className="flex items-center gap-2 w-full text-sm font-medium py-1.5 px-3 hover:bg-primary-500/10 hover:text-primary-500 rounded-md transition-base"
                        >
                          <span className={`icon text-base ${item.icon}`} />
                          {item.label}
                        </button>
                      </li>
                    )
                  )}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </header>
    </div>
  );
};

export default PrivateHeader;
