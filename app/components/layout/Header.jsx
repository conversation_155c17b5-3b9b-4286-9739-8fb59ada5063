'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { HOME, LOGIN } from '@/routes/urls';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUserAsync } from '@/store/api/authApi';
import { useRouter } from 'next/navigation';

export default function Header() {
  const pathname = usePathname();
  const { userToken } = useSelector((state) => state.auth);
  const dispatch = useDispatch((state) => state.auth);
  const router = useRouter();

  const handleLogout = async () => {
    const { payload } = await dispatch(logoutUserAsync());
    if (payload.ok) {
      router.push(LOGIN);
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-40 w-full bg-white border-b-2 border-solid border-gradient-image">
      <div className="max-w-(--container-full) xl:max-w-(--container-8xl) mx-auto py-2 px-4">
        <div className="flex justify-between items-center gap-1.5">
          <Link href="/">
            <Image
              src="./images/logo.svg"
              width={116}
              height={44}
              alt="Hubsups"
              title="Hubsups"
              className="md:max-lg:w-[130px] md:max-lg:h-auto"
            />
          </Link>

          <div className="flex items-center gap-1.5 text-dark-500 text-sm">
            {pathname === HOME ? (
              <>
                Already Registered?{' '}
                <Link
                  href={LOGIN}
                  className="text-primary-500 hover:text-primary-600 font-semibold"
                >
                  Login
                </Link>{' '}
                here
              </>
            ) : userToken?.token ? (
              <button
                type="button"
                onClick={handleLogout}
                className="inline-flex items-center gap-2 text-dark-500 hover:text-primary-500 font-bold transition-all duration-200 ease-in-out cursor-pointer"
              >
                <span className="icon icon-sign-out text-lg" />
                Logout
              </button>
            ) : (
              <Link
                href={HOME}
                className="text-primary-500 hover:text-primary-600 font-bold"
              >
                Register for free
              </Link>
            )}
          </div>
        </div>
      </div>
      {/* <span className='block w-full h-0.5 bg-gradient-to-r from-[#EF2672] via-[#DD2F7B] to-[#129CFB]' /> */}
    </header>
  );
}
