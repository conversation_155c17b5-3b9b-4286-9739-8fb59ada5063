'use client';

import React, { useState } from 'react';
import DataTable from 'react-data-table-component';
import Image from 'next/image';
import { Tooltip } from 'react-tooltip';
import Swal from 'sweetalert2';
import { showSuccessToast } from '@/utils/function';
import SortIcon from '../table/SortIcon';
import CommonPagination from '../table/CommonPagination';

const CustomCheckbox = React.forwardRef(({ onClick, ...rest }, ref) => (
  <div 
    onClick={onClick}
    ref={ref}
    className={`w-4 h-4 rounded border-2 cursor-pointer flex items-center justify-center transition-all duration-200 ease-in-out ${
      rest.checked 
        ? 'bg-primary-500 border-primary-500' 
        : 'border-border-color hover:border-gray-100'
    }`}
  >
    {rest.checked && <span className="icon icon-check-2 text-white text-[8px]" />}
  </div>
));

CustomCheckbox.displayName = 'CustomCheckbox';

const ServiceCategoriesTable = ({ 
  categories, 
  onView, 
  onEdit, 
  onDelete, 
  onStatusToggle,
  selectedRows,
  setSelectedRows 
}) => {
  const [sortedField, setSortedField] = useState('');
  const [sortDirection, setSortDirection] = useState('asc');

  const handleSort = (column, direction) => {
    setSortedField(column.selector);
    setSortDirection(direction);
  };

  const handleDelete = async (category) => {
    const hasSubcategories = category.subcategories && category.subcategories.length > 0;
    
    if (hasSubcategories) {
      Swal.fire({
        title: 'Cannot Delete Category',
        text: 'This category has subcategories. Please delete subcategories first.',
        icon: 'warning',
        confirmButtonText: 'OK',
        customClass: {
          popup: 'rounded-2xl',
          confirmButton: '!rounded-lg px-4 py-2'
        }
      });
      return;
    }

    const result = await Swal.fire({
      title: 'Delete Service Category',
      html: `
        <div class="text-left">
          <p class="mb-3 text-sm text-center text-gray-600">Are you sure you want to delete this service category? This action cannot be undone.</p>
          <div class="bg-gray-50 p-3 rounded-lg border">
            <div class="flex items-center gap-3 mb-2">
              ${category.icon && category.icon.length > 0 ? 
                `<img src="${category.icon[0].preview || category.icon[0]}" alt="${category.name}" class="w-8 h-8 rounded object-cover" />` : 
                '<div class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center"><span class="icon icon-folder text-gray-400 text-sm"></span></div>'
              }
              <div>
                <p class="text-sm font-medium text-gray-900">${category.name}</p>
                <p class="text-xs text-gray-500">${category.slug}</p>
              </div>
            </div>
            ${category.description ? `<p class="text-xs text-gray-600">${category.description}</p>` : ''}
          </div>
        </div>
      `,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#db3545',
      cancelButtonColor: '#b3b6b5',
      confirmButtonText: 'Yes, Delete Category',
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2 !font-medium',
        cancelButton: '!rounded-lg px-4 py-2 !font-medium'
      }
    });

    if (result.isConfirmed) {
      onDelete(category);
      
      Swal.fire({
        title: 'Deleted!',
        text: 'Service category has been deleted successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false,
        customClass: {
          popup: 'rounded-2xl'
        }
      });
    }
  };

  const handleStatusToggle = async (category) => {
    const newStatus = !category.status;
    const action = newStatus ? 'activate' : 'deactivate';

    const result = await Swal.fire({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Service Category`,
      text: `Are you sure you want to ${action} "${category.name}"?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: newStatus ? '#10b857' : '#f59e0b',
      cancelButtonColor: '#6B7280',
      confirmButtonText: `Yes, ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      cancelButtonText: 'Cancel',
      customClass: {
        popup: 'rounded-2xl',
        confirmButton: '!rounded-lg px-4 py-2',
        cancelButton: '!rounded-lg px-4 py-2'
      }
    });

    if (result.isConfirmed) {
      onStatusToggle(category);
      showSuccessToast(`Service category ${action}d successfully`);
    }
  };

  const columns = [
    {
      name: 'Category ID',
      selector: row => row.id,
      sortable: true,
      width: '120px',
      cell: row => (
        <span className="text-sm font-medium text-gray-900">
          #{row.id}
        </span>
      )
    },
    {
      name: 'Category Info',
      selector: row => row.name,
      sortable: true,
      grow: 2,
      cell: row => (
        <div className="flex items-center gap-3 py-2">
          {row.icon && row.icon.length > 0 ? (
            <Image 
              src={row.icon[0].preview || row.icon[0]} 
              alt={row.name}
              className="w-10 h-10 object-cover rounded-lg"
              width={40}
              height={40}
              priority
            />
          ) : (
            <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
              <span className="icon icon-folder text-gray-400 text-lg" />
            </div>
          )}
          <div className="flex-1">
            <h3 className={`text-sm font-medium ${!row.status ? 'text-gray-400' : 'text-gray-900'}`}>
              {row.name}
            </h3>
            <p className="text-xs text-gray-500 line-clamp-1">
              {row.slug}
            </p>
            {row.description && (
              <p className="text-xs text-gray-400 mt-1 line-clamp-1">
                {row.description}
              </p>
            )}
          </div>
        </div>
      )
    },
    {
      name: 'Subcategories',
      selector: row => row.subcategories?.length || 0,
      sortable: true,
      cell: row => (
        <span className="text-sm text-gray-600">
          {row.subcategories?.length || 0}
        </span>
      )
    },
    {
      name: 'Homepage',
      selector: row => row.displayOnHomepage,
      sortable: true,
      cell: row => (
        row.displayOnHomepage ? (
          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary-100 text-primary-700">
            Yes
          </span>
        ) : (
          <span className="text-xs text-gray-400">No</span>
        )
      )
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
          row.status 
            ? 'bg-success-100 text-success-700' 
            : 'bg-gray-100 text-gray-600'
        }`}>
          {row.status ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      name: 'Created Date',
      selector: row => row.createdAt,
      sortable: true,
      cell: row => (
        <span className="text-xs text-gray-500">
          {new Date(row.createdAt).toLocaleDateString()}
        </span>
      )
    },
    {
      name: 'Actions',
      cell: row => (
        <div className="flex items-center gap-1">
          <button
            type="button"
            className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-100 hover:text-primary-600 transition-base"
            onClick={() => onView(row)}
            data-tooltip-id="view-tooltip"
            data-tooltip-content="View category"
          >
            <span className="icon icon-eye text-sm" />
          </button>

          <button
            type="button"
            className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-primary-100 hover:text-primary-600 transition-base"
            onClick={() => onEdit(row)}
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit category"
          >
            <span className="icon icon-pencil-line text-sm" />
          </button>

          <button
            type="button"
            className={`w-8 h-8 flex items-center justify-center rounded-lg transition-base ${
              row.status 
                ? 'hover:bg-warning-100 hover:text-warning-600' 
                : 'hover:bg-success-100 hover:text-success-600'
            }`}
            onClick={() => handleStatusToggle(row)}
            data-tooltip-id="status-tooltip"
            data-tooltip-content={row.status ? 'Deactivate' : 'Activate'}
          >
            <span className={`icon ${row.status ? 'icon-eye-slash' : 'icon-eye'} text-sm`} />
          </button>

          <button
            type="button"
            className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-danger-100 hover:text-danger-600 transition-base"
            onClick={() => handleDelete(row)}
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete category"
          >
            <span className="icon icon-trash text-sm" />
          </button>
        </div>
      )
    }
  ];

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '72px',
      },
    },
  };

  return (
    <>
      <DataTable
        columns={columns}
        data={categories}
        customStyles={customStyles}
        selectableRows
        selectableRowsComponent={CustomCheckbox}
        onSelectedRowsChange={({ selectedRows }) => setSelectedRows(selectedRows)}
        className="custom-table auto-height-table"
        pagination
        sortIcon={<SortIcon sortDirection={sortDirection} />}
        onSort={handleSort}
        sortField={sortedField}
        defaultSortAsc={true}
        paginationPerPage={8}
        paginationRowsPerPageOptions={[8]}
        paginationComponentOptions={{
          rowsPerPageText: 'Rows per page:',
          rangeSeparatorText: 'of',
          selectAllRowsItem: false,
          noRowsPerPage: true,
        }}
        paginationComponent={(props) => (
          <CommonPagination
            selectedCount={props.selectedRows?.length}
            total={props.totalRows}
            page={props.currentPage}
            perPage={props.rowsPerPage}
            onPageChange={props.onChangePage}
          />
        )}
      />

      {/* Tooltips */}
      <Tooltip id="view-tooltip" />
      <Tooltip id="edit-tooltip" />
      <Tooltip id="status-tooltip" />
      <Tooltip id="delete-tooltip" />
    </>
  );
};

export default ServiceCategoriesTable;
