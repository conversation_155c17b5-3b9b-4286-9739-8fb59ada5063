'use client';

import React from 'react';
import Image from 'next/image';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';

const ServiceCategoryViewModal = ({ isOpen, onClose, category }) => {
  if (!category) return null;

  return (
    <BaseOffCanvas
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      title="Service Category Details"
    >
      <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto">
        <div className="space-y-6">
          {/* Basic Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Basic Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Category ID</label>
                <p className="text-sm text-gray-900 mt-1">#{category.id}</p>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Status</label>
                <p className="mt-1">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                    category.status 
                      ? 'bg-success-100 text-success-700' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {category.status ? 'Active' : 'Inactive'}
                  </span>
                </p>
              </div>
              <div className="col-span-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Category Name</label>
                <p className="text-sm text-gray-900 mt-1 font-medium">{category.name}</p>
              </div>
              <div className="col-span-2">
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Slug / Code</label>
                <p className="text-sm text-gray-900 mt-1 font-mono bg-gray-100 px-2 py-1 rounded">
                  {category.slug}
                </p>
              </div>
            </div>
          </div>

          {/* Description */}
          {category.description && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Description</h3>
              <p className="text-sm text-gray-700 leading-relaxed">
                {category.description}
              </p>
            </div>
          )}

          {/* Media */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Media</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Category Icon</label>
                <div className="mt-2">
                  {category.icon && category.icon.length > 0 ? (
                    <Image 
                      src={category.icon[0].preview || category.icon[0]} 
                      alt={category.name}
                      className="w-16 h-16 object-cover rounded-lg border"
                      width={64}
                      height={64}
                      priority
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="icon icon-image text-gray-400 text-xl" />
                    </div>
                  )}
                </div>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Category Image</label>
                <div className="mt-2">
                  {category.image && category.image.length > 0 ? (
                    <Image 
                      src={category.image[0].preview || category.image[0]} 
                      alt={category.name}
                      className="w-16 h-16 object-cover rounded-lg border"
                      width={64}
                      height={64}
                      priority
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="icon icon-image text-gray-400 text-xl" />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Category Settings */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Category Settings</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Parent Category</label>
                <p className="text-sm text-gray-900 mt-1">
                  {category.parentCategory || 'None (Root Category)'}
                </p>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Sort Order</label>
                <p className="text-sm text-gray-900 mt-1">{category.sortOrder}</p>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Display on Homepage</label>
                <p className="mt-1">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                    category.displayOnHomepage 
                      ? 'bg-primary-100 text-primary-700' 
                      : 'bg-gray-100 text-gray-600'
                  }`}>
                    {category.displayOnHomepage ? 'Yes' : 'No'}
                  </span>
                </p>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subcategories Count</label>
                <p className="text-sm text-gray-900 mt-1">
                  {category.subcategories?.length || 0}
                </p>
              </div>
            </div>
          </div>

          {/* Subcategories */}
          {category.subcategories && category.subcategories.length > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Subcategories</h3>
              <div className="space-y-2">
                {category.subcategories.map((sub) => (
                  <div key={sub.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                    <div className="flex items-center gap-3">
                      {sub.icon && sub.icon.length > 0 ? (
                        <Image 
                          src={sub.icon[0].preview || sub.icon[0]} 
                          alt={sub.name}
                          className="w-8 h-8 object-cover rounded"
                          width={32}
                          height={32}
                          priority
                        />
                      ) : (
                        <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                          <span className="icon icon-folder text-gray-400 text-sm" />
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium text-gray-900">{sub.name}</p>
                        <p className="text-xs text-gray-500">{sub.slug}</p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                      sub.status 
                        ? 'bg-success-100 text-success-700' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {sub.status ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Metadata */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-sm font-semibold text-gray-900 mb-3">Metadata</h3>
            <div className="grid grid-cols-1 gap-4">
              <div>
                <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Created Date</label>
                <p className="text-sm text-gray-900 mt-1">
                  {new Date(category.createdAt).toLocaleString()}
                </p>
              </div>
              {category.createdBy && (
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Created By</label>
                  <p className="text-sm text-gray-900 mt-1">{category.createdBy}</p>
                </div>
              )}
              {category.updatedAt && (
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wide">Last Updated</label>
                  <p className="text-sm text-gray-900 mt-1">
                    {new Date(category.updatedAt).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end p-4 border-t border-border-color">
        <button
          type="button"
          className="btn btn-outline-gray"
          onClick={onClose}
        >
          Close
        </button>
      </div>
    </BaseOffCanvas>
  );
};

export default ServiceCategoryViewModal;
