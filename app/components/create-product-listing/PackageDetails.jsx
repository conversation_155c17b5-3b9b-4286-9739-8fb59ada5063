'use client';

import React, { useEffect, useState } from 'react';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import { useFormikContext } from 'formik';
import { useDispatch } from 'react-redux';
import {
  getProdcutUnits,
  getProdcutUnitsMeasurements,
  getProdcutUnitsWeigths,
} from '@/store/api/productApi';

const PackageDetails = () => {
  const dispatch = useDispatch();
  const formik = useFormikContext();
  const [components, setComponents] = useState(
    formik.values.components || ['']
  );
  const [packagingData, setPackagingData] = useState({
    units: [],
    unitMeasurement: [],
    unitWeight: [],
  });

  const getPackagingUnitsData = async () => {
    const results = await Promise.allSettled([
      dispatch(getProdcutUnits()),
      dispatch(getProdcutUnitsMeasurements()),
      dispatch(getProdcutUnitsWeigths()),
    ]);

    const [unitsResult, measurementResult, weightResult] = results;

    const safeGetPayload = (result) =>
      result.status === 'fulfilled' && result.value?.payload?.ok
        ? result.value.payload.data
        : [];

    setPackagingData({
      units: safeGetPayload(unitsResult).map((e) => {
        return { label: e.name, value: e.id };
      }),
      unitMeasurement: safeGetPayload(measurementResult).map((e) => {
        return { label: e.name, value: e.id };
      }),
      unitWeight: safeGetPayload(weightResult).map((e) => {
        return { label: e.name, value: e.id };
      }),
    });
  };

  useEffect(() => {
    getPackagingUnitsData();
  }, []);

  const handleAddComponent = () => {
    const newComponents = [...formik.values.components, ''];
    formik.setFieldValue('components', newComponents);
  };

  const handleRemoveComponent = (index) => {
    if (formik.values.components.length == 1) {
      return;
    }
    const newComponents = formik.values.components.filter(
      (_, i) => i !== index
    );
    formik.setFieldValue('components', newComponents);

    const newTouched = { ...formik.touched.components };
    delete newTouched[index];
    formik.setTouched({ ...formik.touched, components: newTouched }, false);
  };

  const handleComponentChange = (index, value) => {
    const newComponents = [...formik.values.components];
    newComponents[index] = value;
    formik.setFieldValue('components', newComponents);
  };

  return (
    <div className="bg-white border border-border-color rounded-xl mb-4">
      <div className="flex items-center gap-3 p-4">
        <h2 className="text-base font-semibold">Packaging Information</h2>
      </div>

      <div className="space-y-4 p-4 pt-2">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="flex flex-col">
            <span className="form-label">Product Unit</span>
            <SelectField
              className="single-select"
              name="product_unit"
              placeholder="eg. Box, Piece, Unit"
              options={packagingData.units}
              formik={formik}
            />
            {formik.touched?.product_unit && formik.errors?.product_unit && (
              <p className="text-danger-500 text-xs mt-1">
                {formik.errors?.product_unit}
              </p>
            )}
          </div>

          <div className="flex flex-col">
            <span className="form-label">Weight</span>
            <InputField
              id="weight"
              name="weight"
              type="number"
              marginBottom="mb-0"
              placeholder="Weight"
              value={formik.values.weight}
              onChange={formik.handleChange}
              formik={formik}
              rightTextClassName="!right-7 w-[68px]"
              rightText={
                <SelectField
                  className="single-select input-group-select !border-0 !shadow-none !p-0 !w-24"
                  name="weight_unit"
                  options={packagingData.unitWeight}
                  formik={formik}
                />
              }
            />
            {formik.touched?.['weight_unit'] &&
              formik.errors?.['weight_unit'] && (
                <p className="text-danger-500 text-xs mt-1">
                  {formik.errors?.['weight_unit']}
                </p>
              )}
          </div>
        </div>

        <div className="flex flex-col">
          <label className="form-label">Dimensions</label>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {['dimensions_length', 'dimensions_width', 'dimensions_height'].map(
              (dimension) => (
                <div key={dimension}>
                  <InputField
                    id={dimension}
                    name={dimension}
                    type="number"
                    placeholder={
                      dimension.charAt(0).toUpperCase() + dimension.slice(1)
                    }
                    marginBottom="0"
                    value={formik.values[dimension]}
                    onChange={formik.handleChange}
                    formik={formik}
                    rightTextClassName="!right-0"
                    rightText={
                      <SelectField
                        className="single-select input-group-select !border-0 !shadow-none !p-0 !w-24"
                        name="dimensions_unit"
                        options={packagingData.unitMeasurement}
                        formik={formik}
                      />
                    }
                  />
                  {formik.touched?.['dimensions_unit'] &&
                    formik.errors?.['dimensions_unit'] && (
                      <div className="text-danger-500 text-xs mt-1">
                        {formik.errors?.['dimensions_unit']}
                      </div>
                    )}
                </div>
              )
            )}
          </div>
        </div>

        <div className="flex flex-col">
          <div className="flex justify-between items-center">
            <span className="form-label mb-0">Included Components</span>
            <button
              type="button"
              className="inline-flex items-center gap-1 text-primary-500 text-xs font-semibold hover:text-primary-600"
              onClick={handleAddComponent}
            >
              <span className="icon icon-plus text-sm" />
              Add more
            </button>
          </div>

          <div
            className={`flex flex-col gap-1 ${components.length > 0 ? 'mt-2' : ''}`}
          >
            {formik?.values?.components?.map((component, index) => (
              <>
                <div key={index} className="flex items-center gap-2">
                  <input
                    type="text"
                    name={`component[${index}]`}
                    className={`form-control flex-1 ${
                      formik?.errors?.components?.[index] &&
                      formik?.touched?.components?.[index]
                        ? 'border-red-500'
                        : ''
                    }`}
                    placeholder="Enter component"
                    value={component}
                    onChange={(e) =>
                      handleComponentChange(index, e.target.value)
                    }
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveComponent(index)}
                    className="text-danger-500 hover:text-danger-600 hover:bg-danger-500/10 w-8 h-8 flex items-center justify-center rounded-md cursor-pointer transition-base"
                  >
                    <span className="icon icon-trash text-lg align-middle" />
                  </button>
                </div>

                {formik?.errors?.components &&
                  formik?.touched?.components &&
                  formik?.errors?.components?.[index] &&
                  formik?.touched?.components && (
                    <p className="text-danger-500 text-xs mt-1">
                      {formik.errors?.components?.[index]}
                    </p>
                  )}
              </>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PackageDetails;
