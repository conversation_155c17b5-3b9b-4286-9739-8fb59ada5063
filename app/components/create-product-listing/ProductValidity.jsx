import React from 'react';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import RangeDatePicker from '../Inputs/RangeDatePicker';
import SelectField from '../Inputs/SelectField';
import FileUpload from '../Inputs/FileUpload';
import { useFormikContext } from 'formik';

const ProductValidity = () => {
  const formik = useFormikContext();
  return (
    <div className="bg-white border border-border-color rounded-xl mb-4">
      <div className="flex items-center gap-3 p-4">
        <h2 className="text-sm font-bold">Product Policies</h2>
      </div>

      <div className="p-4 mb-0 border-b border-border-color">
        <div className="mb-4">
          <label className="form-label mb-2 block">Validity Duration</label>
          <RangeDatePicker
            placeholder="From - To"
            onChange={(dates) => {
              formik.setFieldValue('validityFrom', dates?.[0] || '');
              formik.setFieldValue('validityTo', dates?.[1] || '');
            }}
            formik={formik}
            nameFrom="validityFrom"
            nameTo="validityTo"
          />
          {formik.touched.validityFrom && formik.errors.validityFrom && (
            <div className="text-danger-500 text-xs mt-1">
              {formik.errors.validityFrom}
            </div>
          )}
          {formik.touched.validityTo && formik.errors.validityTo && (
            <div className="text-danger-500 text-xs mt-1">
              {formik.errors.validityTo}
            </div>
          )}
        </div>
        <div className="flex flex-col gap-4">
          <CustomCheckbox
            id="lifeTimeValidity"
            name="lifeTimeValidity"
            label="Life-Time Validity"
            checked={formik.values.lifeTimeValidity === 'Yes'}
            onChange={(e) => {
              formik.setFieldValue(
                'lifeTimeValidity',
                e.target.checked ? 'Yes' : 'No'
              );
            }}
          />
          <CustomCheckbox
            id="acceptScratchDent"
            name="acceptScratchDent"
            label="Accept Scratch or Dent?"
            checked={formik.values.acceptScratchhDent}
            onChange={(e) => {
              formik.setFieldValue('acceptScratchDent', e.target.checked);
            }}
          />
        </div>
      </div>
      <div className="space-y-3">
        <div className="p-4 mb-0">
          {/* Warranty Toggle */}
          <div className="flex justify-between items-center">
            <CustomCheckbox
              id="hasWarranty"
              name="hasWarranty"
              label="This product has warranty"
              checked={formik.values.hasWarranty === 'Yes'}
              onChange={(e) => {
                formik.setFieldValue(
                  'hasWarranty',
                  e.target.checked ? 'Yes' : 'No'
                );
              }}
            />
          </div>

          {formik.values.hasWarranty === 'Yes' && (
            <div className="grid grid-cols-2 gap-4 p-4 mt-6">
              <div className="flex flex-col">
                <span className="form-label">Duration Type</span>
                <SelectField
                  className="single-select"
                  name="durationType"
                  placeholder=""
                  options={[
                    { value: 'days', label: 'Days' },
                    { value: 'months', label: 'Months' },
                    { value: 'years', label: 'Years' },
                  ]}
                  formik={formik}
                />
                {formik.touched.durationType && formik.errors.durationType && (
                  <div className="text-danger-500 text-xs mt-1">
                    {formik.errors.durationType}
                  </div>
                )}
              </div>
              <div className="flex flex-col">
                <span className="form-label">Duration Value</span>
                <SelectField
                  className="single-select"
                  name="durationValue"
                  placeholder=""
                  options={[
                    { value: '30', label: '30' },
                    { value: '60', label: '60' },
                    { value: '90', label: '90' },
                  ]}
                  formik={formik}
                />
                {formik.touched.durationValue &&
                  formik.errors.durationValue && (
                    <div className="text-danger-500 text-xs mt-1">
                      {formik.errors.durationValue}
                    </div>
                  )}
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col p-4 mb-0 border-t border-border-color">
          {/* Returns Toggle */}
          <CustomCheckbox
            id="acceptReturns"
            name="acceptReturns"
            label="This product has returns"
            checked={formik.values.acceptReturns === 'Yes'}
            onChange={(e) => {
              formik.setFieldValue(
                'acceptReturns',
                e.target.checked ? 'Yes' : 'No'
              );
            }}
          />
          {formik.touched.acceptReturns && formik.errors.acceptReturns && (
            <div className="text-danger-500 text-xs mt-1">
              {formik.errors.acceptReturns}
            </div>
          )}

          {formik.values.acceptReturns === 'Yes' && (
            <div className="mt-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="flex flex-col mb-4">
                  <span className="form-label">
                    Days for Return Acceptance
                  </span>
                  <SelectField
                    className="single-select"
                    name="returnDays"
                    placeholder=""
                    options={[
                      { value: '7', label: '7 Days' },
                      { value: '14', label: '14 Days' },
                      { value: '30', label: '30 Days' },
                    ]}
                    formik={formik}
                  />
                  {formik.touched.returnDays && formik.errors.returnDays && (
                    <div className="text-danger-500 text-xs mt-1">
                      {formik.errors.returnDays}
                    </div>
                  )}
                </div>
                <div className="flex flex-col mb-4">
                  <span className="form-label">Return Policy</span>
                  <SelectField
                    className="single-select"
                    name="returnPolicy"
                    placeholder=""
                    options={[
                      { value: 'full_refund', label: 'Full Refund' },
                      { value: 'store_credit', label: 'Store Credit' },
                      { value: 'exchange', label: 'Exchange Only' },
                    ]}
                    formik={formik}
                  />
                  {formik.touched.returnPolicy &&
                    formik.errors.returnPolicy && (
                      <div className="text-danger-500 text-xs mt-1">
                        {formik.errors.returnPolicy}
                      </div>
                    )}
                </div>
              </div>

              {/* File Upload */}
              <FileUpload
                label="Upload Policy Document"
                value={formik.values.policyFile}
                error={formik.errors.policyFile}
                onChange={(file) => formik.setFieldValue('policyFile', file)}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductValidity;
