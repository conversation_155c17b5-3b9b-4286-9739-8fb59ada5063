import React, { useEffect, useState } from 'react';
import <PERSON><PERSON>ield from '../Inputs/SelectField';
import InputField from '../Inputs/InputField';
import { Tooltip } from 'react-tooltip';
import { useDispatch, useSelector } from 'react-redux';
import { getBrandsAsync } from '@/store/api/productApi';
import { components } from 'react-select';
import {
  getIndustryTagsAsync,
  onboardingUserAsync,
} from '@/store/api/onboardingApi';
import { useFormikContext } from 'formik';

const ProductIdentity = () => {
  const formik = useFormikContext();
  const dispatch = useDispatch();
  const [brands, setBrands] = useState([]);

  const { onboardingData, franchiseIndustryTags } = useSelector(
    (state) => state.onboarding
  );

  const IndustryTags = onboardingData?.industry_tags?.map((item) => {
    return { value: item.id, label: item.name };
  });
  const transformData = (data) => {
    return data?.map((franchise) => ({
      label: franchise.name, // Parent franchise as label
      options: franchise.sub_franchises?.map((sub) => ({
        value: sub.id, // Convert ID to string
        label: sub.name,
      })),
    }));
  };

  useEffect(() => {
    const getBrands = async () => {
      const { payload } = await dispatch(getBrandsAsync());
      setBrands(payload.data?.map((e) => ({ value: e.id, label: e.name })));
    };
    getBrands();
    dispatch(onboardingUserAsync());
  }, []);

  async function getIndustryTags() {
    const { payload } = await dispatch(getIndustryTagsAsync({ name: 'Hotel' }));
  }

  useEffect(() => {
    const hasHotel =
      formik?.values?.industryTags &&
      formik?.values?.industryTags?.some(
        (tag) => tag.label?.toLowerCase() === 'hotel'
      );
    getIndustryTags();
    if (!hasHotel && formik.values.hotelFranchiseName?.length > 0) {
      formik.setFieldValue('hotelFranchiseName', []);
      formik.setFieldTouched('hotelFranchiseName', false);
    }
  }, [formik.values.industryTags]);

  const franchiseTagsOptions = transformData(franchiseIndustryTags) || [];
  return (
    <>
      {/* Product Identity */}
      <div className="bg-white border border-border-color rounded-xl">
        <div className="flex items-center gap-2 p-4">
          <h2 className="text-sm font-bold">Product Identity</h2>
          <span
            className="icon icon-question text-gray-400 cursor-help"
            data-tooltip-id="product-identity-info"
          />
          <Tooltip
            id="product-identity-info"
            // place="top"
            effect="solid"
            className="!bg-white !opacity-100 !z-50 !text-black !shadow-lg !p-3 !rounded-xl"
          >
            <div className="flex justify-center items-center text-center max-w-[340px] text-dark-500 font-normal text-[13px]">
              <p>
                Add details that define and help identify your product—like
                brand, manufacturer, model number, and keywords. These fields
                help buyers search, recognize, and compare your item more
                easily.
              </p>
            </div>
          </Tooltip>
        </div>

        <div className="space-y-4 p-4">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col mb-0">
              <span className="form-label">
                Industry
              </span>
              <SelectField
                id="industryTags"
                name="industryTags"
                label="Industry Tags"
                className="single-select"
                placeholder={null}
                isMulti={true}
                options={IndustryTags}
                formik={formik}
              />
              {formik.touched.industryTags && formik.errors.industryTags && (
                <div className="text-danger-500 text-xs mt-1">
                  {formik.errors.industryTags}
                </div>
              )}
            </div>
            <div className="flex flex-col mb-0">
              <span className="form-label">
                Brand/ Manufacturer
              </span>
              <SelectField
                id="brandName"
                name="brandName"
                label="Brand/ Manufacturer"
                className="single-select"
                placeholder={null}
                options={brands}
                formik={formik}
              />
              {formik.touched.brandName && formik.errors.brandName && (
                <div className="text-danger-500 text-xs mt-1">
                  {formik.errors.brandName}
                </div>
              )}
            </div>

            <InputField
              id="modelNumber"
              name="modelNumber"
              type="text"
              marginBottom="mb-0"
              label="Manufacturer Part Number"
              placeholder={null}
              formik={formik}
              length={100}
            />

            <div className="flex flex-col mb-4">
              <span className="form-label">Hotel/Franchise Name</span>
              <SelectField
                options={franchiseTagsOptions}
                isMulti={true}
                closeMenuOnSelect={false}
                isSearchable
                // isClearable
                name="hotelFranchiseName"
                className="single-select multi-select"
                placeholder="Select option"
                isDisabled={
                  !Array.isArray(formik.values.industryTags) ||
                  !formik.values.industryTags.some(
                    (e) => e.label?.toLowerCase() === 'hotel'
                  )
                }
                menuPortalClassName="multiselect-menu-portal"
                components={{ Group: components.Group }}
                formik={formik}
              />
              {formik?.errors.hotelFranchiseName &&
                formik?.touched.hotelFranchiseName && (
                  <p className="text-danger-500 text-xs mt-1">
                    {formik?.errors.hotelFranchiseName}
                  </p>
                )}{' '}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductIdentity;
