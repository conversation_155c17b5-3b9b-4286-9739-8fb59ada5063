'use client';

import React from 'react';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import RangeDatePicker from '../Inputs/RangeDatePicker';
import { Form, Formik, useFormikContext } from 'formik';
import {
  PROMOTION_AND_DISCOUNTS_SCHEMA,
  PROMOTION_AND_DISCOUNTS_SCHEMA_WITHOUT_VARIANT,
} from '@/utils/schema';
import { useDispatch, useSelector } from 'react-redux';
import { submitProductPromotions } from '@/store/api/productApi';
import { formatDate } from '../create-product-listing/CreateProduct';
import { ProductType } from '@/utils/constant';

const PromotionDiscounts = ({
  navigationComponent,
  module = ProductType?.RETAIL,
}) => {
  const dispatch = useDispatch();
  const { productId } = useSelector((state) => state.product);
  const { productDetails, productSetDetails } = useSelector(
    (state) => state.product
  );
  const productData =
    module === ProductType?.RETAIL ? productDetails : productSetDetails;
  const variationOptions = (productData?.variations || []).map((e) => ({
    label:
      e?.label +
      (Array.isArray(e?.attributes)
        ? ' ' + e.attributes.map((attr) => attr?.value).join(', ')
        : ''),
    value: e?.id,
  }));

  const allOption = {
    label: 'All',
    value: '__all__', // unique identifier
  };
  return (
    <Formik
      initialValues={{
        hasOffer: productData?.promotions?.is_promotion_applicable
          ? 'Yes'
          : 'No',
        offerType: '' || productData?.promotions?.promotion_type,
        discountName: '' || productData?.promotions?.discount_name,
        discountType: '' || productData?.promotions?.discount_type,
        buyQuantity: '' || productData?.promotions?.min_qty,
        getQuantity: '' || productData?.promotions?.discount,
        promoCode: '' || productData?.promotions?.promo_code,
        selectedVariants: productData?.promotions?.variation_ids || [],
        discountValidityFrom: '' || productData?.promotions?.start_date,
        discountValidityTo: '' || productData?.promotions?.end_date,
        selectedVariants:
          productData?.promotions?.variations?.map((e) => {
            return { label: e.label, value: e.id };
          }) || [],
      }}
      validationSchema={
        variationOptions.length
          ? PROMOTION_AND_DISCOUNTS_SCHEMA
          : PROMOTION_AND_DISCOUNTS_SCHEMA_WITHOUT_VARIANT
      }
      validateOnChange={true}
      validateOnBlur={true}
      enableReinitialize={true}
      onSubmit={async (values) => {
        const {
          hasOffer,
          offerType: promotion_type,
          discountName: discount_name,
          discountType: discount_type,
          buyQuantity: min_qty,
          getQuantity: discount,
          promoCode: promo_code,
          selectedVariants: variation_ids,
          discountValidityFrom,
          discountValidityTo,
          selectedVariants,
        } = values;

        const { payload } = await dispatch(
          submitProductPromotions({
            is_promotion_applicable: hasOffer == 'Yes' ? 1 : 0,
            discount_name,
            min_qty,
            promotion_type,
            discount,
            discount_type,
            promo_code,
            start_date: formatDate(discountValidityFrom),
            end_date: formatDate(discountValidityTo),
            product_id: productId,
            variation_ids: selectedVariants.map((e) => e.value),
          })
        );

        return payload?.ok;
      }}
    >
      {(formik) => {
        return (
          <Form onSubmit={formik.handleSubmit}>
            {navigationComponent &&
              React.cloneElement(navigationComponent, {
                ...navigationComponent.props,
                formik,
              })}
            <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
              <div className="max-w-[1152px] mx-auto px-6 pb-8">
                {/* Promotion Type */}
                <div className="bg-white border border-border-color rounded-xl mb-4">
                  <div className="flex items-center gap-3 p-4">
                    <h2 className="text-base font-semibold">Promotion Type</h2>
                  </div>
                  <div className="space-y-4 p-4 pt-2">
                    <div className="flex flex-col">
                      {/* Offer Toggle */}
                      <div className="flex justify-between items-center">
                        <CustomCheckbox
                          id="hasOffer"
                          name="hasOffer"
                          label="This product has promotions"
                          checked={formik.values.hasOffer === 'Yes'}
                          onChange={(e) => {
                            formik.setFieldValue(
                              'hasOffer',
                              e.target.checked ? 'Yes' : 'No'
                            );
                          }}
                        />
                      </div>

                      {/* Offer Details with smooth transition */}
                      <div
                        className={`transition-all duration-200 ease-in-out ${
                          formik.values.hasOffer === 'Yes'
                            ? 'max-h-[1000px] opacity-100'
                            : 'max-h-0 opacity-0 overflow-hidden'
                        }`}
                      >
                        <div className="mt-6">
                          {/* Discount type */}
                          <div className="flex flex-col gap-4 mb-4 xl:mb-6">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                              {[
                                {
                                  id: 'buy_x_get_y_free',
                                  title: 'Buy X get Y free',
                                  description:
                                    'Buy specific quantity and get free items',
                                  value: 'free',
                                },
                                {
                                  id: 'buy_x_get_discount',
                                  title: 'Buy X get discount of $Y',
                                  description:
                                    'Buy specific quantity and get discount',
                                  value: 'discount',
                                },
                              ].map((type) => (
                                <label
                                  key={type.id}
                                  className={`
                              flex flex-col p-3 lg:p-4 rounded-xl border cursor-pointer relative
                              ${
                                formik.values.offerType === type.value
                                  ? 'border-primary-500'
                                  : 'border-border-color surface hover:border-primary-500 transition-base'
                              }
                            `}
                                >
                                  <div className="flex items-center justify-between gap-2">
                                    <div>
                                      <h3
                                        className={`font-semibold text-sm mb-0.5 text-dark-500 ${
                                          formik.values.offerType === type.value
                                            ? 'text-primary-500'
                                            : ''
                                        }`}
                                      >
                                        {type.title}
                                      </h3>
                                      <p
                                        className={`text-xs text-gray-300 ${
                                          formik.values.offerType === type.value
                                            ? 'text-primary-500'
                                            : ''
                                        }`}
                                      >
                                        {type.description}
                                      </p>
                                    </div>

                                    <div
                                      className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all
                                ${formik.values.offerType === type.value ? 'border-primary-500' : 'border-gray-200'}`}
                                    >
                                      {formik.values.offerType ===
                                        type.value && (
                                        <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                                      )}
                                    </div>
                                    <input
                                      type="radio"
                                      name="offerType"
                                      value={type.value}
                                      checked={
                                        formik.values.offerType === type.value
                                      }
                                      onChange={() =>
                                        formik.setFieldValue(
                                          'offerType',
                                          type.value
                                        )
                                      }
                                      className="hidden"
                                    />
                                  </div>
                                </label>
                              ))}

                              {formik.touched?.offerType &&
                                formik.errors?.offerType && (
                                  <p className="text-danger-500 text-xs mt-1">
                                    {formik.errors?.offerType}
                                  </p>
                                )}
                            </div>
                          </div>

                          {formik.values.offerType === 'discount' && (
                            <div className="flex flex-col mb-4 flex-1">
                              <span
                                className="form-label"
                              >
                                Discount Type
                              </span>
                              <SelectField
                                className="single-select"
                                name="discountType"
                                label="Discount Type"
                                placeholder="Select type"
                                options={[
                                  {
                                    value: 'percentage',
                                    label: 'Percentage Discount',
                                  },
                                  {
                                    value: 'fixed',
                                    label: 'Fixed Amount Discount',
                                  },
                                ]}
                                formik={formik}
                              />
                              {formik?.touched?.discountType &&
                                formik?.errors?.discountType && (
                                  <div className="text-danger-500 text-xs mt-1">
                                    {formik?.errors?.discountType}
                                  </div>
                                )}
                            </div>
                          )}

                          <div className="flex flex-col">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4 mb-0">
                              <div
                                className={`flex-1 ${formik.values.offerType === 'Buy X get discount of $Y' ? 'col-span-1' : 'col-span-2'}`}
                              >
                                <InputField
                                  id="discountName"
                                  name="discountName"
                                  type="text"
                                  label="Discount Name"
                                  placeholder="eg. Super Sumer Sale"
                                  value={formik.values.discountName}
                                  formik={formik}
                                />
                              </div>
                            </div>

                            <div className="flex items-start gap-4 mb-0">
                              <div className="flex-1">
                                <InputField
                                  id="buyQuantity"
                                  name="buyQuantity"
                                  type="number"
                                  label="Buy (X)"
                                  marginBottom="mb-0"
                                  placeholder="Define X amount"
                                  value={formik.values.buyQuantity}
                                  formik={formik}
                                />
                              </div>
                              <div className="flex-1">
                                <InputField
                                  id="getQuantity"
                                  name="getQuantity"
                                  type="number"
                                  label="Get (Y)"
                                  marginBottom="mb-0"
                                  placeholder="Define Y amount"
                                  value={formik.values.getQuantity}
                                  formik={formik}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Validity and Promo Code */}
                {formik.values.hasOffer == 'Yes' && (
                  <div className="bg-white border border-border-color rounded-xl mb-4">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">
                        Validity and Promo Code
                      </h2>
                    </div>
                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 p-4 pt-2">
                      <div>
                        <label className="form-label mb-2 block">
                          Validity
                        </label>
                        <RangeDatePicker
                          placeholder="From - To"
                          onChange={(dates) => {
                            formik.setFieldValue(
                              'discountValidityFrom',
                              dates?.[0] || ''
                            );
                            formik.setFieldValue(
                              'discountValidityTo',
                              dates?.[1] || ''
                            );
                          }}
                          formik={formik}
                          nameFrom="discountValidityFrom"
                          nameTo="discountValidityTo"
                        />
                        {formik.touched.discountValidityFrom &&
                          formik.errors.discountValidityFrom && (
                            <div className="text-danger-500 text-xs mt-1">
                              {formik.errors.discountValidityFrom}
                            </div>
                          )}

                        {formik.touched.discountValidityTo &&
                          formik.errors.discountValidityTo && (
                            <div className="text-danger-500 text-xs mt-1">
                              {formik.errors.discountValidityTo}
                            </div>
                          )}
                      </div>

                      <div className="flex items-center gap-3">
                        <div className="flex-1">
                          <InputField
                            id="promoCode"
                            name="promoCode"
                            type="text"
                            label="Promo Code"
                            marginBottom="mb-0"
                            placeholder={null}
                            value={formik.values.promoCode}
                            formik={formik}
                            generateLabel="Generate code"
                            onGenerateValue={() => {
                              const timestamp = Date.now().toString().slice(-6);
                              const random = Math.random()
                                .toString(36)
                                .substring(2, 5)
                                .toUpperCase();
                              const newPromoCode = `PROMO-${timestamp}-${random}`;
                              formik.setFieldValue('promoCode', newPromoCode);
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Select variants for promotions and discounts */}
                {formik.values.hasOffer == 'Yes' && variationOptions.length ? (
                  <div className="bg-white border border-border-color rounded-xl mb-4 p-4">
                    <div className="flex items-center gap-3">
                      <h2 className="text-base font-semibold">
                        Select variants for promotions and discounts
                      </h2>
                    </div>

                    <SelectField
                      className="single-select multi-select mt-4"
                      menuPortalClassName="multiselect-menu-portal"
                      name="selectedVariants"
                      placeholder="Find and select attributes..."
                      options={[allOption, ...variationOptions]}
                      value={formik.values.selectedVariants}
                      onChange={(selectedOptions) => {
                        if (!Array.isArray(selectedOptions)) return;

                        const isAllSelected = selectedOptions.some(
                          (option) => option.value === '__all__'
                        );

                        if (isAllSelected) {
                          formik.setFieldValue(
                            'selectedVariants',
                            variationOptions
                          );
                        } else {
                          formik.setFieldValue(
                            'selectedVariants',
                            selectedOptions
                          );
                        }
                      }}
                      isSearchable
                      isMulti={true}
                      closeMenuOnSelect={true}
                      defaultMenuIsOpen={false}
                    />
                    {formik.errors?.selectedVariants &&
                      formik.touched?.selectedVariants && (
                        <p className="text-danger-500 text-xs mt-1">
                          {formik.errors?.selectedVariants}
                        </p>
                      )}
                  </div>
                ) : null}
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default PromotionDiscounts;
