'use client';
import React, { useState, useCallback, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { Formik, Form } from 'formik';
import Image from 'next/image';
import RadioButton from '../Inputs/RadioButton';
import <PERSON>Field from '../Inputs/SelectField';
import InputField from '../Inputs/InputField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import BaseModal from '../modals/BaseModal';
import DefineAttributes from './DefineAttributes';
import VariantBuilder from './VariantBuilder';
import * as Yup from 'yup';
import { ATTRIBUES_AND_SKU_SUFFIX_SCHEMA } from '@/utils/schema';
import {
  addAttributeAsync,
  createAttributesVariantsAsync,
  getAttributesAsync,
  getProductDetailsAsync,
} from '@/store/api/productApi';
import { useDispatch, useSelector } from 'react-redux';
import { showWarningToast } from '@/utils/function';

const VariantsConfiguration = ({ navigationComponent }) => {
  const dispatch = useDispatch();
  const { productId, productDetails } = useSelector((state) => state.product);
  const [showNewValueModal, setShowNewValueModal] = useState(false);
  const [attributesList, setAttributesList] = useState([]);
  const [attributeTypes, setAttributeTypes] = useState('all');
  const [formikInstance, setFormikInstance] = useState(null);

  const [uploadedImage, setUploadedImage] = useState(null);
  const [uploadedImageName, setUploadedImageName] = useState(''); // Add this state

  const [currentAttribute, setCurrentAttribute] = useState('');
  const [isVariantBuilderOpen, setIsVariantBuilderOpen] = useState(false);
  const [editVariantId, setEditVariantId] = useState(null);
  const [attributeId, setAttributeId] = useState('');
  const [newValue, setNewValue] = useState('');
  const [productVariantsDetails, setProductVariantsDetails] = useState({});

  const [attributesValue, setAttributesValue] = useState([]);
  const [showConfirmPopup, setShowConfirmPopup] = useState(false);
  const [pendingAttributeChange, setPendingAttributeChange] = useState(null);

  useEffect(() => {
    getAttributes();
  }, [attributeTypes]);
  useEffect(() => {
    getProductDetails();
  }, []);
  async function getAttributes() {
    const { payload } = await dispatch(
      getAttributesAsync({
        category_id: productDetails?.['general-info']?.category?.id,
        type: attributeTypes,
      })
    );
    const AttributeData = payload?.data?.map((attr) => ({
      name: attr.attribute_name,
      id: attr.id,
      type: attr.type,
      options: attr.attribute_values.map((val) => {
        return {
          color_code: val.type_value,
          value: val.attribute_values,
          id: val.id,
        };
      }),
    }));

    setAttributesList(AttributeData);
  }

  async function getProductDetails() {
    setProductVariantsDetails(productDetails);
    setAttributesValue(
      productDetails?.attributes?.reduce((acc, curr) => {
        const exists = acc.find((item) => item.value === curr.attribute?.id);
        if (!exists) {
          acc.push({
            label: curr.attribute?.name || '',
            value: curr.attribute?.id,
          });
        }
        return acc;
      }, []) || []
    );
  }
  const handleAddNewValue = (attributeName, attributeid, formik) => {
    setCurrentAttribute(attributeName);
    setFormikInstance(formik);
    setShowNewValueModal(true);
    setAttributeId(attributeid);
  };

  const getValidationSchema = (currentAttribute) => {
    let baseSchema = {
      label: Yup.string().required('Label is required'),
    };

    if (currentAttribute.toLowerCase() === 'color') {
      baseSchema.color_code = Yup.string()
        .matches(/^#[0-9A-Fa-f]{6}$/, 'Enter a valid hex code')
        .required('Color code is required');
    } else if (currentAttribute.toLowerCase() === 'image') {
      baseSchema.image = Yup.mixed().required('Image is required');
    }

    return Yup.object().shape(baseSchema);
  };

  // List of fixed keys to exclude
  const fixedKeys = ['id', 'sku', 'upc', 'images', 'newId'];

  const handleRemoveVariation = (index, formik) => {
    const updated = [...formik.values.productVariants];
    updated.splice(index, 1);
    formik.setFieldValue('productVariants', updated);
  };

  const attributeOptions = [
    { label: 'Required', value: 'Required' },
    { label: 'Recommended', value: 'Recommended' },
    { label: 'All Attributes', value: 'all' },
  ];
  return (
    <Formik
      initialValues={{
        attributeType: productVariantsDetails?.attribute_type || attributeTypes,
        hasVariations:
          productVariantsDetails?.is_variation === true ? true : false,
        // attributes: attributeTypes,
        singleProductSku: productVariantsDetails?.variations?.[0]?.label || '',
        singleProductUpc: productVariantsDetails?.variations?.[0]?.code || '',
        selectedAttributes:
          productVariantsDetails?.attributes?.reduce((acc, curr) => {
            const exists = acc.find(
              (item) => item.value === curr.attribute?.id
            );
            if (!exists) {
              acc.push({
                label: curr.attribute?.name || '',
                value: curr.attribute?.id,
              });
            }
            return acc;
          }, []) || [],
        selectedVariantsAttributes:
          productVariantsDetails?.attributes?.reduce((acc, curr) => {
            const exists = acc.find(
              (item) => item.value === curr.attribute?.id
            );
            if (!exists) {
              acc.push({
                label: curr.attribute?.name || '',
                value: curr.attribute?.id,
              });
            }
            return acc;
          }, []) || [],
        selectedAttributesValues: productVariantsDetails?.attributes?.reduce(
          (acc, curr) => {
            const existing = acc.find(
              (item) => item.name?.value === curr.attribute?.id
            );
            const attributeValue = {
              label: curr.attributeValue?.name || '',
              value: curr.attributeValue?.id,
            };

            if (existing) {
              existing.values.push(attributeValue);
            } else {
              acc.push({
                name: {
                  label: curr.attribute?.name || '',
                  value: curr.attribute?.id,
                },
                values: [attributeValue],
              });
            }

            return acc;
          },
          []
        ),
        skuSuffix: productVariantsDetails?.sku_suffix || '',
        productVariants:
          productVariantsDetails?.variations?.map((data, index) => {
            return {
              id: index + 1,
              newId: data.id,
              upc: data.code || '',
              sku: data.label || '',
              images: data.media?.map((m) => m.url) || [],
              ...Object.fromEntries(
                (data.attributes || []).map((attr) => [attr.id, attr.value_id])
              ),
            };
          }) || [],
      }}
      enableReinitialize={true}
      validationSchema={ATTRIBUES_AND_SKU_SUFFIX_SCHEMA} // Generate the schema dynamically
      onSubmit={async (values) => {
        let finalPayload;
        if (values?.hasVariations && values?.productVariants?.length === 0) {
          showWarningToast('Please add the product variants');
        } else {
          finalPayload = {
            attribute_type: values?.attributeType,
            productId: productId,
            attributes: values.selectedAttributesValues.map((data) => ({
              id: data.name.value,
              values: data.values.map((v) => ({ id: v.value })),
            })),
            is_variation: values?.hasVariations ? 1 : 0,
            selected_attributes: values?.selectedVariantsAttributes?.map(
              (data) => data?.value
            ),
            sku_suffix: values?.hasVariations ? values?.skuSuffix : '',
            variations: values?.hasVariations
              ? values?.productVariants?.map((variant) => ({
                  // id: variant.id,
                  is_default: values?.productVariants.length === 1 ? 1 : 0,
                  id: variant.newId,
                  sku: variant.sku,
                  code: variant.upc,
                  images: variant.images,
                  attributes: Object.entries(variant)
                    .filter(([key]) =>
                      values?.selectedVariantsAttributes?.some(
                        (attr) => attr.value === key
                      )
                    )
                    .map(([attribute_id, value_id]) => ({
                      attribute_id,
                      value_id,
                    })),
                }))
              : [
                  {
                    id: values?.productVariants?.[0]?.newId,
                    sku: values?.singleProductSku || '',
                    code: values?.singleProductUpc || '',
                    is_default: 1,
                  },
                ],
          };
        }
        let { payload } = await dispatch(
          createAttributesVariantsAsync(finalPayload)
        );

        return payload.status || payload.ok;
        // Handle form submission here
      }}
    >
      {(formik) => {
        return (
          <>
            <Form onSubmit={formik.handleSubmit}>
              {navigationComponent &&
                React.cloneElement(navigationComponent, {
                  ...navigationComponent.props,
                  formik,
                })}
              <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
                <div className="max-w-[1152px] mx-auto px-6 pb-8">
                  <div className="card !p-0 mb-4">
                    <div className="flex items-center justify-between gap-2 p-4">
                      <h2 className="text-sm font-semibold">Attributes</h2>

                      <div className="flex items-center gap-x-4">
                        <RadioButton
                          values={attributeOptions}
                          name="attributeType"
                          formik={formik}
                          onChange={(value) => {
                            if (
                              formik?.values?.hasVariations &&
                              formik.values.productVariants?.length > 0
                            ) {
                              setPendingAttributeChange(value);
                              setShowConfirmPopup(true);
                            } else {
                              formik.setTouched({}); // ✅ Clear all touched
                              formik.setErrors({});
                              formik.setValues({
                                ...formik.values,
                                attributeType: value,
                                selectedAttributes: [],
                                selectedVariantsAttributes: [],
                                selectedAttributesValues: [],
                                productVariants: [],
                                hasVariations: false,
                                singleProductSku: '',
                                singleProductUpc: '',
                                skuSuffix: '', // Optional: Reset if SKU suffix depends on attributes
                              });

                              setAttributeTypes(value);
                            }
                          }}
                          value={formik.values.attributeType}
                        />
                      </div>
                    </div>

                    <div className="space-y-4 p-4 pt-2">
                      <DefineAttributes
                        formik={formik}
                        attributesList={attributesList}
                        handleAddNewValue={handleAddNewValue}
                        setAttributesValue={setAttributesValue}
                        setShowConfirmPopup={setShowConfirmPopup}
                        setPendingAttributeChange={setPendingAttributeChange}
                      />
                    </div>
                  </div>
                  {/* Create Variants Field */}
                  {formik?.values?.selectedAttributes?.length > 0 && (
                    <div className="card !p-0">
                      <div className="flex items-center justify-between p-4">
                        <h4 className="text-sm font-semibold">
                          Create Variants
                        </h4>
                        {formik?.values?.productVariants?.length > 0 &&
                          formik?.values?.skuSuffix && (
                            <button
                              type="button"
                              className="inline-flex items-center gap-2 btn btn-outline-gray"
                              onClick={() => setIsVariantBuilderOpen(true)}
                            >
                              <span className="icon icon-plus" />
                              Add variants
                            </button>
                          )}
                      </div>
                      <div className="p-4 pt-2 space-y-4 xl:space-y-6">
                        <CustomCheckbox
                          id="hasVariations"
                          name="hasVariations"
                          label="This product has variations"
                          checked={formik.values.hasVariations}
                          onChange={(e) => {
                            // If user is trying to uncheck and variants exist, prevent and warn
                            if (
                              !e.target.checked &&
                              formik.values.productVariants.length > 0
                            ) {
                              showWarningToast(
                                'You have added variations. Please remove them first if not needed.'
                              );
                              return;
                            }

                            // Validate selected attributes and values
                            const isAttributesValid =
                              !formik.values.selectedAttributes.every(
                                (attr) => {
                                  const entry =
                                    formik.values.selectedAttributesValues.find(
                                      (v) => v.name.value === attr.value
                                    );
                                  return !(entry && entry.values.length > 0);
                                }
                              );

                            if (!isAttributesValid) {
                              showWarningToast(
                                'Please select attribute values'
                              );
                              return;
                            }

                            // Set field value
                            formik.setFieldValue(
                              'hasVariations',
                              e.target.checked
                            );
                          }}
                        />

                        {formik?.values?.hasVariations === false && (
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <InputField
                              type="text"
                              label="SKU"
                              id="singleProductSku"
                              name="singleProductSku"
                              marginBottom="mb-0"
                              value={formik.values.singleProductSku}
                              generateLabel="Generate code"
                              className="flex-1"
                              formik={formik}
                              onGenerateValue={() => {
                                const timestamp = Date.now()
                                  .toString()
                                  .slice(-6);
                                const random = Math.random()
                                  .toString(36)
                                  .substring(2, 5)
                                  .toUpperCase();
                                const newSku = `COA-VDV066-${timestamp}-${random}`;
                                formik.setFieldValue(
                                  'singleProductSku',
                                  newSku
                                );
                              }}
                              readOnly
                            />
                            <InputField
                              type="text"
                              id="singleProductUpc"
                              name="singleProductUpc"
                              marginBottom="mb-0"
                              label="Universal Product Code"
                              formik={formik}
                              // {...formik.getFieldProps('singleProductUpc')}
                              value={formik?.values?.singleProductUpc}
                            />
                          </div>
                        )}
                        {formik.values.hasVariations && (
                          <div className="flex items-start flex-col xl:flex-row gap-4">
                            <div className="flex flex-col w-full xl:flex-1">
                              <span className="form-label">
                                Select attributes for variant setup
                              </span>
                              <SelectField
                                isMulti
                                className="single-select"
                                name="selectedVariantsAttributes"
                                placeholder="Select attribute"
                                options={formik.values.selectedAttributes
                                  .map((attr) => {
                                    const entry =
                                      formik.values.selectedAttributesValues.find(
                                        (v) => v.name.value === attr.value
                                      );
                                    return entry && entry.values.length
                                      ? { label: attr.label, value: attr.value }
                                      : null;
                                  })
                                  .filter(Boolean)}
                                value={formik.values.selectedVariantsAttributes}
                                onChange={(newSelectedVariants) => {
                                  formik.setFieldValue(
                                    'selectedVariantsAttributes',
                                    newSelectedVariants
                                  );

                                  setAttributesValue(newSelectedVariants);
                                  const fixedKeys = [
                                    'id',
                                    'sku',
                                    'upc',
                                    'images',
                                    'newId',
                                  ];
                                  const updatedVariants =
                                    formik.values.productVariants.map(
                                      (variant) => {
                                        const copy = { ...variant };
                                        Object.keys(copy).forEach((key) => {
                                          if (
                                            !fixedKeys.includes(key) &&
                                            !newSelectedVariants.some(
                                              (a) => a.value === key
                                            )
                                          ) {
                                            delete copy[key];
                                          }
                                        });
                                        return copy;
                                      }
                                    );
                                  formik.setFieldValue(
                                    'productVariants',
                                    updatedVariants
                                  );
                                }}
                              />

                              {formik.touched.selectedVariantsAttributes &&
                                formik.errors.selectedVariantsAttributes && (
                                  <span className="text-danger-500 text-xs mt-1">
                                    {formik.errors.selectedVariantsAttributes}
                                  </span>
                                )}
                            </div>

                            <div>
                              <span className="flex items-start justify-between gap-4 flex-1 w-full xl:flex-[0_0_324px]">
                                <InputField
                                  type="text"
                                  label="SKU Suffix"
                                  id="skuSuffix"
                                  name="skuSuffix"
                                  marginBottom="mb-0 w-full"
                                  placeholder="24-BM04"
                                  {...formik.getFieldProps('skuSuffix')}
                                />
                              </span>
                              {formik.touched.skuSuffix &&
                                formik.errors.skuSuffix && (
                                  <span className="text-danger-500 text-xs mt-1">
                                    {formik.errors.skuSuffix}
                                  </span>
                                )}
                            </div>
                          </div>
                        )}
                        {formik?.values?.productVariants?.length === 0 ? (
                          <div className="p-4">
                            {formik?.values?.skuSuffix &&
                              formik?.values?.selectedVariantsAttributes
                                ?.length > 0 &&
                              formik?.values?.hasVariations && (
                                <button
                                  type="button"
                                  onClick={() => setIsVariantBuilderOpen(true)}
                                  className="flex items-center justify-center w-full border-2 border-dashed border-border-color hover:border-dark-500/10 hover:bg-dark-500/5 rounded-lg py-6 2xl:py-[75px] px-8 transition-base"
                                >
                                  <span className="inline-flex items-center gap-1 text-sm font-medium text-gray-700">
                                    <span className="icon icon-plus" />
                                    Add Variation
                                  </span>
                                </button>
                              )}
                          </div>
                        ) : (
                          formik?.values?.hasVariations &&
                          formik?.values?.productVariants?.map(
                            (data, index) => (
                              <div className="space-y-4">
                                <div
                                  key={index}
                                  className="card !p-0 overflow-hidden"
                                >
                                  <div className="flex items-center justify-between p-4">
                                    <div className="flex items-center gap-3">
                                      <span className="text-base font-semibold">
                                        SKU: {data?.sku}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-3">
                                      <button
                                        className="btn btn-outline-gray"
                                        onClick={(e) => {
                                          e?.preventDefault();
                                          setEditVariantId(data.id);
                                          setIsVariantBuilderOpen(true);
                                        }}
                                      >
                                        Edit
                                      </button>

                                      <button
                                        className="btn btn-outline-danger w-8 h-8 !p-0 inline-flex items-center justify-center"
                                        onClick={() =>
                                          handleRemoveVariation(index, formik)
                                        }
                                      >
                                        <span className="icon icon-trash text-base" />
                                      </button>
                                    </div>
                                  </div>

                                  <div className="flex xl:items-center justify-between flex-col xl:flex-row gap-4 p-4">
                                    <div className="flex flex-col gap-2 xl:gap-6">
                                      <div className="">
                                        <div className="text-xs text-gray-400 mb-1">
                                          Universal Product Code
                                        </div>
                                        <div className="text-[13px] font-semibold">
                                          {data?.upc || '-'}
                                        </div>
                                      </div>
                                      <div className="">
                                        <div className="text-xs text-gray-400 mb-1">
                                          Attributes Combination
                                        </div>

                                        <ul className="flex gap-2 flex-wrap text-xs text-gray-500">
                                          {Object.entries(data).filter(
                                            ([key]) =>
                                              !fixedKeys.includes(key) &&
                                              formik.values.selectedVariantsAttributes?.some(
                                                (attr) => attr.value === key
                                              )
                                          ).length > 0 ? (
                                            Object.entries(data)
                                              .filter(
                                                ([key]) =>
                                                  !fixedKeys.includes(key) &&
                                                  formik.values.selectedVariantsAttributes?.some(
                                                    (attr) => attr.value === key
                                                  )
                                              )
                                              .map(
                                                (
                                                  [attrId, selectedValueId],
                                                  index,
                                                  array
                                                ) => {
                                                  const attribute =
                                                    formik?.values?.selectedAttributesValues?.find(
                                                      (attr) =>
                                                        attr?.name?.value ===
                                                        attrId
                                                    );

                                                  const attributeLabel =
                                                    attribute?.name?.label ||
                                                    attrId;
                                                  const valueLabel =
                                                    attribute?.values?.find(
                                                      (val) =>
                                                        val.value ===
                                                        selectedValueId
                                                    )?.label || selectedValueId;

                                                  return (
                                                    <li
                                                      key={index}
                                                      className={`text-[13px] font-semibold relative ${
                                                        index !==
                                                        array.length - 1
                                                          ? 'after:content-[""] after:absolute after:right-0 after:top-1/2 after:h-3 after:w-[1px] after:bg-gray-100 after:-translate-y-1/2 pr-3'
                                                          : ''
                                                      }`}
                                                    >
                                                      {attributeLabel}:{' '}
                                                      {valueLabel}
                                                    </li>
                                                  );
                                                }
                                              )
                                          ) : (
                                            <li className="text-[13px] font-semibold">
                                              -
                                            </li>
                                          )}
                                        </ul>
                                      </div>
                                    </div>

                                    <div className="grid grid-cols-5 gap-2">
                                      {data?.images?.map((image, index) => (
                                        <Image
                                          key={index}
                                          src={image?.preview}
                                          width={120}
                                          height={120}
                                          alt="Product variant"
                                          className="object-cover rounded-xl"
                                        />
                                      ))}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            )
                          )
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Form>
            {/* OffCanvas modal */}
            <BaseOffCanvas
              isOpen={isVariantBuilderOpen}
              onClose={() => {
                setIsVariantBuilderOpen(false);
                setEditVariantId(null);
              }}
              title="Create Variants"
              size="xl"
            >
              <VariantBuilder
                onClose={() => {
                  setIsVariantBuilderOpen(false);
                  setEditVariantId(null);
                }}
                editVariantId={editVariantId}
                setEditVariantId={setEditVariantId}
                outerFormik={formik}
                attributesValue={attributesValue}
              />
            </BaseOffCanvas>

            {/* Add new value modal */}
            <Formik
              initialValues={{
                label: '',
                color_code: '',
                image: '',
              }}
              validationSchema={getValidationSchema(currentAttribute)}
              validateOnChange={true}
              validateOnBlur={true}
              enableReinitialize={true}
              onSubmit={async (values) => {
                const { payload } = await dispatch(
                  addAttributeAsync({
                    name: values.label,
                    value: values.color_code || values.image,
                    attribute_id: attributeId,
                    type: currentAttribute.toLowerCase(),
                  })
                );
                if (payload.status || payload.ok) {
                  getAttributes();
                }
                setShowNewValueModal(false);
              }}
            >
              {(formik) => {
                const onDrop = useCallback((acceptedFiles) => {
                  const file = acceptedFiles[0];
                  formik.setFieldValue('image', file);
                  setUploadedImage(URL.createObjectURL(file));
                  setUploadedImageName(file.name); // Store the file name
                }, []);
                const { getRootProps, getInputProps, isDragActive } =
                  useDropzone({
                    onDrop,
                    accept: {
                      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.svg'],
                    },
                    maxFiles: 1,
                  });

                return (
                  <Form onSubmit={formik.handleSubmit}>
                    <BaseModal
                      isOpen={showNewValueModal}
                      onClose={() => {
                        formik?.setErrors({});
                        formik?.handleReset();
                        setShowNewValueModal(false);
                        setNewValue('');
                      }}
                      size="md"
                      title="Add new attribute value"
                      paragraph={null}
                    >
                      <div className="flex flex-col w-full">
                        <div className="p-4">
                          <p className="mb-6 text-sm text-gray-500 font-medium">
                            This value will be added to the selected attribute
                            for this product. You can reuse it later for similar
                            listings.
                          </p>

                          <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <InputField
                              type="text"
                              placeholder="Enter new value"
                              label="Label"
                              marginBottom="mb-0"
                              name="label"
                              id="label"
                              autoComplete="off"
                              formik={formik}
                            />

                            {currentAttribute.toLowerCase() === 'color' && (
                              <div>
                                <label className="form-label mb-2">
                                  Color Code
                                </label>
                                <div className="relative">
                                  {/* Color Picker */}
                                  <div className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 rounded-lg border border-border-color overflow-hidden">
                                    <input
                                      type="color"
                                      value={formik.values.color_code}
                                      className="absolute inset-0 w-7 h-7 -left-1 -top-1 cursor-pointer"
                                      onChange={(e) =>
                                        formik.setFieldValue(
                                          'color_code',
                                          e.target.value
                                        )
                                      }
                                    />
                                  </div>

                                  {/* Text Input */}
                                  <input
                                    type="text"
                                    name="color_code"
                                    className={`form-control !pl-10 ${
                                      formik.touched.color_code &&
                                      formik.errors.color_code
                                        ? 'border-red-500'
                                        : ''
                                    }`}
                                    placeholder="#000000"
                                    value={formik.values.color_code}
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      if (value.length <= 7) {
                                        formik.setFieldValue(
                                          'color_code',
                                          value
                                        );
                                      }
                                    }}
                                  />

                                  {/* Validation Error */}
                                </div>
                                {formik.touched.color_code &&
                                  formik.errors.color_code && (
                                    <span className="text-danger-500 text-xs mt-1">
                                      {formik.errors.color_code}
                                    </span>
                                  )}
                              </div>
                            )}

                            {/* Image upload */}
                            {currentAttribute.toLowerCase() == 'image' && (
                              <div className="flex flex-col">
                                <label className="form-label mb-2">Image</label>
                                <div
                                  {...getRootProps()}
                                  className={`inline-flex ${
                                    isDragActive ? 'bg-gray-50' : ''
                                  }`}
                                >
                                  <input {...getInputProps()} />
                                  {uploadedImage ? (
                                    <div className="flex items-center justify-between gap-2 border border-border-color rounded-lg py-2 px-4 w-full">
                                      <div className="flex items-center gap-2">
                                        <div className="w-10 h-10 overflow-hidden aspect-square border border-border-color rounded-full">
                                          <Image
                                            src={uploadedImage}
                                            name="image"
                                            id="image"
                                            alt="Uploaded preview"
                                            width={100}
                                            height={100}
                                            className="object-cover h-full"
                                          />
                                        </div>

                                        <span className="text-sm text-gray-500 truncate max-w-[200px] ellipsis">
                                          {uploadedImageName}
                                        </span>
                                      </div>
                                      <button
                                        type="button"
                                        className="flex items-center justify-center h-8 w-8 text-danger-500 border border-border-color hover:bg-danger-500 hover:text-white hover:border-danger-500 transition-base shadow-[0px_1px_0px_0px_#1A1A1A12] rounded-lg cursor-pointer"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          setUploadedImage(null);
                                          setUploadedImageName('');
                                        }}
                                      >
                                        <span className="icon icon-trash text-lg" />
                                      </button>
                                    </div>
                                  ) : (
                                    <div className="inline-flex text-[13px] border border-border-color rounded-lg py-1.5 px-4 text-center cursor-pointer transition-base">
                                      Select Image
                                    </div>
                                  )}
                                </div>

                                {formik.touched.image &&
                                  formik.errors.image && (
                                    <span className="text-danger-500 text-xs mt-1">
                                      {formik.errors.image}
                                    </span>
                                  )}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex justify-end gap-3 border-t border-border-color p-4">
                          <button
                            className="btn btn-outline-gray"
                            onClick={() => {
                              formik?.setErrors({});
                              formik?.handleReset();
                              setShowNewValueModal(false);
                              setNewValue('');
                            }}
                          >
                            Cancel
                          </button>
                          <button
                            className="btn"
                            type="submit"
                            // onClick={handleSaveNewValue}
                          >
                            Add Value
                          </button>
                        </div>
                      </div>
                    </BaseModal>
                  </Form>
                );
              }}
            </Formik>

            <BaseModal
              isOpen={showConfirmPopup}
              onClose={() => {
                formik?.setErrors({});
                setShowConfirmPopup(false);
                setPendingAttributeChange(null);
              }}
              size="md"
              title="Remove Attribute & Variant Configurations?"
              paragraph={null}
            >
              <div className="flex flex-col w-full">
                <div className="p-4">
                  <p className="mb-6 text-sm text-gray-500 font-medium">
                    If you changing the attribute type or remove attributes, it
                    will delete your existing variant configurations.
                  </p>
                </div>

                <div className="flex justify-end gap-3 border-t border-border-color p-4">
                  <button
                    className="btn btn-outline-gray"
                    onClick={() => {
                      formik?.setErrors({});
                      setShowConfirmPopup(false);
                      setPendingAttributeChange(null);
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn"
                    onClick={() => {
                      formik.setTouched({}); // ✅ Clear all touched
                      formik.setErrors({});
                      formik.setValues({
                        ...formik.values,
                        attributeType: pendingAttributeChange,
                        selectedAttributes: [],
                        selectedVariantsAttributes: [],
                        selectedAttributesValues: [],
                        productVariants: [],
                        hasVariations: false,
                        singleProductSku: '',
                        singleProductUpc: '',
                        skuSuffix: '', // Optional: Reset if SKU suffix depends on attributes
                      });
                      setAttributeTypes(pendingAttributeChange);
                      setShowConfirmPopup(false);
                      setPendingAttributeChange(null);
                    }}
                  >
                    Yes, Continue
                  </button>
                </div>
              </div>
            </BaseModal>
          </>
        );
      }}
    </Formik>
  );
};

export default VariantsConfiguration;
