import React from 'react';

const ProductStepper = ({ steps, activeStep, completedSteps = [], onStepClick, pendingSections }) => {
  return (
    <div className="flex flex-col gap-y-1">
      {steps.map((step, index) => {
        const isPending = pendingSections?.includes(index);
        const isActive = activeStep === index;
        const isCompleted = completedSteps?.includes(index);

        return (
          <div className='relative' key={index}>
            <button
              type="button"
              onClick={() => onStepClick(index)}
              className={`flex items-center w-full gap-2 py-2 pl-7 pr-2 text-sm font-medium text-left transition-all
                ${
                  isPending
                    ? 'text-danger-500'
                    : isActive
                    ? 'text-dark-500'
                    : isCompleted
                    ? 'text-success-500'
                    : 'text-gray-400'
                }`}
            >
              <div className={`absolute left-0 w-4 h-4 border-3 rounded-full z-[1]
                ${
                  isPending
                    ? ' border-danger-500'
                    : isActive
                    ? 'border-dark-500 bg-white'
                    : isCompleted
                    ? 'border-success-500 bg-white'
                    : 'bg-gray-gradient !border-0'
                }`}
              />
              {step.title}
            </button>
            {index < steps?.length - 1 && (
              <div className="absolute left-[7px] top-[20px] w-[1px] h-full bg-gray-100"/>
            )}
          </div>
        );
      })}
    </div>
  );
};


export default ProductStepper;