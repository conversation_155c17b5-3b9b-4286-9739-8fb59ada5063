'use client';
import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import FileUpload from '../Inputs/FileUpload';
import { PRODUCT_DESCRIPTION_SCHEMA } from '@/utils/schema';
import { Form, Formik } from 'formik';
import { submitProductDescription } from '@/store/api/productApi';
import { useDispatch, useSelector } from 'react-redux';
import { ProductType } from '@/utils/constant';

function ProductDescription({
  navigationComponent,
  module = ProductType?.RETAIL,
}) {
  const dispatch = useDispatch();
  const { productId } = useSelector((state) => state.product);
  const { productDetails, productSetDetails } = useSelector(
    (state) => state.product
  );
  const productData =
    module === ProductType?.RETAIL ? productDetails : productSetDetails;
  return (
    <Formik
      initialValues={{
        productDescription: '' || productData?.description?.description,
        keyHighlights: productData?.description?.key_highlights?.length
          ? productData?.description?.key_highlights
          : [''],
        productDocument: '',
      }}
      validationSchema={PRODUCT_DESCRIPTION_SCHEMA}
      validateOnChange={true}
      validateOnBlur={true}
      enableReinitialize={true}
      onSubmit={async (values) => {
        const {
          productDescription: description,
          keyHighlights: key_highlights,
          productDocument: specifications,
        } = values;

        const { payload } = await dispatch(
          submitProductDescription({
            description,
            key_highlights,
            specifications,
            // replace_specifications,
            product_id: productId,
          })
        );

        return payload?.ok;
      }}
    >
      {(formik) => {
        // Inside the Formik render function
        useEffect(() => {
          formik.validateField('keyHighlights');
        }, [formik.values.keyHighlights]);

        const handleHighlightChange = (index, value) => {
          const newHighlights = [...formik.values.keyHighlights];
          newHighlights[index] = value;
          formik.setFieldValue('keyHighlights', newHighlights);
          formik.setFieldTouched(`keyHighlights[${index}]`, true, false);
        };

        const handleAddHighlight = () => {
          const newHighlights = [...formik.values.keyHighlights, ''];
          formik.setFieldValue('keyHighlights', newHighlights);
        };

        const handleRemoveHighlight = (index) => {
          const newHighlights = formik.values.keyHighlights.filter(
            (_, i) => i !== index
          );
          formik.setFieldValue('keyHighlights', newHighlights);

          const newTouched = { ...formik.touched.keyHighlights };
          delete newTouched[index];
          formik.setTouched(
            { ...formik.touched, keyHighlights: newTouched },
            false
          );
        };

        return (
          <Form onSubmit={formik.handleSubmit}>
            {navigationComponent &&
              React.cloneElement(navigationComponent, {
                ...navigationComponent.props,
                formik,
              })}

            <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
              <div className="max-w-[1152px] mx-auto px-6 pb-8">
                <div className="bg-white border border-border-color rounded-xl">
                  <div className="flex items-center justify-between gap-3 p-4">
                    <h2 className="font-bold">Product Description</h2>
                  </div>
                  <div className="space-y-4 xl:space-y-6 p-4 pt-2">
                    {/* Product Description */}
                    <div className="flex flex-col">
                      <div className="flex justify-between items-center gap-2 mb-1">
                        <label
                          htmlFor="productDescription"
                          className="form-label mb-0"
                        >
                          Product Description
                        </label>
                        <button
                          type="button"
                          className="inline-flex items-center gap-2 text-xs font-bold cursor-pointer text-primary-500 transition-base text-gradient"
                        >
                          {/* <span className="icon icon-magic-wand text-lg align-middle" /> */}
                          <Image
                            src="/images/ai-icon.svg"
                            alt="AI Icon"
                            width={15}
                            height={15}
                          />
                          Generate text
                        </button>
                      </div>
                      <textarea
                        id="productDescription"
                        name="productDescription"
                        // placeholder="Enter product description"
                        rows="8"
                        maxLength={5000}
                        className={`form-control ${
                          formik.touched?.productDescription &&
                          formik.errors?.productDescription
                            ? 'border-red-500'
                            : ''
                        }`}
                        value={formik.values.productDescription || ''}
                        onChange={formik.handleChange}
                      />
                      {formik.touched?.productDescription &&
                        formik.errors?.productDescription && (
                          <p className="text-danger-500 text-xs mt-1">
                            {formik.errors?.productDescription}
                          </p>
                        )}
                    </div>

                    {/* Key Highlights Section */}
                    <div className="flex flex-col">
                      <div className="flex justify-between items-center">
                        <span className="form-label mb-0">Key Highlights</span>
                        <button
                          type="button"
                          className="inline-flex items-center gap-1 text-primary-500 text-xs font-semibold hover:text-primary-600"
                          onClick={handleAddHighlight}
                        >
                          <span className="icon icon-plus text-sm" />
                          Add more
                        </button>
                      </div>

                      <div
                        className={`flex flex-col gap-1 ${formik.values.keyHighlights.length > 0 ? 'mt-2' : ''}`}
                      >
                        {formik.values.keyHighlights.map((highlight, index) => (
                          <React.Fragment key={index}>
                            <div
                              key={index}
                              className="flex items-center gap-2"
                            >
                              <input
                                type="text"
                                name={`keyHighlights[${index}]`}
                                className={`form-control flex-1 ${
                                  formik.errors.keyHighlights?.[index] &&
                                  formik.touched.keyHighlights?.[index]
                                    ? 'border-red-500'
                                    : ''
                                }`}
                                placeholder="Enter product highlight"
                                value={highlight}
                                onChange={(e) => {
                                  // handleHighlightChange(index, e.target.value);

                                  const value = e.target.value;
                                  formik.setFieldValue(
                                    `keyHighlights[${index}]`,
                                    value
                                  ); // ✅ correct value update
                                  formik.setFieldTouched(
                                    `keyHighlights[${index}]`,
                                    true,
                                    false
                                  );
                                }}
                              />
                              <button
                                type="button"
                                onClick={() => handleRemoveHighlight(index)}
                                className="text-danger-500 hover:text-danger-600 hover:bg-danger-500/10 w-8 h-8 flex items-center justify-center rounded-md cursor-pointer transition-base"
                              >
                                <span className="icon icon-trash text-lg align-middle" />
                              </button>
                            </div>
                            {formik.errors.keyHighlights &&
                              formik.touched.keyHighlights &&
                              formik.errors.keyHighlights[index] &&
                              formik.touched.keyHighlights[index] && (
                                <p className="text-danger-500 text-xs mt-1">
                                  {formik.errors.keyHighlights[index]}
                                </p>
                              )}
                          </React.Fragment>
                        ))}
                        {/* Group-level error display (optional) */}
                      </div>
                    </div>

                    {/* File Upload */}
                    <FileUpload
                      label="Product Specifications"
                      name="productDocument"
                      tooltipContent="Upload technical documents, specification sheets, or detailed product information that helps buyers understand key materials, dimensions, compliance, or certifications of your product."
                      accept=".pdf"
                      value={formik.values.productDocument}
                      formik={formik}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}

export default ProductDescription;
