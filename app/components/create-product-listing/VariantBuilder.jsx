import React, { useEffect, useState } from 'react';
import { Formik, Form, FieldArray } from 'formik';
import InputField from '../Inputs/InputField';
import ImageUpload from '../Inputs/ImageUpload';
import { VARIANTS_CONFIGURATION_ADD_SCHEMA } from '@/utils/schema';
import SelectField from '../Inputs/SelectField';
import { showSuccessToast, showWarningToast } from '@/utils/function';

const VariantBuilder = ({
  onClose,
  outerFormik,
  attributesValue,
  editVariantId,
  setEditVariantId,
}) => {
  const [skuSuffix, setSkuSuffix] = useState(
    outerFormik?.values?.skuSuffix || ''
  ); // Track SKU suffix locally

  useEffect(() => {
    // Update skuSuffix when outerFormik value changes
    setSkuSuffix(outerFormik?.values?.skuSuffix || '');
  }, [outerFormik?.values?.skuSuffix]);

  const handleAddVariation = (values, formik) => {
    const newVariation = {
      id: values.selectedVariantsAttributes.length + 1,
      upc: '',
      sku: `COA-VDV066-${outerFormik?.values?.skuSuffix}`,
      images: [],
      ...Object.fromEntries(
        (outerFormik?.values?.selectedVariantsAttributes || []).map((attr) => [
          attr.value,
          '',
        ])
      ),
    };

    const newVariations = [...values.selectedVariantsAttributes, newVariation];
    formik.setFieldValue('selectedVariantsAttributes', newVariations);
  };

  const handleRemoveVariation = (index, formik) => {
    const updated = [...formik.values.selectedVariantsAttributes];
    updated.splice(index, 1);
    formik.setFieldValue('selectedVariantsAttributes', updated);
  };

  // Get the selected variant based on the editVariantId
  const selectedVariant = outerFormik.values.productVariants.find(
    (variant) => variant.id === editVariantId
  );

  const generateVariantWithAttributes = (variant, selectedAttrs) => {
    const base = {
      ...variant,
    };

    selectedAttrs?.forEach((attr) => {
      if (!(attr.value in base)) {
        base[attr.value] = '';
      }
    });

    return base;
  };

  return (
    <Formik
      initialValues={{
        selectedVariantsAttributes: editVariantId
          ? [
              generateVariantWithAttributes(
                selectedVariant,
                outerFormik.values.selectedVariantsAttributes
              ),
            ] // If editing, show the selected variant only
          : [
              {
                id: outerFormik.values.productVariants.length + 1,
                upc: '',
                sku: `COA-VDV066-${skuSuffix || ''}`,
                images: [],
                ...Object.fromEntries(
                  (outerFormik?.values?.selectedVariantsAttributes || []).map(
                    (attr) => [attr.value, '']
                  )
                ),
              },
            ], // If adding, initialize with an empty variant
      }}
      enableReinitialize={outerFormik?.values?.skuSuffix ? true : false}
      validationSchema={VARIANTS_CONFIGURATION_ADD_SCHEMA(attributesValue)}
      onSubmit={(values) => {
        // Check if there are selected variants before submission
        if (!values?.selectedVariantsAttributes?.length) {
          showWarningToast('Please add at least one variation before saving.');
          return;
        }

        const fixedKeys = ['id', 'sku', 'upc', 'images', 'newId'];
        const combinations = new Set();
        let isDuplicateFound = false;

        const existingVariants = outerFormik?.values?.productVariants || [];

        const fullVariants = editVariantId
          ? existingVariants.map((variant) =>
              variant.id === editVariantId
                ? { ...variant, ...values.selectedVariantsAttributes[0] }
                : variant
            )
          : [...existingVariants, ...values.selectedVariantsAttributes];

        for (const variation of fullVariants) {
          const comboKey = Object.entries(variation)
            .filter(([key]) => !fixedKeys.includes(key))
            .map(([_, value]) => value?.toString().toLowerCase().trim())
            .join('|');

          if (combinations.has(comboKey)) {
            isDuplicateFound = true;
            break;
          }
          combinations.add(comboKey);
        }

        if (isDuplicateFound) {
          showWarningToast(
            'Each variant must have a unique combination of attribute values.'
          );
          return;
        }

        // If no duplicates, proceed to update outerFormik
        const updatedVariants = fullVariants;

        outerFormik.setFieldValue('productVariants', updatedVariants);

        showSuccessToast('Variants saved successfully!');
        onClose();
      }}
    >
      {(formik) => {
        useEffect(() => {
          const selected = outerFormik?.values?.selectedVariantsAttributes;
          if (!selected) return;

          const updated = formik.values.selectedVariantsAttributes.map(
            (variation) => {
              const updatedVar = { ...variation };

              selected.forEach((attr) => {
                if (!(attr.value in updatedVar)) {
                  updatedVar[attr.value] = '';
                }
              });

              return updatedVar;
            }
          );

          formik.setFieldValue('selectedVariantsAttributes', updated);
        }, [outerFormik?.values?.selectedVariantsAttributes]);

        return (
          <Form className="max-w-[1152px] mx-auto">
            <div className="flex items-center justify-between mb-4 xl:mb-6">
              <button type="button" className="btn btn-gray" onClick={onClose}>
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                onClick={(e) => {
                  e.preventDefault();
                  formik.submitForm();
                }}
              >
                Save Variants
              </button>
            </div>
            <div className="max-h-[calc(100dvh-260px)] overflow-y-auto">
              <FieldArray name="selectedVariantsAttributes">
                {() =>
                  formik.values.selectedVariantsAttributes.map(
                    (variation, index) => (
                      <div key={index} className="card !p-0 mb-4">
                        <div className="flex items-center justify-between p-4">
                          <h3 className="text-sm font-bold">
                            Variation {index + 1}
                          </h3>

                          {!editVariantId && index !== 0 && (
                            <button
                              type="button"
                              onClick={() =>
                                handleRemoveVariation(index, formik)
                              }
                              className="flex items-center justify-center h-8 w-8 text-danger-500 border border-border-color hover:bg-danger-500 hover:text-white hover:border-danger-500 transition-base shadow-[0px_1px_0px_0px_#1A1A1A12] rounded-lg cursor-pointer"
                            >
                              <span className="icon icon-trash" />
                            </button>
                          )}
                        </div>
                        <div className="p-4 pt-2">
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            <InputField
                              type="text"
                              label="SKU"
                              id={`selectedVariantsAttributes.${index}.sku`}
                              name={`selectedVariantsAttributes.${index}.sku`}
                              marginBottom="mb-0"
                              value={variation.sku}
                              generateLabel="Generate code"
                              className="flex-1"
                              formik={formik}
                              onGenerateValue={() => {
                                const timestamp = Date.now()
                                  .toString()
                                  .slice(-6);
                                const random = Math.random()
                                  .toString(36)
                                  .substring(2, 5)
                                  .toUpperCase();
                                const newSku = `COA-VDV066-${timestamp}-${random}`;
                                formik.setFieldValue(
                                  `selectedVariantsAttributes.${index}.sku`,
                                  newSku
                                );
                              }}
                              readOnly
                            />
                            <InputField
                              type="text"
                              id={`selectedVariantsAttributes.${index}.upc`}
                              name={`selectedVariantsAttributes.${index}.upc`}
                              marginBottom="mb-0"
                              label="Universal Product Code"
                              {...formik.getFieldProps(
                                `selectedVariantsAttributes.${index}.upc`
                              )}
                            />
                            {formik.touched.selectedVariantsAttributes?.[index]
                              ?.upc &&
                              formik.errors.selectedVariantsAttributes?.[index]
                                ?.upc && (
                                <span className="text-danger-500 text-xs mt-1">
                                  {
                                    formik.errors.selectedVariantsAttributes[
                                      index
                                    ].upc
                                  }
                                </span>
                              )}
                          </div>
                          <div className="grid grid-cols-2 gap-4 mb-4">
                            {outerFormik?.values?.selectedVariantsAttributes?.map(
                              (attr) => (
                                <div key={attr.value}>
                                  <span className="form-label">
                                    Attribute: {attr.label}
                                  </span>

                                  <SelectField
                                    options={
                                      outerFormik?.values?.selectedAttributesValues
                                        ?.find(
                                          (e) => e?.name?.value === attr?.value
                                        ) // Find the attribute by value
                                        ?.values.map((e) => ({
                                          label: e.label, // Label to display in the select options (e.g., 'Red', 'Blue')
                                          value: e.value, // Value to set in the field (ID, e.g., Q1BudjdlR004b0pvNEd2WWJ1M3lTUT09)
                                        })) || []
                                    } // Return empty array if no options are found
                                    className="single-select"
                                    placeholder={`Select ${attr?.label}`} // Placeholder text for the select field
                                    value={{
                                      value:
                                        formik.values
                                          .selectedVariantsAttributes[index]?.[
                                          attr.value
                                        ] || '', // Get selected value (ID)
                                      label:
                                        outerFormik?.values?.selectedAttributesValues
                                          ?.find(
                                            (e) =>
                                              e?.name?.value === attr?.value
                                          ) // Find the corresponding attribute
                                          ?.values.find(
                                            (e) =>
                                              e.value ===
                                              formik.values
                                                .selectedVariantsAttributes[
                                                index
                                              ]?.[attr.value] // Match selected value (ID)
                                          )?.label || '', // Display the corresponding label based on the selected value
                                    }}
                                    onChange={(selectedOption) => {
                                      const selectedValue =
                                        selectedOption?.value || ''; // Get the value (ID) from the selected option
                                      formik.setFieldValue(
                                        `selectedVariantsAttributes.${index}.${attr.value}`, // Update the form state with the selected ID
                                        selectedValue
                                      );
                                    }}
                                  />

                                  {formik.touched.selectedVariantsAttributes?.[
                                    index
                                  ]?.[attr.value] &&
                                    formik.errors.selectedVariantsAttributes?.[
                                      index
                                    ]?.[attr.value] && (
                                      <div className="text-danger-500 text-xs mt-1">
                                        {
                                          formik.errors
                                            .selectedVariantsAttributes[index][
                                            attr.value
                                          ]
                                        }
                                      </div>
                                    )}
                                </div>
                              )
                            )}
                          </div>

                          <ImageUpload
                            images={
                              formik.values.selectedVariantsAttributes[index]
                                .images || []
                            }
                            name={`selectedVariantsAttributes.${index}.images`}
                            setImages={(imgs) =>
                              formik.setFieldValue(
                                `selectedVariantsAttributes.${index}.images`,
                                imgs
                              )
                            }
                            maxSize={5000000}
                            heading="Image"
                            uploadText="Add multiple images"
                            maxFiles={5}
                            // showError={
                            //   formik.submitCount > 0 &&
                            //   (!formik.values.selectedVariantsAttributes[index]
                            //     .images ||
                            //     formik.values.selectedVariantsAttributes[index]
                            //       .images?.length < 1)
                            // }
                            formik={formik}
                          />
                          {formik.touched.selectedVariantsAttributes?.[index]
                            ?.images &&
                            formik.errors.selectedVariantsAttributes?.[index]
                              ?.images && (
                              <span className="text-danger-500 text-xs mt-1">
                                {
                                  formik.errors.selectedVariantsAttributes[
                                    index
                                  ].images
                                }
                              </span>
                            )}
                        </div>
                      </div>
                    )
                  )
                }
              </FieldArray>
            </div>

            {!editVariantId && (
              <div className="mt-4 flex justify-end">
                <button
                  type="button"
                  className="btn btn-outline-gray"
                  onClick={() => handleAddVariation(formik.values, formik)}
                >
                  + Add more
                </button>
              </div>
            )}
          </Form>
        );
      }}
    </Formik>
  );
};

export default VariantBuilder;
