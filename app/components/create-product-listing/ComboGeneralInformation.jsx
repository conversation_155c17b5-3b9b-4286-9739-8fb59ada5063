'use client';
import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useEffect,
} from 'react';
import Image from 'next/image';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import * as Yup from 'yup';

// Validation Schema
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Title is required')
    .max(150, 'Title is too long'),
  description: Yup.string().required('Description is required'),
});

const ComboGeneralInformation = forwardRef(
  ({ onSuccess, valueComboData }, ref) => {
    const [highlights, setHighlights] = useState([]);
    const formikRef = React.useRef();

    useImperativeHandle(ref, () => ({
      submitForm: () => {
        formikRef.current?.handleSubmit();
      },
      // getValues: () => formikRef.current.values,
      resetForm: () => {
        formikRef.current?.resetForm();
      },
    }));

    useEffect(() => {
      setHighlights(valueComboData?.key_highlights || []);
    }, [valueComboData?.key_highlights]);

    const handleHighlightChange = (index, value, setFieldValue) => {
      const newHighlights = [...highlights];
      newHighlights[index] = value;

      const filteredHighlights = newHighlights.filter(
        (item) => item !== null && item !== undefined && item.trim() !== ''
      );

      setHighlights(newHighlights);
      setFieldValue('highlights', filteredHighlights);
    };

    const handleAddHighlight = (setFieldValue) => {
      const newHighlights = [...highlights, ''];
      setHighlights(newHighlights);
      setFieldValue('highlights', newHighlights);
    };

    const handleRemoveHighlight = (index, setFieldValue) => {
      const newHighlights = highlights.filter((_, i) => i !== index);
      setHighlights(newHighlights);
      setFieldValue('highlights', newHighlights);
    };
    return (
      <Formik
        innerRef={formikRef}
        initialValues={{
          id: valueComboData?.id || '',
          name: valueComboData?.name || '',
          description: valueComboData?.description || '',
          highlights: valueComboData?.key_highlights || [],
        }}
        enableReinitialize
        validationSchema={validationSchema}
        onSubmit={(values, { setSubmitting }) => {
          const filteredHighlights = values.highlights.filter(
            (item) => item !== null && item !== undefined && item.trim() !== ''
          );
          const payload = {
            combo_id: values?.id ?? '',
            name: values?.name ?? '',
            description: values?.description ?? '',
            key_highlights: filteredHighlights ?? [],
          };

          setTimeout(() => {
            onSuccess(payload);
            setSubmitting(false);
          }, 500);
        }}
      >
        {({ values, setFieldValue, touched, errors }) => (
          <Form>
            <div className="bg-white border border-border-color rounded-xl">
              <div className="flex items-center justify-between gap-3 p-4">
                <h2 className="font-bold">General Information</h2>
                <div className="flex items-center gap-3">
                  <span className="text-xs text-gray-400">
                    Vendor ID: #VX321
                  </span>
                </div>
              </div>

              <div className="space-y-4 xl:space-y-6 p-4 pt-2">
                {/* Combo Title */}
                <div className="flex flex-col mb-4 xl:mb-6">
                  <label htmlFor="name" className="form-label">
                    Combo Title
                  </label>
                  <Field
                    as="textarea"
                    id="name"
                    name="name"
                    className={`form-control ${touched.name && errors.name ? 'border-red-500' : ''}`}
                    placeholder="Enter Combo Title"
                    rows="3"
                    maxLength={150}
                    value={values.name}
                  />
                  <ErrorMessage
                    name="name"
                    component="p"
                    className="text-danger-500 text-xs mt-1"
                  />
                </div>

                {/* Combo Description */}
                <div className="flex flex-col">
                  <div className="flex justify-between items-center gap-2 mb-1">
                    <label htmlFor="description" className="form-label mb-0">
                      Combo Description
                    </label>
                    <button
                      type="button"
                      className="inline-flex items-center gap-2 text-xs font-bold cursor-pointer text-primary-500 transition-base text-gradient"
                    >
                      <Image
                        src="/images/ai-icon.svg"
                        alt="AI Icon"
                        width={15}
                        height={15}
                      />
                      Generate text
                    </button>
                  </div>
                  <Field
                    as="textarea"
                    id="description"
                    name="description"
                    rows="8"
                    maxLength={5000}
                    className={`form-control ${touched.description && errors.description ? 'border-red-500' : ''}`}
                    value={values.description}
                  />
                  <ErrorMessage
                    name="description"
                    component="p"
                    className="text-danger-500 text-xs mt-1"
                  />
                </div>

                {/* Key Highlights */}
                <div className="flex flex-col">
                  <div className="flex justify-between items-center">
                    <span className="form-label mb-0">Key Highlights</span>
                    <button
                      type="button"
                      className="inline-flex items-center gap-1 text-primary-500 text-xs font-semibold hover:text-primary-600"
                      onClick={() => handleAddHighlight(setFieldValue)}
                    >
                      <span className="icon icon-plus text-sm" />
                      Add more
                    </button>
                  </div>

                  <div
                    className={`flex flex-col gap-1 ${highlights.length > 0 ? 'mt-2' : ''}`}
                  >
                    {highlights?.map((highlight, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <input
                          type="text"
                          className="form-control flex-1"
                          placeholder="Enter Combo highlight"
                          value={highlight}
                          onChange={(e) =>
                            handleHighlightChange(
                              index,
                              e.target.value,
                              setFieldValue
                            )
                          }
                        />
                        <button
                          type="button"
                          onClick={() =>
                            handleRemoveHighlight(index, setFieldValue)
                          }
                          className="text-danger-500 hover:text-danger-600 hover:bg-danger-500/10 w-8 h-8 flex items-center justify-center rounded-md cursor-pointer transition-base"
                        >
                          <span className="icon icon-trash text-lg align-middle" />
                        </button>
                      </div>
                    ))}
                  </div>
                  {errors.highlights &&
                    typeof errors.highlights === 'string' && (
                      <p className="text-danger-500 text-xs mt-1">
                        {errors.highlights}
                      </p>
                    )}
                </div>
              </div>
            </div>
          </Form>
        )}
      </Formik>
    );
  }
);

export default ComboGeneralInformation;
