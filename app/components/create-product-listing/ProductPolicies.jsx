'use client';
import React, { useEffect, useState } from 'react';
import { Form, Formik, useFormikContext } from 'formik';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import RangeDatePicker from '../Inputs/RangeDatePicker';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import { PROUDCT_POLICIES_SCHEMA } from '@/utils/schema';
import { useDispatch, useSelector } from 'react-redux';
import { getCountryOfOrigin, submitProductPoliciesInfo } from '@/store/api/productApi';
import { formatDate } from '../create-product-listing/CreateProduct';
import { ProductType } from '@/utils/constant';

const ProductPolicies = ({ navigationComponent, module=ProductType?.RETAIL }) => {
  const dispatch = useDispatch()
  const [countryOfOrigin, setCountryOfOrigin] = useState([])
  const { productDetails, productSetDetails,productId} = useSelector((state) =>state.product)
  const productData = module === ProductType?.RETAIL ? productDetails : productSetDetails;

  useEffect(()=>{
    GetCountryOfOrigin()
  },[])

  const GetCountryOfOrigin = async ()=>{
     const {payload} = await dispatch(getCountryOfOrigin())
     if(payload.status || payload.ok){
      setCountryOfOrigin(payload.data.map((e)=> { return {label: e.name, value : e.id}}))
     }
  }

  return (
    <Formik
      initialValues={{
        countryOfOrigin: '' || productData?.policies?.country_of_origin?.id,
        tariffPercentage: ''|| productData?.policies?.tariff_percentage,
        validityFrom: '' || productData?.policies?.validity_start_date,
        validityTo: '' || productData?.policies?.validity_end_date,
        lifeTimeValidity: productData?.policies?.is_life_time_validity?"Yes" : "No" || 'No' ,
        acceptScratchOrDent: productData?.policies?.is_scratch_dent? "Yes" : "No" || 'No',
        hasWarranty: productData?.policies?.is_warranty_applicable? "Yes" : "No" ||  'No',
        durationType: '' || productData?.policies?.duration_type,
        durationValue: '' || productData?.policies?.duration_value,
        acceptReturns: productData?.policies?.is_return_applicable? "Yes" : "No" || 'No',
        returnDays: '' || productData?.policies?.days_return_acceptance,
        returnPolicy: '' || productData?.policies?.return_policy,
        policyFile: null,
      }}
      validationSchema={PROUDCT_POLICIES_SCHEMA}
      validateOnChange={true}
      validateOnBlur={true}
      enableReinitialize={true}
      onSubmit={async (values) => {
        
       const {
        countryOfOrigin: country_of_origin,
        tariffPercentage: tariff_percentage,
        validityFrom,
        validityTo,
        lifeTimeValidity,
        acceptScratchOrDent,
        hasWarranty,
        durationType: duration_type,
        durationValue: duration_value,
        acceptReturns,
        returnDays: days_return_acceptance,
        returnPolicy: return_policy,
        policyFile: return_doc,
       } = values

       const {payload} = await dispatch(submitProductPoliciesInfo({
        country_of_origin,
        tariff_percentage,
        validity_start_date : formatDate(validityFrom),
        validity_end_date : formatDate(validityTo),
        is_life_time_validity : lifeTimeValidity == 'Yes' ? 1 : 0,
        is_scratch_dent : acceptScratchOrDent == 'Yes' ? 1 : 0,
        is_warranty_applicable : hasWarranty == 'Yes' ? 1 : 0,
        duration_type,
        duration_value,
        is_return_applicable : acceptReturns=="Yes" ? 1 : 0,
        days_return_acceptance,
        return_policy,
        return_doc,
        product_id:productId,
        is_new_return_doc:0
       }))
      return payload?.ok

      }}
    >
      {(formik) => {
        return (
          <Form onSubmit={formik.handleSubmit}>
            {navigationComponent &&
              React.cloneElement(navigationComponent, {
                ...navigationComponent.props,
                formik,
              })}
            <>
              <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
                <div className="max-w-[1152px] mx-auto px-6 pb-8">
                  <div className="bg-white border border-border-color rounded-xl mb-4">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">
                        Product Origin and Tariff{' '}
                      </h2>
                    </div>

                    <div className="grid grid-cols-2 gap-4 p-4 pt-2">
                      <div className="flex flex-col flex-1">
                        <span className="form-label">
                          Country of Origin
                        </span>
                        <SelectField
                          className="single-select"
                          name="countryOfOrigin"
                          label="Country of Origin"
                          placeholder="Select type"
                          options={countryOfOrigin}
                          formik={formik}
                        />
                        {formik?.touched?.countryOfOrigin &&
                          formik?.errors?.countryOfOrigin && (
                            <div className="text-danger-500 text-xs mt-1">
                              {formik?.errors?.countryOfOrigin}
                            </div>
                          )}
                      </div>
                      <div className="flex flex-col mb-0">
                        <InputField
                          id="tariffPercentage"
                          name="tariffPercentage"
                          type="text"
                          marginBottom="mb-0"
                          label="Tariff Percentage"
                          placeholder={null}
                          value={formik.values.tariffPercentage}
                          onChange={formik.handleChange}
                          formik={formik}
                          length={100}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Product Validity */}
                  <div className="bg-white border border-border-color rounded-xl mb-4">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">Product Validity</h2>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4 p-4 pt-2">
                      <div>
                        <label className="form-label mb-2 block">
                          Validity Duration
                        </label>
                        <RangeDatePicker
                          placeholder="From - To"
                          onChange={(dates) => {
                            formik.setFieldValue('validityFrom', dates?.[0] || '');
                            formik.setFieldValue('validityTo', dates?.[1] || '');
                          }}
                          formik={formik}
                          nameFrom="validityFrom"
                          nameTo="validityTo"
                          disableEndDate={formik.values.lifeTimeValidity === 'Yes'}
                        />

                        {formik.touched.validityFrom &&
                          formik.errors.validityFrom && (
                            <div className="text-danger-500 text-xs mt-1">
                              {formik.errors.validityFrom}
                            </div>
                          )}

                        {formik.touched.validityTo && formik.errors.validityTo && (
                          <div className="text-danger-500 text-xs mt-1">
                            {formik.errors.validityTo}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-3 mb-0 mt-6">
                        <CustomCheckbox
                          id="lifeTimeValidity"
                          name="lifeTimeValidity"
                          label="Life-Time Validity"
                          checked={formik.values.lifeTimeValidity === 'Yes'}
                          onChange={(e) => {
                            const checked = e.target.checked;
                            formik.setFieldValue(
                              'lifeTimeValidity',
                              checked ? 'Yes' : 'No'
                            );

                            // Clear end date if lifeTimeValidity is checked
                            if (checked) {
                              formik.setFieldValue('validityTo', null);
                            }
                          }}
                        />
                        <CustomCheckbox
                          id="acceptScratchOrDent"
                          name="acceptScratchOrDent"
                          label="Accept Scratch or Dent?"
                          checked={formik.values.acceptScratchOrDent === 'Yes'}
                          onChange={(e) => {
                            formik.setFieldValue(
                              'acceptScratchOrDent',
                              e.target.checked ? 'Yes' : 'No'
                            );
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Warranty and Return */}
                  <div className="bg-white border border-border-color rounded-xl mb-4">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">
                        Warranty and Return
                      </h2>
                    </div>

                    <div className="space-y-3">
                      <div className="p-4 pt-2 mb-0">
                        {/* Warranty Toggle */}
                        <div className="flex justify-between items-center">
                          <CustomCheckbox
                            id="hasWarranty"
                            name="hasWarranty"
                            label="This product has warranty"
                            checked={formik.values.hasWarranty === 'Yes'}
                            onChange={(e) => {
                              formik.setFieldValue(
                                'hasWarranty',
                                e.target.checked ? 'Yes' : 'No'
                              );
                            }}
                          />
                        </div>

                        {formik.values.hasWarranty === 'Yes' && (
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
                            <div className="flex flex-col">
                              <span className="form-label">Duration Type</span>
                              <SelectField
                                className="single-select"
                                name="durationType"
                                placeholder=""
                                options={[
                                  { value: 'day', label: 'Days' },
                                  { value: 'month', label: 'Months' },
                                  { value: 'year', label: 'Years' },
                                ]}
                                formik={formik}
                              />
                              {formik.touched.durationType &&
                                formik.errors.durationType && (
                                  <div className="text-danger-500 text-xs mt-1">
                                    {formik.errors.durationType}
                                  </div>
                                )}
                            </div>
                            <div className="flex flex-col">
                              <span className="form-label">Duration Value</span>
                              <SelectField
                                className="single-select"
                                name="durationValue"
                                placeholder=""
                                options={[
                                  { value: 30, label: '30' },
                                  { value: 60, label: '60' },
                                  { value: 90, label: '90' },
                                ]}
                                formik={formik}
                              />
                              {formik.touched.durationValue &&
                                formik.errors.durationValue && (
                                  <div className="text-danger-500 text-xs mt-1">
                                    {formik.errors.durationValue}
                                  </div>
                                )}
                            </div>
                          </div>
                        )}

                        {/* Returns Toggle */}
                        <div className="mt-4">
                          <CustomCheckbox
                            id="acceptReturns"
                            name="acceptReturns"
                            label="This product has returns"
                            checked={formik.values.acceptReturns === 'Yes'}
                            onChange={(e) => {
                              formik.setFieldValue(
                                'acceptReturns',
                                e.target.checked ? 'Yes' : 'No'
                              );
                            }}
                          />
                          {formik.touched.acceptReturns &&
                            formik.errors.acceptReturns && (
                              <div className="text-danger-500 text-xs mt-1">
                                {formik.errors.acceptReturns}
                              </div>
                            )}

                          {formik.values.acceptReturns === 'Yes' && (
                            <div className="mt-4">
                              <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4">
                                <div className="flex flex-col mb-4">
                                  <span className="form-label">
                                    Days for Return Acceptance
                                  </span>
                                  <SelectField
                                    className="single-select"
                                    name="returnDays"
                                    placeholder=""
                                    options={[
                                      { value: 7, label: '7 Days' },
                                      { value: 14, label: '14 Days' },
                                      { value: 30, label: '30 Days' },
                                    ]}
                                    formik={formik}
                                  />
                                  {formik.touched.returnDays &&
                                    formik.errors.returnDays && (
                                      <div className="text-danger-500 text-xs mt-1">
                                        {formik.errors.returnDays}
                                      </div>
                                    )}
                                </div>
                                <div className="flex flex-col mb-4">
                                  <span className="form-label">
                                    Return Policy
                                  </span>
                                  <SelectField
                                    className="single-select"
                                    name="returnPolicy"
                                    placeholder=""
                                    options={[
                                      {
                                        value: 'full_refund',
                                        label: 'Full Refund',
                                      },
                                      {
                                        value: 'store_credit',
                                        label: 'Store Credit',
                                      },
                                      { value: 'exchange', label: 'Exchange Only' },
                                    ]}
                                    formik={formik}
                                  />
                                  {formik.touched.returnPolicy &&
                                    formik.errors.returnPolicy && (
                                      <div className="text-danger-500 text-xs mt-1">
                                        {formik.errors.returnPolicy}
                                      </div>
                                    )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          </Form>
        );
      }}
    </Formik>
  );
};

export default ProductPolicies;
