'use client';

import React, { useEffect, useRef, useState } from 'react';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import ResidentialAddressSection from '../onboarding/ResidentialAdressSection';
import { Form, Formik, useFormikContext } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import {
  createPickupAddress,
  getPickupAddress,
  submitProductShippingInfo,
} from '@/store/api/productApi';
import PackageDetails from './PackageDetails';
import { PACKAGING_INFORMATION_SCHEMA } from '@/utils/schema';
import { components } from 'react-select';
import ResidentialAddressSectionShipping from './ResidentialAddressSectionShipping';
import { ProductType } from '@/utils/constant';

const ShippingLogistics = ({
  navigationComponent,
  module = ProductType?.RETAIL,
}) => {
  const [showNewAddress, setShowNewAddress] = useState(false);
  const [address, setAddress] = useState([]);
  const dispatch = useDispatch();
  const formikRef = useRef();
  const { productId } = useSelector((state) => state.product);
  const { productDetails, productSetDetails } = useSelector(
    (state) => state.product
  );
  const productData =
    module === ProductType?.RETAIL ? productDetails : productSetDetails;
  useEffect(() => {
    getAddress();
  }, []);

  const getAddress = async () => {
    const { payload } = await dispatch(getPickupAddress());

    if (payload?.ok && payload?.data) {
      const addressData = payload.data;

      const formattedAddress = Object.values(addressData).map((arr) => {
        const e = arr[0]; // Each key has only one element
        return {
          value: e.id,
          label: e.address_line_1,
        };
      });

      setAddress(formattedAddress);
    } else {
      setAddress([]);
    }
  };
  return (
    <Formik
      initialValues={{
        product_unit: '' || productData?.['shipping_and_logistic']?.unit_id,
        weight: '' || productData?.['shipping_and_logistic']?.weight,
        weight_unit:
          '' || productData?.['shipping_and_logistic']?.parcel_weight_unit,
        dimensions_unit:
          '' || productData?.['shipping_and_logistic']?.parcel_dimensions_unit,
        dimensions_length:
          '' || productData?.['shipping_and_logistic']?.dimensions_length,
        dimensions_width:
          '' || productData?.['shipping_and_logistic']?.dimensions_width,
        dimensions_height:
          '' || productData?.['shipping_and_logistic']?.dimensions_height,
        minLeadTime: '' || productData?.['shipping_and_logistic']?.lead_min_day,
        maxLeadTime: '' || productData?.['shipping_and_logistic']?.lead_max_day,
        preferredCarrier:
          productData?.['shipping_and_logistic']?.ship_by || 'seller',
        shippingCost:
          '' || productData?.['shipping_and_logistic']?.shipping_cost,
        isFreeDelivery:
          false || productData?.['shipping_and_logistic']?.is_free_delivery,
        handlingFee: '' || productData?.['shipping_and_logistic']?.handling_fee,
        availableShipping:
          productData?.['shipping_and_logistic']?.is_immediate_delivery != null
            ? 1
            : '',
        pickupAddress: '' || productData?.['shipping_and_logistic']?.address_id,
        components: productData?.['shipping_and_logistic']?.included_components
          ?.length
          ? productData?.['shipping_and_logistic'].included_components
          : [''],
        shippingCarrier:
          '' || productData?.['shipping_and_logistic']?.preffered_carrier,
      }}
      validationSchema={PACKAGING_INFORMATION_SCHEMA}
      validateOnChange={true}
      validateOnBlur={true}
      enableReinitialize={true}
      onSubmit={async (values) => {
        const {
          product_unit: unit_id,
          weight,
          weight_unit: parcel_weight_unit,
          dimensions_unit: parcel_dimensions_unit,
          dimensions_length,
          dimensions_width,
          dimensions_height,
          minLeadTime: lead_min_day,
          maxLeadTime: lead_max_day,
          preferredCarrier: ship_by,
          shippingCost: shipping_cost,
          isFreeDelivery,
          handlingFee: handling_fee,
          availableShipping: is_immediate_delivery,
          pickupAddress: address_id,
          components: included_components,
          shippingCarrier: preffered_carrier,
        } = values;

        const { payload } = await dispatch(
          submitProductShippingInfo({
            ship_by,
            unit_id,
            weight,
            parcel_weight_unit,
            parcel_dimensions_unit,
            dimensions_height,
            dimensions_length,
            dimensions_width,
            lead_max_day,
            lead_min_day,
            preffered_carrier,
            shipping_cost,
            is_free_delivery: isFreeDelivery ? 1 : 0,
            handling_fee,
            is_immediate_delivery,
            address_id,
            included_components,
            product_id: productId,
          })
        );

        return payload?.ok;
      }}
    >
      {(formik) => {
        useEffect(() => {
          const isFree = formik.values.isFreeDelivery;
          if (isFree) {
            formik.setFieldValue('shippingCost', '');
          }
        }, [formik.values.isFreeDelivery]);

        useEffect(() => {
          if (formik.values.preferredCarrier === 'hubsup') {
            formik.setFieldValue('shippingCarrier', '');
          }
        }, [formik.values.preferredCarrier]);
        return (
          <Form onSubmit={formik.handleSubmit}>
            {navigationComponent &&
              React.cloneElement(navigationComponent, {
                ...navigationComponent.props,
                formik,
              })}
            <>
              <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
                <div className="max-w-[1152px] mx-auto px-6 pb-8">
                  <PackageDetails />
                  <div className="bg-white border border-border-color rounded-xl mb-4">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">
                        Product Pickup Location
                      </h2>
                    </div>

                    <div className="space-y-4 p-4 pt-2">
                      <div className="flex flex-col">
                        {/* Select address */}
                        <div
                          className={`flex flex-col mb-0 transition-all duration-300 ease-in-out ${
                            showNewAddress
                              ? 'max-h-0 opacity-0 overflow-hidden'
                              : 'max-h-[200px] opacity-100'
                          }`}
                        >
                          <div className="flex items-center justify-between gap-2">
                            <label htmlFor="brandName" className="form-label">
                              Select location
                            </label>
                            <button
                              className="text-primary-500 hover:text-primary-600 cursor-pointer font-semibold text-[13px] transition-base"
                              onClick={() => setShowNewAddress(true)}
                            >
                              + Add new
                            </button>
                          </div>
                          <SelectField
                            className="single-select"
                            name="pickupAddress"
                            label="Select location"
                            placeholder=""
                            options={address}
                            formik={formik}
                          />
                          {formik?.touched?.pickupAddress &&
                            formik?.errors?.pickupAddress && (
                              <div className="text-danger-500 text-xs mt-1">
                                {formik?.errors?.pickupAddress}
                              </div>
                            )}
                        </div>

                        {/* Add New address */}
                        <div
                          className={`transition-all duration-300 ease-in-out ${
                            showNewAddress
                              ? 'max-h-[1000px] opacity-100'
                              : 'max-h-0 opacity-0 overflow-hidden'
                          }`}
                        >
                          <div className="flex justify-between items-center mb-4">
                            <label className="form-label !font-semibold">
                              Add New Address
                            </label>

                            <div className="flex gap-4">
                              <button
                                className="text-danger-500 hover:text-danger-600 cursor-pointer font-bold text-[13px] transition-base"
                                onClick={async () => {
                                  if (formikRef.current) {
                                    const formik = formikRef.current;

                                    if (
                                      Object.keys(formik.errors).length === 0
                                    ) {
                                      // No validation errors
                                      await formik.submitForm();
                                      setShowNewAddress(false);
                                    } else {
                                      // Ensure both errors and touched are updated
                                      formik.setTouched(
                                        Object.entries(formik.errors).reduce(
                                          (acc, [key, value]) => {
                                            if (
                                              typeof value === 'object' &&
                                              value !== null
                                            ) {
                                              acc[key] = Object.keys(
                                                value
                                              ).reduce((childAcc, childKey) => {
                                                childAcc[childKey] = true;
                                                return childAcc;
                                              }, {});
                                            } else {
                                              acc[key] = true;
                                            }
                                            return acc;
                                          },
                                          {}
                                        ),
                                        true // shouldValidate
                                      );

                                      formik.setErrors(formik.errors); // Explicitly set errors (important if not submitting)
                                    }
                                  }
                                }}
                              >
                                Save
                              </button>
                              <button
                                className="text-danger-500 hover:text-danger-600 cursor-pointer font-bold text-[13px] transition-base"
                                onClick={() => setShowNewAddress(false)}
                              >
                                Cancel
                              </button>
                            </div>
                          </div>

                          <ResidentialAddressSectionShipping
                            className="!p-0 !border-0"
                            showTitle={false}
                            formikRef={formikRef}
                            getAddress={getAddress}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white border border-border-color rounded-xl mb-4">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">
                        Fulfilment Channel
                      </h2>
                    </div>

                    <div className="space-y-3 p-4 pt-2">
                      <div className="flex flex-col gap-4 mb-4">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3">
                          {[
                            {
                              id: 'vendor',
                              title: 'By Vendor',
                              description:
                                'Ship products using your own carrier',
                              value: 'seller',
                            },
                            {
                              id: 'hubsups',
                              title: 'By Hubsups',
                              description: 'Let Hubsups handle your shipping',
                              value: 'hubsup',
                            },
                          ].map((type) => (
                            <label
                              key={type.id}
                              className={`
                      flex flex-col p-3 lg:p-4 rounded-xl border cursor-pointer relative
                      ${
                        formik.values.preferredCarrier === type.value
                          ? 'border-primary-500'
                          : 'border-border-color surface hover:border-primary-500 transition-base'
                      }
                    `}
                            >
                              <div className="flex items-center justify-between gap-2">
                                <div>
                                  <h3
                                    className={`font-semibold text-sm mb-0.5 text-dark-500 ${
                                      formik.values.preferredCarrier ===
                                      type.value
                                        ? 'text-primary-500'
                                        : ''
                                    }`}
                                  >
                                    {type.title}
                                  </h3>
                                  <p
                                    className={`text-xs text-gray-300 ${
                                      formik.values.preferredCarrier ===
                                      type.value
                                        ? 'text-primary-500'
                                        : ''
                                    }`}
                                  >
                                    {type.description}
                                  </p>
                                </div>

                                <div
                                  className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all
                        ${formik.values.preferredCarrier === type.value ? 'border-primary-500' : 'border-gray-200'}`}
                                >
                                  {formik.values.preferredCarrier ===
                                    type.value && (
                                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                                  )}
                                </div>
                                <input
                                  type="radio"
                                  name=" \preferredCarrier"
                                  value={type.value}
                                  checked={
                                    formik.values.preferredCarrier ===
                                    type.value
                                  }
                                  onChange={() => {
                                    formik.setFieldValue(
                                      'preferredCarrier',
                                      type.value
                                    );
                                  }}
                                  className="hidden"
                                />
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4 mb-0">
                        {formik.values.preferredCarrier === 'seller' && (
                          <div className="flex-1">
                            <InputField
                              id="shippingCarrier"
                              name="shippingCarrier"
                              type="text"
                              label="Shipping Carrier"
                              placeholder={null}
                              value={formik.values.shippingCarrier}
                              onChange={formik.handleChange}
                              formik={formik}
                              length={100}
                            />
                          </div>
                        )}
                        <div
                          className={`flex items-start gap-4 mb-0 ${formik.values.preferredCarrier === 'seller' ? '' : 'col-span-2'}`}
                        >
                          <div className="flex-1">
                            <InputField
                              id="shippingCost"
                              name="shippingCost"
                              type="number"
                              label="Shipping Cost"
                              placeholder="0.00"
                              rightText="$"
                              rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                              inputClassName="form-control !pl-7 !pr-4"
                              disabled={formik.values.isFreeDelivery}
                              value={formik.values.shippingCost}
                              onChange={formik.handleChange}
                              formik={formik}
                            />
                          </div>

                          <CustomCheckbox
                            id="isFreeDelivery"
                            name="isFreeDelivery"
                            label="Free Delivery"
                            checked={formik.values.isFreeDelivery}
                            onChange={(e) => {
                              formik.setFieldValue(
                                'isFreeDelivery',
                                e.target.checked
                              );
                              if (e.target.checked) {
                                formik.setFieldValue('shippingCost', '');
                              }
                            }}
                            className="mt-8"
                          />
                        </div>
                      </div>
                      <div className="mb-0">
                        <label className="form-label mb-2 block">
                          Lead Time
                        </label>
                        <div className="grid grid-cols-2 gap-4">
                          <InputField
                            id="minLeadTime"
                            name="minLeadTime"
                            type="number"
                            placeholder={null}
                            rightText="Min"
                            inputClassName="form-control !pr-12"
                            rightTextClassName="top-1/2 -translate-y-1/2 !text-dark-500"
                            value={formik.values.minLeadTime}
                            onChange={formik.handleChange}
                            formik={formik}
                          />
                          <InputField
                            id="maxLeadTime"
                            name="maxLeadTime"
                            type="number"
                            placeholder={null}
                            rightText="Max"
                            inputClassName="form-control !pr-12"
                            rightTextClassName="top-1/2 -translate-y-1/2 !text-dark-500"
                            value={formik.values.maxLeadTime}
                            onChange={formik.handleChange}
                            formik={formik}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <InputField
                          id="handlingFee"
                          name="handlingFee"
                          type="number"
                          label="Handling Fee"
                          placeholder={null}
                          marginBottom="mb-0"
                          value={formik.values.handlingFee}
                          onChange={formik.handleChange}
                          formik={formik}
                        />
                        <div className="flex flex-col mb-0">
                          <span className="form-label">
                            Available for Immediate Shipping
                          </span>
                          <SelectField
                            className="single-select"
                            id="availableShipping"
                            name="availableShipping"
                            placeholder={null}
                            options={[
                              { value: 0, label: 'No' },
                              { value: 1, label: 'Yes' },
                            ]}
                            formik={formik}
                          />
                          {formik.touched?.['availableShipping'] &&
                            formik.errors?.['availableShipping'] && (
                              <div className="text-danger-500 text-xs mt-1">
                                {formik.errors?.['availableShipping']}
                              </div>
                            )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          </Form>
        );
      }}
    </Formik>
  );
};

export default ShippingLogistics;
