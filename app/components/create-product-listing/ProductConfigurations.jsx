'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Formik, Form, useFormikContext } from 'formik';
import Image from 'next/image';
import { useDropzone } from 'react-dropzone';
import BaseModal from '../modals/BaseModal';
import InputField from '../Inputs/InputField';
import DefineAttributes from '../create-product-listing/DefineAttributes';
import { useDispatch, useSelector } from 'react-redux';
import { addAttributeAsync, getAttributesAsync } from '@/store/api/productApi';
import * as Yup from 'yup';

const ProductConfigurations = ({ onClose }) => {
  const outerFormik = useFormikContext();
  const dispatch = useDispatch();
  const { userDetails } = useSelector((state) => state.auth);
  const [showNewValueModal, setShowNewValueModal] = useState(false);
  const [currentAttribute, setCurrentAttribute] = useState('');
  const [newValue, setNewValue] = useState('');
  const [formikInstance, setFormikInstance] = useState(null);
  const [attributesList, setAttributesList] = useState([]);
  const [colorValue, setColorValue] = useState('#88309A');
  const [attributeId, setAttributeId] = useState('');

  const getValidationSchema = (currentAttribute) => {
    let baseSchema = {
      label: Yup.string().required('Label is required'),
    };

    if (currentAttribute.toLowerCase() === 'color') {
      baseSchema.color_code = Yup.string()
        .matches(/^#[0-9A-Fa-f]{6}$/, 'Enter a valid hex code')
        .required('Color code is required');
    } else if (currentAttribute.toLowerCase() === 'image') {
      baseSchema.image = Yup.mixed().required('Image is required');
    }

    return Yup.object().shape(baseSchema);
  };

  const validationSchema = Yup.object().shape({
    selectedAttributes: Yup.array()
      .min(1, 'Please select at least one attribute.')
      .required('Required'),

    selectedAttributesValues: Yup.array()
      .min(1, 'Please select at least one attribute value.')
      .required('Required')
      .test(
        'length-match',
        'Attributes and values must have the same length.',
        function (value) {
          const { selectedAttributes } = this.parent;
          return (
            Array.isArray(value) && value.length === selectedAttributes?.length
          );
        }
      ),
  });

  const [currentStep, setCurrentStep] = useState(1);
  const [variations, setVariations] = useState([]);
  const [groupBy, setGroupBy] = useState('Color');

  useEffect(() => {
    getAttributes();
  }, []);

  async function getAttributes() {
    const { payload } = await dispatch(getAttributesAsync(userDetails.id));
    const AttributeData = payload?.data?.map((attr) => ({
      name: attr.attribute_name,
      id: attr.id,
      type: attr.type,
      options: attr.attribute_values.map((val) => {
        return {
          color_code: val.type_value,
          value: val.attribute_values,
          id: val.attribute_id,
        };
      }),
    }));

    // Temporary attribute data | Remove after API is ready
    setAttributesList(AttributeData);
  }

  const handleAddNewValue = (attributeName, attributeid, formik) => {
    setCurrentAttribute(attributeName);
    setFormikInstance(formik);
    setShowNewValueModal(true);
    setAttributeId(attributeid);
  };

  const handleSaveNewValue = () => {
    if (newValue.trim() && formikInstance) {
      const trimmedValue = newValue.trim();

      setAttributesList((prevAttributes) =>
        prevAttributes.map((attr) => {
          if (attr.name === currentAttribute) {
            return {
              ...attr,
              options: [...attr.options, trimmedValue],
            };
          }
          return attr;
        })
      );

      // Get current selected values
      const currentSelected =
        formikInstance.values.selectedAttributes[currentAttribute] || [];

      // Update Formik with both existing and new values
      formikInstance.setFieldValue(`selectedAttributes.${currentAttribute}`, [
        ...currentSelected,
        trimmedValue,
      ]);

      setNewValue('');
      setShowNewValueModal(false);
    }
  };

  const generateVariations = (selectedAttributes) => {
    const attributes = Object.entries(selectedAttributes).filter(
      ([_, values]) => values.length > 0
    );

    if (attributes.length === 0) return [];

    const [[firstAttr, firstValues], ...rest] = attributes;

    let result = firstValues.map((value) => ({
      [firstAttr]: value,
      price: '',
      quantity: '100',
    }));

    rest.forEach(([attr, values]) => {
      const newResult = [];
      result.forEach((item) => {
        values.forEach((value) => {
          newResult.push({ ...item, [attr]: value });
        });
      });
      result = newResult;
    });

    return result;
  };

  const handleNextStep = (formik) => {
    if (currentStep === 1) {
      const newVariations = generateVariations(
        formik.values.selectedAttributes
      );
      setVariations(newVariations);
      setCurrentStep(2);
    }
  };

  const handleVariationChange = (index, field, value) => {
    const newVariations = [...variations];
    newVariations[index][field] = value;
    setVariations(newVariations);
  };

  const [uploadedImage, setUploadedImage] = useState(null);
  const [uploadedImageName, setUploadedImageName] = useState(''); // Add this state

  return (
    <>
      <Formik
        initialValues={{
          selectedAttributes: [],
          selectedAttributesValues: [],
        }}
        validationSchema={validationSchema}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
        onSubmit={(values) => {}}
      >
        {(formik) => (
          <Form className="w-full">
            <div className="flex justify-between items-center mb-6">
              <div className="flex flex-col gap-1">
                <div className="inline-flex items-center gap-3">
                  <h1 className="text-xl font-bold">Product Attributes</h1>
                </div>
              </div>
              {/* Heading Step buttons */}
              <div className="flex gap-3">
                <button
                  type="button"
                  className="btn btn-gray"
                  onClick={onClose}
                >
                  Cancel
                </button>
                {/* Remove the onClick handler from here */}
                <button
                  className="btn cursor-pointer"
                  onClick={() => {
                    // if(currentStep==1)handleNextStep(formik)
                    outerFormik.setFieldValue(
                      'selectedAttributesValue',
                      formik.values.selectedAttributesValues
                    );
                    // onClose()
                  }}
                >
                  Save attributes
                </button>
              </div>
            </div>

            {/* Define Attributes */}
            <div className="h-[calc(100dvh-198px)] overflow-y-auto">
              <DefineAttributes
                outerFormik={outerFormik}
                formik={formik}
                attributesList={attributesList}
                handleAddNewValue={handleAddNewValue}
              />
            </div>
          </Form>
        )}
      </Formik>
      {/* Add new value modal */}
      <Formik
        initialValues={{
          label: '',
          color_code: '',
          image: '',
        }}
        validationSchema={getValidationSchema(currentAttribute)}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
        onSubmit={async (values) => {
          const { payload } = await dispatch(
            addAttributeAsync({
              name: values.label,
              value: values.color_code || values.image,
              attribute_id: attributeId,
              type: currentAttribute.toLowerCase(),
            })
          );
          if (payload.status || payload.ok) {
            getAttributes();
          }
          setShowNewValueModal(false);
        }}
      >
        {(formik) => {
          const onDrop = useCallback((acceptedFiles) => {
            const file = acceptedFiles[0];
            formik.setFieldValue('image', file);
            setUploadedImage(URL.createObjectURL(file));
            setUploadedImageName(file.name); // Store the file name
          }, []);

          const { getRootProps, getInputProps, isDragActive } = useDropzone({
            onDrop,
            accept: {
              'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.svg'],
            },
            maxFiles: 1,
          });

          return (
            <Form>
              <BaseModal
                isOpen={showNewValueModal}
                onClose={() => {
                  setShowNewValueModal(false);
                  setNewValue('');
                }}
                size="md"
                title="Add new attribute value"
                paragraph={null}
              >
                <div className="flex flex-col w-full">
                  <div className="p-4">
                    <p className="mb-6 text-sm text-gray-500 font-medium">
                      This value will be added to the selected attribute for
                      this product. You can reuse it later for similar listings.
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                      <InputField
                        type="text"
                        placeholder="Enter new value"
                        label="Label"
                        marginBottom="mb-0"
                        name="label"
                        id="label"
                        autoComplete="off"
                        formik={formik}
                      />

                      {currentAttribute.toLowerCase() === 'color' && (
                        <div>
                          <label className="form-label mb-2">Color Code</label>
                          <div className="relative">
                            {/* Color Picker */}
                            <div className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 rounded-lg border border-border-color overflow-hidden">
                              <input
                                type="color"
                                value={formik.values.color_code}
                                className="absolute inset-0 w-7 h-7 -left-1 -top-1 cursor-pointer"
                                onChange={(e) =>
                                  formik.setFieldValue(
                                    'color_code',
                                    e.target.value
                                  )
                                }
                              />
                            </div>

                            {/* Text Input */}
                            <input
                              type="text"
                              name="color_code"
                              className={`form-control !pl-10 ${
                                formik.touched.color_code &&
                                formik.errors.color_code
                                  ? 'border-red-500'
                                  : ''
                              }`}
                              placeholder="#000000"
                              value={formik.values.color_code}
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value.length <= 7) {
                                  formik.setFieldValue('color_code', value);
                                }
                              }}
                            />

                            {/* Validation Error */}
                          </div>
                          {formik.touched.color_code &&
                            formik.errors.color_code && (
                              <span className="text-danger-500 text-xs mt-1">
                                {formik.errors.color_code}
                              </span>
                            )}
                        </div>
                      )}

                      {/* Image upload */}
                      {currentAttribute.toLowerCase() == 'image' && (
                        <div className="flex flex-col">
                          <label className="form-label mb-2">Image</label>
                          <div
                            {...getRootProps()}
                            className={`inline-flex ${
                              isDragActive ? 'bg-gray-50' : ''
                            }`}
                          >
                            <input {...getInputProps()} />
                            {uploadedImage ? (
                              <div className="flex items-center justify-between gap-2 border border-border-color rounded-lg py-2 px-4 w-full">
                                <div className="flex items-center gap-2">
                                  <div className="w-10 h-10 overflow-hidden aspect-square border border-border-color rounded-full">
                                    <Image
                                      src={uploadedImage}
                                      name="image"
                                      id="image"
                                      alt="Uploaded preview"
                                      width={100}
                                      height={100}
                                      className="object-cover h-full"
                                    />
                                  </div>
                                  <span className="text-sm text-gray-500 truncate max-w-[200px] ellipsis">
                                    {uploadedImageName}
                                  </span>
                                </div>
                                <button
                                  type="button"
                                  className="flex items-center justify-center h-8 w-8 text-danger-500 border border-border-color hover:bg-danger-500 hover:text-white hover:border-danger-500 transition-base shadow-[0px_1px_0px_0px_#1A1A1A12] rounded-lg cursor-pointer"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setUploadedImage(null);
                                    setUploadedImageName('');
                                  }}
                                >
                                  <span className="icon icon-trash text-lg" />
                                </button>
                              </div>
                            ) : (
                              <div className="inline-flex text-[13px] border border-border-color rounded-lg py-1.5 px-4 text-center cursor-pointer transition-base">
                                Select Image
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 border-t border-border-color p-4">
                    <button
                      className="btn btn-outline-gray"
                      onClick={() => {
                        setShowNewValueModal(false);
                        setNewValue('');
                      }}
                    >
                      Cancel
                    </button>
                    <button className="btn" onClick={handleSaveNewValue}>
                      Add Value
                    </button>
                  </div>
                </div>
              </BaseModal>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default ProductConfigurations;
