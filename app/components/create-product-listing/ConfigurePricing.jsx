3;
import React, { useEffect, useRef, useState } from 'react';
import DataTable from 'react-data-table-component';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import TierPricingContent from './TierPricingConfiguration';
import { Form, Formik, useFormikContext } from 'formik';
import * as Yup from 'yup';

const ConfigurePricing = ({ onClose }) => {
  const outerFormik = useFormikContext();
  const [defaultSku, setDefaultSku] = useState('24-BM04-BLK-LEA');
  const [chargeTaxEnabled, setChargeTaxEnabled] = useState(false);
  const [sameForAll, setSameForAll] = useState(false);
  const [isAddTierOpen, setIsAddTierOpen] = useState(false);
  const [selctedTier, setSelectedTier] = useState(null);
  const [tableData, setTableData] = useState([]);
  const tierPricingRef = useRef();

  useEffect(() => {
    const data = outerFormik?.values?.productVariants?.variations?.map(
      (e, i) => {
        return {
          id: e.id,
          sku: e.sku,
          additionalPrice: '0.00',
          finalPrice: '0.00',
          quantity: '1',
          minOrderQty: '1',
          outOfStock: '1',
          restocking: '1',
          tierPricing: false,
          tiers: [],
          backorder: false,
          defaultSku: false,
        };
      }
    );
    setTableData(data);
  }, [outerFormik?.values?.productVariants?.variations]);

  // Update the columns definition for Tier Pricing, Backorder, and Default SKU

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '56px',
      },
    },
  };

  const validationSchema = Yup.object().shape({
    basePrice: Yup.string().required('Base Price is required'),

    chargeTax: Yup.boolean(),

    taxationType: Yup.string().when('chargeTax', {
      is: true,
      then: (schema) => schema.required('Taxation Type is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    priceAfterTax: Yup.string().when('chargeTax', {
      is: true,
      then: (schema) => schema.required('Price After Tax is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  return (
    <>
      <Formik
        const
        initialValues={{
          basePrice: '',
          taxationType: '',
          priceAfterTax: '',
          chargeTax: false,
          sameForAll: false,
          defaultSku: '',
          variants: outerFormik?.values?.productVariants?.variations?.map(
            (e) => {
              return {
                id: e.id,
                sku: e.sku,
                additionalPrice: '',
                finalPrice: '',
                quantity: 1,
                minOrderQty: 1,
                outOfStock: 0,
                restocking: 0,
                tierPricing: false,
                tiers: [],
                backorder: false,
              };
            }
          ),
        }}
        validationSchema={validationSchema}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
        onSubmit={async (values) => {}}
      >
        {(formik) => {
          const columns = [
            {
              name: 'SKU Label',
              selector: (row) => row.sku,
              width: '150px',
            },
            {
              name: 'Additional Price',
              width: '110px',
              cell: (row) => (
                <InputField
                  type="text"
                  name="additionalPrice"
                  placeholder="0.00"
                  rightText="$"
                  rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                  inputClassName="form-control !pl-7 !pr-0"
                  marginBottom="mb-0"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.additionalPrice || ''
                  }
                  onChange={(e) => {
                    const inputValue = e.target.value;
                    const updatedVariants = formik.values.variants.map(
                      (variant) => {
                        if (variant.id === row.id) {
                          const additionalPrice = inputValue;
                          const finalPrice =
                            Number(inputValue || 0) +
                            Number(formik.values.basePrice || 0);
                          return { ...variant, additionalPrice, finalPrice };
                        }
                        return variant;
                      }
                    );

                    formik.setFieldValue('variants', updatedVariants);
                  }}
                />
              ),
            },
            {
              name: 'Final Price',
              cell: (row) => (
                <InputField
                  type="text"
                  name="finalPrice"
                  placeholder="0.00"
                  rightText="$"
                  rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                  inputClassName="form-control !pl-7 !pr-4"
                  marginBottom="mb-0"
                  value={
                    Number(
                      formik?.values?.variants?.find((e) => e.id === row.id)
                        ?.additionalPrice
                    ) + Number(formik.values.basePrice) || ''
                  }
                  readOnly
                  disabled
                />
              ),
            },
            {
              name: 'Quantity',
              cell: (row) => (
                <InputField
                  type="number"
                  name="quantity"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.quantity || ''
                  }
                  marginBottom="mb-0"
                  min="0"
                  onChange={(e) => {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === row.id
                        ? { ...variant, quantity: e.target.value }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }}
                />
              ),
            },
            {
              name: 'Min. Order Qty',
              cell: (row) => (
                <InputField
                  type="number"
                  name="minOrderQty"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.minOrderQty || ''
                  }
                  marginBottom="mb-0"
                  min="1"
                  onChange={(e) => {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === row.id
                        ? { ...variant, minOrderQty: e.target.value }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }}
                />
              ),
            },
            {
              name: 'Out of Stock',
              cell: (row) => (
                <InputField
                  type="number"
                  name="outOfStock"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.outOfStock || ''
                  }
                  marginBottom="mb-0"
                  min="0"
                  onChange={(e) => {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === row.id
                        ? { ...variant, outOfStock: e.target.value }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }}
                />
              ),
            },
            {
              name: 'Restocking',
              cell: (row) => (
                <InputField
                  type="number"
                  name="restocking"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.restocking || ''
                  }
                  marginBottom="mb-0"
                  min="0"
                  onChange={(e) => {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === row.id
                        ? { ...variant, restocking: e.target.value }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }}
                />
              ),
            },
            {
              name: 'Tier Pricing',
              center: true,
              cell: (row) => (
                <div className="flex justify-center">
                  <CustomCheckbox
                    id={`tier-pricing-${row.id}`}
                    name="tierPricing"
                    checked={
                      formik.values.variants?.find((e) => e.id === row.id)
                        ?.tierPricing || false
                    }
                    onChange={(e) => {
                      const newVariants = formik.values.variants.map(
                        (variant) =>
                          variant.id === row.id
                            ? { ...variant, tierPricing: e.target.checked }
                            : variant
                      );
                      formik.setFieldValue('variants', newVariants);
                      setSelectedTier((prev) =>
                        e.target.checked ? row.id : prev
                      );
                      if (e.target.checked) {
                        setIsAddTierOpen(true);
                      }
                    }}
                  />
                </div>
              ),
            },
            {
              name: 'Backorder',
              center: true,
              cell: (row) => (
                <div className="flex justify-center">
                  <CustomCheckbox
                    id={`backorder-${row.id}`}
                    name="backorder"
                    checked={
                      formik.values.variants?.find((e) => e.id === row.id)
                        ?.backorder || false
                    }
                    onChange={(e) => {
                      const newVariants = formik.values.variants.map(
                        (variant) =>
                          variant.id === row.id
                            ? { ...variant, backorder: e.target.checked }
                            : variant
                      );
                      formik.setFieldValue('variants', newVariants);
                    }}
                  />
                </div>
              ),
            },
            {
              name: 'Default SKU',
              center: true,
              cell: (row) => (
                <div className="relative flex items-center">
                  {/* Hidden Native Input */}
                  <input
                    type="radio"
                    name="defaultSku"
                    value={row.sku}
                    checked={formik.values.defaultSku === row.sku}
                    onChange={() => formik.setFieldValue('defaultSku', row.sku)}
                    className="form-radio absolute z-10 opacity-0 visibility-hidden cursor-pointer"
                  />

                  {/* Custom Styled Radio */}
                  <div
                    className={`absolute w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all
          ${formik.values.defaultSku === row.sku ? 'border-primary-500' : 'border-gray-200'}`}
                  >
                    {formik.values.defaultSku === row.sku && (
                      <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                    )}
                  </div>
                </div>
              ),
            },
          ];

          return (
            <Form onSubmit={formik.handleSubmit}>
              <div className="flex items-center justify-between mb-6">
                <button className="flex items-center gap-2 text-dark-500">
                  <span className="text-lg font-semibold">
                    Configure Pricing & Inventory
                  </span>
                </button>
                <div className="flex gap-2">
                  <button className="btn btn-gray" onClick={onClose}>
                    Cancel
                  </button>
                  <button
                    onClick={() => {
                      outerFormik.setFieldValue('variations', formik.values);
                    }}
                    type="submit"
                    className="btn btn-primary"
                  >
                    Save
                  </button>
                </div>
              </div>

              <div className="card !p-0 overflow-hidden">
                <div className="p-4 border-b border-border-color">
                  <div className="grid grid-cols-3 gap-x-4">
                    <InputField
                      type="text"
                      id="basePrice"
                      label="Base Price"
                      name="basePrice"
                      placeholder="0.00"
                      rightText="$"
                      rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                      inputClassName="form-control !pl-7 !pr-4"
                      formik={formik}
                    />

                    {formik.values.chargeTax && (
                      <>
                        <div className="flex flex-col mb-5">
                          <span className="form-label">Taxation Type</span>
                          <SelectField
                            name="taxationType"
                            id="taxationType"
                            placeholder="Select option"
                            className="single-select"
                            options={[
                              { value: 'gst', label: 'GST' },
                              { value: 'vat', label: 'VAT' },
                            ]}
                            formik={formik}
                          />
                          {formik.touched.taxationType &&
                            formik.errors.taxationType && (
                              <span className="text-danger-500 text-xs mt-1">
                                {formik.errors.taxationType}
                              </span>
                            )}
                        </div>
                        <InputField
                          type="text"
                          id="priceAfterTax"
                          label="Price after tax"
                          name="priceAfterTax"
                          placeholder="0.00"
                          rightText="$"
                          rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                          inputClassName="form-control !pl-7 !pr-4"
                          formik={formik}
                        />
                      </>
                    )}
                  </div>

                  <div className="flex items-center gap-4 w-full">
                    <CustomCheckbox
                      id="chargeTax"
                      name="chargeTax"
                      label="Charge tax on this product"
                      checked={formik.values.chargeTax}
                      onChange={(e) => {
                        formik.setFieldValue('chargeTax', e.target.checked);
                      }}
                    />

                    <CustomCheckbox
                      id="sameForAll"
                      name="sameForAll"
                      label="Same for all"
                      checked={sameForAll}
                      onChange={(e) => {
                        setSameForAll(e.target.checked);
                      }}
                    />
                  </div>
                </div>
                <DataTable
                  columns={columns}
                  data={tableData}
                  customStyles={customStyles}
                  fixedHeader
                  className="custom-table auto-height-table configure-table"
                />
                <BaseOffCanvas
                  isOpen={isAddTierOpen}
                  onClose={() => setIsAddTierOpen(false)}
                  title="Add Tier Pricing"
                >
                  <div className="flex items-center justify-between mb-6">
                    <button className="flex items-center gap-2 text-dark-500">
                      <span className="text-lg font-semibold">
                        Add Tier Pricing
                      </span>
                    </button>
                    <div className="flex gap-2">
                      <button
                        className="btn btn-gray"
                        onClick={() => setIsAddTierOpen(false)}
                      >
                        Cancel
                      </button>
                      <button
                        className="btn btn-primary"
                        onClick={() => {
                          if (tierPricingRef.current) {
                            tierPricingRef.current.saveTiers();
                          }
                        }}
                      >
                        Save
                      </button>
                    </div>
                  </div>
                  <TierPricingContent
                    ref={tierPricingRef}
                    selctedTierId={selctedTier}
                  />
                </BaseOffCanvas>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default ConfigurePricing;
