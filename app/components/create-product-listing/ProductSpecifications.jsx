import React, { useState } from 'react';
import ImageUpload from '../Inputs/ImageUpload';
import { useFormikContext } from 'formik';

function ProductSpecifications({ submitAttempted }) {
  const formik = useFormikContext();
  const [images, setImages] = useState([]);
  const showError = submitAttempted && images.length === 0;

  return (
    <div className="bg-white border border-border-color rounded-xl">
      <div className="flex items-center gap-3 p-4">
        <h2 className="text-sm font-bold">
          Product Specifications{' '}
          <span className="text-dark-500/40 text-sm font-medium ml-0.5">
            (Optional)
          </span>
        </h2>
      </div>

      <div className="space-y-4 p-4 pt-2">
        <ImageUpload
          images={images}
          setImages={setImages}
          maxFiles={5}
          maxSize={3000000}
          heading={null}
          showError={showError}
        />
      </div>
    </div>
  );
}

export default ProductSpecifications;
