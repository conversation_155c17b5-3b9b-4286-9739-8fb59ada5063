import React, { useEffect } from 'react';
import SelectField from '../Inputs/SelectField';
import AttributeField from '../Inputs/AttributeField';

const DefineAttributes = ({
  formik,
  attributesList = [],
  handleAddNewValue,
  setShowConfirmPopup,
  setPendingAttributeChange,
}) => {
  const colorData = {
    Black: '#000000',
    Blue: '#005BD3',
    Multi: '#CA06D4',
    Red: '#F61F1F',
    Purple: '#CA06D4',
  };

  useEffect(() => {
    if (formik.values.attributeType === 'Required') {
      const allAttrs = attributesList.map((e) => ({
        label: e.name,
        value: e.id,
      }));

      formik.setFieldValue('selectedAttributes', allAttrs);
      // formik.setFieldValue('selectedVariantsAttributes', allAttrs);

      const currentValues = formik.values.selectedAttributesValues || [];
      const updatedAttrValues = allAttrs.map((attr) => {
        const existing = currentValues.find(
          (item) => item.name?.value === attr.value
        );
        return (
          existing || {
            name: { label: attr.label, value: attr.value },
            values: [],
          }
        );
      });

      formik.setFieldValue('selectedAttributesValues', updatedAttrValues);
    }
  }, [formik.values.attributeType, attributesList]);

  return (
    <div className="card !p-0 relative">
      <div className="p-4">
        <div className="flex flex-col">
          <span className="form-label">
            Select attributes to make variants
          </span>
          <SelectField
            isMulti
            className="single-select multi-select"
            placeholder="Find and select attributes..."
            options={attributesList.map((e) => ({
              label: e.name,
              value: e.id,
            }))}
            isDisabled={formik.values.attributeType === 'Required'} // 🔒 Disable if Required
            value={
              formik.values.attributeType === 'Required'
                ? attributesList.map((e) => ({
                    label: e.name,
                    value: e.id,
                  }))
                : Array.from(
                    new Map(
                      formik.values.selectedAttributes.map((idObj) => {
                        const id = idObj?.value || idObj;
                        const match = attributesList.find((e) => e.id === id);

                        return [
                          id,
                          match
                            ? { label: match.name, value: match.id }
                            : { label: '', value: id },
                        ];
                      })
                    ).values()
                  )
            }
            onChange={(selectedOptions) => {
              const selected = selectedOptions || [];
              if (
                selected.length === 0 &&
                (formik.values.productVariants?.length || 0) > 0
              ) {
                setShowConfirmPopup(true);
                setPendingAttributeChange(formik?.values?.attributeType);
                return;
              }

              const updatedVariantAttrs =
                formik.values.selectedAttributes.filter((variantAttr) =>
                  selected.find((sel) => sel.value === variantAttr.value)
                );

              formik.setFieldValue('selectedAttributes', selected);
              formik.setFieldValue(
                'selectedVariantsAttributes',
                updatedVariantAttrs
              );

              const currentAttrValues =
                formik.values.selectedAttributesValues || [];

              const updatedAttrValues = currentAttrValues.filter((attrValue) =>
                selected.some((sel) => sel.value === attrValue.name?.value)
              );

              const newAttrValues = selected
                .filter(
                  (sel) =>
                    !updatedAttrValues.find(
                      (item) => item.name?.value === sel.value
                    )
                )
                .map((sel) => ({
                  name: { label: sel.label, value: sel.value },
                  values: [],
                }));

              formik.setFieldValue('selectedAttributesValues', [
                ...updatedAttrValues,
                ...newAttrValues,
              ]);

              if (selected.length > 0) {
                formik.setFieldTouched('selectedAttributes', false);
                formik.setFieldError('selectedAttributes', undefined);
              }
            }}
            isSearchable
            closeMenuOnSelect
            defaultMenuIsOpen={false}
            marginBottom="mb-4"
            formik={formik}
          />
        </div>
        {formik.touched.selectedAttributes &&
          formik.errors.selectedAttributes && (
            <div className="text-danger-500 text-xs mt-1">
              {formik.errors.selectedAttributes}
            </div>
          )}

        {(formik.values.selectedAttributes || []).map((selected, index) => {
          const attr = attributesList.find((a) => a.id === selected.value);
          if (!attr) return null;

          const attributeFieldValue =
            formik.values.selectedAttributesValues.find(
              (item) => item.name?.value === attr.id
            )?.values || [];

          return (
            <div
              key={attr.id}
              className="border border-border-color rounded-xl mt-4 mb-3"
            >
              <div className="flex items-center justify-between px-4 py-2.5">
                <h4 className="text-sm font-bold">{attr.name}</h4>
                <div className="flex items-center gap-3">
                  <button
                    className="btn btn-outline-gray inline-flex items-center gap-2"
                    onClick={(e) => {
                      e?.preventDefault();
                      handleAddNewValue(attr.type, attr.id, formik);
                    }}
                  >
                    Add new value
                  </button>
                  <button
                    className="flex items-center justify-center h-8 w-8 text-danger-500 border border-border-color hover:bg-danger-500 hover:text-white hover:border-danger-500 transition-base shadow-[0px_1px_0px_0px_#1A1A1A12] rounded-lg cursor-pointer"
                    onClick={() => {
                      // Remove the attribute from the selected attributes list
                      const updatedAttributes =
                        formik.values.selectedAttributes.filter(
                          (e) => e.value !== attr.id
                        );
                      formik.setFieldValue(
                        'selectedAttributes',
                        updatedAttributes
                      );

                      // Remove the attribute's values from selectedAttributesValues
                      const updatedAttrValues =
                        formik.values.selectedAttributesValues?.filter(
                          (v) => v.name?.value !== attr.id
                        ) || [];
                      formik.setFieldValue(
                        'selectedAttributesValues',
                        updatedAttrValues
                      );

                      // Remove the attribute from selectedVariantsAttributes (the key feature here)
                      const updatedVariantsAttributes =
                        formik.values.selectedVariantsAttributes?.filter(
                          (variantAttr) => variantAttr.value !== attr.id
                        ) || [];
                      formik.setFieldValue(
                        'selectedVariantsAttributes',
                        updatedVariantsAttributes
                      );
                    }}
                  >
                    <span className="icon icon-trash" />
                  </button>
                </div>
              </div>

              <AttributeField
                id="selectedAttributesValues"
                name="selectedAttributesValues"
                type={attr.name === 'Color' ? 'color' : 'default'}
                options={attr.options}
                colorData={colorData}
                value={attributeFieldValue}
                onChange={(newValues) => {
                  const existing = formik.values.selectedAttributesValues || [];
                  const updated = existing.map((item) =>
                    item.name?.value === attr.id
                      ? { ...item, values: [...newValues] }
                      : item
                  );
                  formik.setFieldValue('selectedAttributesValues', updated);
                }}
                formik={formik}
                marginBottom="mb-0 p-4"
                isMulti
              />
              {formik.touched.selectedAttributesValues?.[index]?.values &&
                formik.errors.selectedAttributesValues?.[index]?.values && (
                  <span className="text-danger-500 text-xs mt-1">
                    {formik.errors.selectedAttributesValues[index].values}
                  </span>
                )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default DefineAttributes;