import React, { useState, useEffect, useRef } from 'react';
import { useFormik, useFormikContext } from 'formik';
import * as Yup from 'yup';
import InputField from '../Inputs/InputField';

import { Formik, Form } from 'formik';
import { forwardRef, useImperativeHandle } from 'react';
import { showSuccessToast } from '@/utils/function';

const TierPricingContent = forwardRef(({ selctedTierId }, ref) => {
  const outerFormik = useFormikContext();

  const [tiers, setTiers] = useState([]);
  const [isAddingTier, setIsAddingTier] = useState(false);
  const [editingTier, setEditingTier] = useState(null);
  const tierPricingRef = useRef();

  useEffect(() => {
    const selectedVariant = outerFormik?.values?.variants?.find(
      (v) => v.id === selctedTierId
    );
    setTiers(selectedVariant?.tiers || []);
  }, [selctedTierId, outerFormik?.values?.variants]);

  useEffect(() => {
    if (tiers.length === 0) {
      setIsAddingTier(true);
    } else {
      setIsAddingTier(false);
    }
  }, [tiers]);

  const saveTiers = () => {
    const updatedVariants = outerFormik?.values?.variants.map((variant) => {
      if (variant.id === selctedTierId) {
        return {
          ...variant,
          tiers: tiers, // set the local tiers state here
        };
      }
      return variant;
    });

    outerFormik.setFieldValue('variants', updatedVariants);
    showSuccessToast('Tier pricing added successfully');
  };

  // Expose saveTiers to the parent using the ref
  useImperativeHandle(ref, () => ({
    saveTiers,
  }));

  const handleValidation = async (formik) => {
    const errors = await formik.validateForm();
    if (Object.keys(errors).length === 0) {
      await formik.submitForm();
    } else {
      formik.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
    }
  };

  const tierValidationSchema = Yup.object({
    title: Yup.string().required('Tier name is required'),
    minQuantity: Yup.number()
      .typeError('Minimum quantity must be a number')
      .required('Minimum quantity is required')
      .min(1),
    maxQuantity: Yup.number()
      .typeError('Maximum quantity must be a number')
      .required('Maximum quantity is required')
      .moreThan(Yup.ref('minQuantity'), 'Max must be greater than Min')
      .test(
        'no-overlap',
        'This range overlaps with another tier',
        function (value) {
          const { minQuantity } = this.parent;
          const editingId = editingTier?.id;
          const currentMin = Number(minQuantity);
          const currentMax = Number(value);

          if (isNaN(currentMin) || isNaN(currentMax)) return true;

          for (let tier of tiers) {
            if (tier.id === editingId) continue;
            const tierMin = Number(tier.minQuantity);
            const tierMax = Number(tier.maxQuantity);
            if (currentMax >= tierMin && currentMin <= tierMax) return false;
          }
          return true;
        }
      )
      .test(
        'continuous-range',
        'Range must continue from the previous tier without gaps',
        function (value) {
          const { minQuantity } = this.parent;
          const editingId = editingTier?.id;
          const currentMin = Number(minQuantity);
          const currentMax = Number(value);

          if (isNaN(currentMin) || isNaN(currentMax)) return true;

          const sortedTiers = [...tiers]
            .filter((t) => t.id !== editingId)
            .sort((a, b) => a.minQuantity - b.minQuantity);

          const previousTier = sortedTiers
            .filter((t) => t.maxQuantity < currentMin)
            .pop();
          const nextTier = sortedTiers.find((t) => t.minQuantity > currentMax);

          if (
            previousTier &&
            currentMin !== Number(previousTier.maxQuantity) + 1
          )
            return false;
          if (nextTier && currentMax >= nextTier.minQuantity) return false;
          if (nextTier && nextTier.minQuantity - currentMax !== 1) return false;

          return true;
        }
      ),
    price: Yup.number()
      .typeError('Price must be a number')
      .required('Price is required')
      .min(0),
  });

  const getNextMinQuantity = () => {
    if (tiers.length === 0) return 1;
    const lastTier = tiers[tiers.length - 1];
    return Number(lastTier.maxQuantity) + 1;
  };

  const AddTier = (setValues) => {
    setEditingTier(null);
    setIsAddingTier(true);
    setValues({
      title: '',
      minQuantity: getNextMinQuantity(),
      maxQuantity: '',
      price: '',
    });
  };

  return (
    <Formik
      initialValues={{
        title: '',
        minQuantity: 1,
        maxQuantity: '',
        price: '',
      }}
      enableReinitialize
      validationSchema={tierValidationSchema}
      onSubmit={(values, { resetForm }) => {
        const tierId = editingTier ? editingTier.id : tiers.length;
        const newTier = { ...values, id: tierId };

        if (editingTier) {
          const updatedTiers = tiers.map((t) =>
            t.id === editingTier.id ? newTier : t
          );
          setTiers(updatedTiers);
        } else {
          setTiers((prev) => [...prev, newTier]);
        }

        setIsAddingTier(false);
        setEditingTier(null);
        resetForm();
      }}
    >
      {(formik) => (
        <div className="bg-white border border-border-color rounded-xl !p-0 mt-4">
          {tiers.map((tier) => (
            <div
              key={tier.id}
              className={`flex gap-4 items-start !p-4 not-[:first-child]:border-t not-[:last-child]:border-border-color ${
                !isAddingTier ? '' : 'border-b border-border-color'
              }`}
            >
              <span className="icon icon-stack text-gray-200 text-xl mt-3" />
              <div className="flex flex-col justify-between w-full">
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <h4 className="text-sm font-bold">{tier.title}</h4>
                    <div className="flex items-center gap-1 text-[13px]">
                      <span className="text-dark-500">
                        {tier.minQuantity} - {tier.maxQuantity}
                      </span>
                      <span className="text-dark-500/10">|</span>
                      <span className="text-dark-500">${tier.price}/unit</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      type="button"
                      onClick={() => {
                        setEditingTier(tier);
                        setIsAddingTier(true);
                        formik.setValues({
                          title: tier.title,
                          minQuantity: tier.minQuantity,
                          maxQuantity: tier.maxQuantity,
                          price: tier.price,
                        });
                      }}
                      className="w-8 h-8 flex items-center justify-center text-xl rounded-lg text-dark-500 hover:bg-gray-500/10 hover:text-gray-500 transition-base cursor-pointer"
                    >
                      <span className="icon icon-pencil-line" />
                    </button>
                    <button
                      type="button"
                      onClick={() =>
                        setTiers((prev) => prev.filter((t) => t.id !== tier.id))
                      }
                      className="w-8 h-8 flex items-center justify-center text-xl rounded-lg text-dark-500 hover:bg-danger-500/10 hover:text-danger-500 transition-base cursor-pointer"
                    >
                      <span className="icon icon-trash" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}

          {(tiers.length === 0 || isAddingTier) && (
            <div className="flex gap-4 items-start !p-4">
              <span className="icon icon-stack text-gray-200 text-xl mt-3" />
              <Form onSubmit={formik.handleSubmit} className="flex-1">
                <InputField
                  label="Tier Name"
                  id="title"
                  name="title"
                  type="text"
                  placeholder="Enter tier name"
                  formik={formik}
                />
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="form-label mb-2 block">
                      Quantity Range
                    </label>
                    <div className="grid grid-cols-2 gap-4">
                      <InputField
                        name="minQuantity"
                        id="minQuantity"
                        type="number"
                        rightText="Min"
                        marginBottom="mb-0"
                        inputClassName="form-control"
                        value={
                          formik.values.minQuantity ||
                          formik.initialValues.minQuantity
                        }
                        readOnly
                        disabled
                        formik={formik}
                      />
                      <InputField
                        id="maxQuantity"
                        name="maxQuantity"
                        type="number"
                        rightText="Max"
                        marginBottom="mb-0"
                        inputClassName="form-control !pr-12"
                        formik={formik}
                      />
                    </div>
                  </div>
                  <InputField
                    label="Price/Item"
                    id="price"
                    name="price"
                    type="number"
                    placeholder="0.00"
                    rightText="$"
                    marginBottom="mb-0"
                    rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                    inputClassName="form-control !pl-7 !pr-4"
                    formik={formik}
                  />
                </div>
                <div className="mt-4 flex justify-end gap-3">
                  <button
                    type="button"
                    onClick={() => {
                      setIsAddingTier(false);
                      setEditingTier(null);
                      formik.resetForm();
                    }}
                    className="btn btn-outline-danger"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={() => handleValidation(formik)}
                    className="btn"
                  >
                    {editingTier ? 'Save' : 'Add Tier'}
                  </button>
                </div>
              </Form>
            </div>
          )}

          {tiers.length > 0 && (
            <div className="py-3 px-4 border-t border-border-color">
              <button
                type="button"
                className="flex items-center gap-2 cursor-pointer group"
                onClick={() => AddTier(formik.setValues)}
              >
                <span className="icon icon-plus-circle group-hover:text-primary-500 transition-base" />
                <span className="text-[13px] font-semibold group-hover:text-primary-500 transition-base">
                  Add another tier
                </span>
              </button>
            </div>
          )}
        </div>
      )}
    </Formik>
  );
});

export default TierPricingContent;
