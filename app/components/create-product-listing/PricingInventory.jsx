'use client';
import React, { useState } from 'react';
import PricingInventoryTable from '../table/PricingInventoryTable';
import ConfigurePricing from '../create-product-listing/ConfigurePricing';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import { useFormikContext } from 'formik';

const PricingInventory = () => {
  const formik = useFormikContext();

  const [isConfigurePricingOpen, setIsConfigurePricingOpen] = useState(false);

  const sampleData =
    formik?.values?.variations?.variants?.map((e) => {
      return {
        sku: e.sku,
        attriutes: ['Black', 'Leather'],
        price: e.finalPrice,
        quantity: e.quantity,
        tierPricing: e.tierPricing,
        isDefault: formik?.values?.variations?.defaultSku == e.sku,
      };
    }) || [];
  return (
    <>
      <div className="bg-white border border-border-color rounded-xl mb-4">
        <div className="flex items-center justify-between gap-2 p-4">
          <h2 className="text-sm font-bold">
            Pricing and Inventory Management
          </h2>

          <button
            type="button"
            className="flex justify-center items-center font-semibold bg-white rounded-lg border-2 text-[13px] border-border-color py-1 px-4 hover:bg-dark-500/10 cursor-pointer transition-base"
          >
            Edit Details
          </button>
        </div>

        <div className="p-4">
          <div className="border border-dashed border-border-color rounded-lg py-10 2xl:py-[74px] px-9">
            <button
              type="button"
              onClick={() => setIsConfigurePricingOpen(true)}
              className="flex justify-center items-center justify-self-center bg-white rounded-lg border-2 text-[13px] border-border-color py-1 px-4 hover:bg-dark-500/10 cursor-pointer transition-base"
            >
              <span className="icon icon-plus text-lg mr-2" />
              Add Pricing and Inventory for variants
            </button>
          </div>
        </div>

        <PricingInventoryTable data={sampleData} />
      </div>

      {/* OffCanvas modal */}
      <BaseOffCanvas
        isOpen={isConfigurePricingOpen}
        onClose={() => setIsConfigurePricingOpen(false)}
        // title="Configure Pricing"
      >
        <ConfigurePricing onClose={() => setIsConfigurePricingOpen(false)} />
      </BaseOffCanvas>
    </>
  );
};

export default PricingInventory;
