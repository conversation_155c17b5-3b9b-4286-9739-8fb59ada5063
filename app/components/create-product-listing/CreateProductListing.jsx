'use client';
import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { useDispatch } from 'react-redux';
import { resetProductState } from '@/store/actions/userActions';


const ProductListingOptions = () => {
  const dispatch = useDispatch()
  useEffect(()=>{
    dispatch(resetProductState())
  },[])

  const [selectedOption, setSelectedOption] = useState('blank');

  const options = [
    {
      id: 'blank',
      icon: 'icon-file-text',
      title: 'Blank Form',
      description: 'Fill out the product listing form at your own pace. This option is ideal for carefully crafting individual products, allowing you to add detailed descriptions, customize pricing, manage inventory, and ensure each listing meets your exact standards.'
    },
    {
      id: 'bulk',
      icon: 'icon-files',
      title: 'Bulk Upload',
      description: 'Upload multiple products at once using our easy-to-use spreadsheet template. Perfect for vendors managing large catalogs, map your fields — helping you save time and streamline your listings.'
    },
    {
      id: 'set',
      icon: 'icon-package-lined',
      title: 'Product Set',
      description: 'A grouped offering of multiple products sold exclusively as a single unit. Items in a Product Set cannot be purchased individually, making it ideal for curated kits or wholesale packages.'
    },
    {
      id: 'combo',
      icon: 'icon-stack',
      title: 'Value Combo',
      description: 'A curated combination of complementary products offered together for added value. Each item in a Value Combo can also be sold separately, providing flexibility in promotions or upsells.'
    }
  ];

  return (
    <div className="max-w-[900px] mx-auto pt-[120px] pb-8 px-4">
      <div className="text-left mb-4">
        <h2 className="text-3xl font-bold mb-1">Create your product listings</h2>
        <p className="text-sm text-gray-500">Choose how you'd like to add your products and begin building your catalog</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-1 bg-gray-500/5 rounded-xl p-1">
        {options.map((option) => (
          <div
            key={option.id}
            onClick={() => setSelectedOption(option.id)}
            className={`relative p-4 text-gray-500 rounded-xl border cursor-pointer transition-all duration-200 ${
              selectedOption === option.id
                ? 'border-primary-500 text-primary-500 bg-white'
                : 'border-border-color hover:border-primary-500 hover:text-primary-500 hover:bg-white'
            }`}
          >
            <div className="flex flex-col items-center text-center gap-2">
              <span className={`icon ${option.icon} text-2xl ${
                selectedOption === option.id ? 'text-primary-500' : 'text-gray-400'
              }`} />
              <span className="font-semibold text-sm">{option.title}</span>
            </div>
          </div>
        ))}
      </div>

      {selectedOption === 'blank' && (
        <div className="card flex flex-col mt-4 !p-6">
          <p className="text-sm text-dark-500 mb-6">{options[0].description}</p>
          <Link 
            href={`/manage-products/product/${"create"}`}
            className="btn btn-primary ml-auto"
          >
            Start with blank form
          </Link>
        </div>
      )}

      {selectedOption === 'bulk' && (
        <div className="card flex flex-col mt-4 !p-6">
          <p className="text-sm text-dark-500 mb-6">{options[1].description}</p>
          <div className="flex items-center justify-between">
            <div className='flex items-center gap-2'>
              <Link href="" className="text-sm text-secondary-500 underline font-semibold hover:no-underline transition-base">Download Simple Product CSV</Link>
              <span className="font-semibold text-gray-200 text-sm">or</span>
              <Link href="" className="text-sm text-secondary-500 underline font-semibold hover:no-underline transition-base">Explore other templates</Link>
            </div>
            <Link 
              href="/manage-products/bulk-upload"
              className="btn btn-primary ml-auto"
            >
              Upload CSV
            </Link>
          </div>
        </div>
      )}
      {selectedOption === 'set' && (
        <div className="card flex flex-col mt-4 !p-6">
          <p className="text-sm text-dark-500 mb-6">{options[2].description}</p>
          <Link 
            href="/manage-products/product-set"
            className="btn btn-primary ml-auto"
          >
            Create Product Set
          </Link>
        </div>
      )}
      {selectedOption === 'combo' && (
        <div className="card flex flex-col mt-4 !p-6">
          <p className="text-sm text-dark-500 mb-6">{options[3].description}</p>
          <Link 
            href="/manage-products/value-combo"
            className="btn btn-primary ml-auto"
          >
            Build Value Combo
          </Link>
        </div>
      )}

      <p className="text-sm text-gray-300 mt-8 text-center">
        Note: Not sure which method suits you? You can always start with a blank form and switch to bulk upload later
      </p>
    </div>
  );
};

export default ProductListingOptions;