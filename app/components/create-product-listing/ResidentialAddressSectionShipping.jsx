import { useLoadScript, Autocomplete } from '@react-google-maps/api';
import {  useEffect, useState } from 'react';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import { usaCityOptions, usaStateOptions } from '@/utils/constant';
import { Form, Formik, useFormikContext } from 'formik';
import { RESIDENTIAL_ADDRESS_SECTION } from '@/utils/schema';
import { useDispatch } from 'react-redux';
import { createPickupAddress, getPickupAddress } from '@/store/api/productApi';

const GOOGLE_MAPS_API_KEY = ''; // Replace with your key
const libraries = ['places'];

const AddressAutocomplete = () => {
  const [autocomplete, setAutocomplete] = useState(null);
  const formik = useFormikContext()

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: GOOGLE_MAPS_API_KEY,
    libraries,
  });

  const onLoad = (autocompleteInstance) => {
    setAutocomplete(autocompleteInstance);
  };

  const onPlaceChanged = () => {
    if (!autocomplete) return;

    const place = autocomplete.getPlace();
    if (!place.address_components) return;

    let address = place.formatted_address;
    let city = '';
    let state = '';
    let postalCode = '';

    place.address_components.forEach((component) => {
      if (component.types.includes('locality')) {
        city = component.long_name;
      }
      if (component.types.includes('administrative_area_level_1')) {
        state = component.long_name;
      }
      if (component.types.includes('postal_code')) {
        postalCode = component.long_name;
      }
    });

    // Update Formik values
    formik.setValues({
      ...formik.values,
      address,
      city,
      state,
      postalCode,
    });
  };

  return (
    <>
      {/* {isLoaded && (
         <Autocomplete onLoad={onLoad} onPlaceChanged={onPlaceChanged}   options={{ componentRestrictions: { country: 'us' } }}> */}
      <InputField
        id="address"
        name="address"
        type="text"
        label="Address"
        placeholder="Enter address"
        required={false}
        value={formik.values.address}
        formik={formik}
      />
      {/* </Autocomplete>
      )} */}
    </>
  );
};

export default function ResidentialAddressSectionShipping({
  formikRef,
  className = 'mb-4',
  title = 'Residential Address',
  showTitle = true,
  getAddress
}) {

  const dispatch = useDispatch()

  return (
    <Formik
      innerRef={formikRef}
      initialValues={{
        address: '',
        city: '',
        state: '',
        postalCode: '',
        locationLabel:''
      }}
      validationSchema={RESIDENTIAL_ADDRESS_SECTION}
      validateOnChange={true}
      validateOnBlur={true}
      enableReinitialize={true}
      onSubmit={async (values) => {
        const { payload } = await dispatch(
          createPickupAddress({
            address_type: values?.locationLabel,
            address_line_1: values?.address,
            city: values?.city,
            country: 'USA',
            state: values?.state,
            postal_code: values?.postalCode,
          })
        );

        if (payload.status || payload.ok) {
        getAddress()
        }
        
      }}
    >
      {(formik) => {
        return (
          <Form onSubmit={formik.handleSubmit}>
            <div className={`card !p-0 ${className}`}>
              {showTitle && title && (
                <div className="flex items-center gap-3 p-4">
                  <h4 className="text-base font-bold">{title}</h4>
                </div>
              )}

              <div className="grid grid-cols-1 xl:grid-cols-3 gap-x-4 p-4">
                <div className="col-span-full">
                  <AddressAutocomplete formik={formik} />
                </div>
                <div className="flex flex-col mb-4 xl:mb-0">
                  <span className="form-label">
                    City / Town
                  </span>
                  <SelectField
                    className="single-select"
                    name="city"
                    placeholder="Enter city / town"
                    // isDisabled = {formik.values.city ? false :true}
                    options={
                      // { value: formik.values.city, label: formik.values.city }
                      usaCityOptions
                    }
                    formik={formik}
                  />
                  {formik?.touched?.city && formik?.errors?.city && (
                    <div className="text-danger-500 text-xs mt-1">
                      {formik?.errors?.city}
                    </div>
                  )}
                </div>

                <div className="flex flex-col mb-4 xl:mb-0">
                  <span className="form-label">
                    State / Region
                  </span>
                  <SelectField
                    className="single-select"
                    name="state"
                    placeholder="Enter state / region"
                    // isDisabled = {formik.values.state ? false :true}
                    options={
                      // { value: formik.values.state, label: formik.values.state },
                      usaStateOptions
                    }
                    formik={formik}
                  />
                  {formik?.touched?.state && formik?.errors?.state && (
                    <div className="text-danger-500 text-xs mt-1">
                      {formik?.errors?.state}
                    </div>
                  )}
                </div>

                <div className="flex flex-col mb-4 xl:mb-0">
                  <InputField
                    id="postalCode"
                    name="postalCode"
                    type="text"
                    label="ZIP / Postal Code"
                    placeholder="Enter postal code"
                    marginBottom="mb-0"
                    inputClassName="form-control w-full"
                    required={false}
                    onBlur={formik.handleBlur}
                    value={formik.values.postalCode}
                    formik={formik}
                  />
                </div>
                <div className="flex flex-col mb-4 xl:mb-0">
                  <InputField
                    id="locationLabel"
                    name="locationLabel"
                    type="text"
                    label="Location Label"
                    placeholder="Enter location label"
                    marginBottom="mb-0"
                    inputClassName="form-control w-full"
                    required={false}
                    onBlur={formik.handleBlur}
                    value={formik.values.locationLabel}
                    formik={formik}
                  />
                </div>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}