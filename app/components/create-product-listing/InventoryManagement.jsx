import React from 'react';
import InputField from '../Inputs/InputField';
import <PERSON>Field from '../Inputs/SelectField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import { Tooltip } from 'react-tooltip';
import { useFormikContext } from 'formik';

const InventoryManagement = () => {
  const formik = useFormikContext();
  return (
    <div className="bg-white border border-border-color rounded-xl mb-4">
      <div className="flex items-center gap-3 p-4">
        <h2 className="text-sm font-bold">Inventory Management</h2>
      </div>

      <div className="space-y-4 p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-0">
          <InputField
            id="sku"
            name="sku"
            type="text"
            label="SKU (Stock Keeping Unit)"
            formik={formik}
          />

          <InputField
            id="totalQuantity"
            name="totalQuantity"
            type="number"
            label="Total Quantity in Stock"
            formik={formik}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-0">
          <InputField
            id="minOrderQuantity"
            name="minOrderQuantity"
            type="number"
            label="Minimum Order Quantity"
            formik={formik}
          />

          <InputField
            id="outOfStockThreshold"
            name="outOfStockThreshold"
            type="number"
            label="Out of Stock Threshold"
            formik={formik}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-0">
          <div className="flex flex-col">
            <span className="form-label">
              Backorders Allowed
            </span>
            <SelectField
              id="backordersAllowed"
              name="backordersAllowed"
              label="Backorders Allowed"
              className="single-select"
              placeholder={null}
              options={[
                { value: 'yes', label: 'Yes' },
                { value: 'no', label: 'No' },
              ]}
              formik={formik}
            />
            {formik?.touched?.backordersAllowed &&
              formik?.errors?.backordersAllowed && (
                <div className="text-danger-500 text-xs mt-1">
                  {formik?.errors?.backordersAllowed}
                </div>
              )}
          </div>

          <InputField
            id="restockingThreshold"
            name="restockingThreshold"
            type="number"
            marginBottom="mb-0"
            label="Restocking Threshold"
            formik={formik}
          />
        </div>
      </div>

      <div className="space-y-3">
        <div className="p-4 mb-0 border-t border-border-color">
          <div className="flex items-center gap-2">
            <CustomCheckbox
              id="negotiationEnabled"
              name="negotiationEnabled"
              label="Negotiation Enabled"
              checked={formik.values.negotiationEnabled}
              onChange={formik.handleChange}
            />
            <span
              className="icon icon-question text-gray-400 cursor-help"
              data-tooltip-id="negotiation-info"
            />
            <Tooltip
              id="negotiation-info"
              // place="top"
              effect="solid"
              className="!bg-white !opacity-100 !z-50 !text-black !shadow-lg !p-3 !rounded-xl"
            >
              <div className="flex justify-center items-center text-center max-w-[340px] text-dark-500 font-normal text-[13px]">
                <p>
                  Allow buyers to negotiate price or terms once they reach a
                  certain quantity threshold. Ideal for bulk purchases and
                  tier-based pricing.
                </p>
              </div>
            </Tooltip>
          </div>

          {/* Extra info with smooth transition */}
          <div
            className={`grid grid-cols-1 md:grid-cols-2 gap-4 w-full overflow-hidden transition-all duration-300 ease-in-out ${
              formik.values.negotiationEnabled
                ? 'max-h-[200px] opacity-100 pt-4'
                : 'max-h-0 opacity-0'
            }`}
          >
            <InputField
              id="negotiationQuantity"
              name="negotiationQuantity"
              type="number"
              marginBottom="mb-0"
              label="Negotiation Quantity"
              formik={formik}
            />

            <InputField
              id="autoAcceptThreshold"
              name="autoAcceptThreshold"
              type="number"
              marginBottom="mb-0"
              label="Auto-Accept Threshold"
              formik={formik}
            />
          </div>
        </div>

        <div className="flex items-center gap-2 p-4 mb-0 border-t border-border-color">
          <CustomCheckbox
            id="makeToOrder"
            name="makeToOrder"
            label="This product comes under make to order"
            checked={formik.values.makeToOrder}
            onChange={formik.handleChange}
          />
          <span
            className="icon icon-question text-gray-400 cursor-help"
            data-tooltip-id="make-to-order-info"
          />
          <Tooltip
            id="make-to-order-info"
            // place="top"
            effect="solid"
            className="!bg-white !opacity-100 !z-50 !text-black !shadow-lg !p-3 !rounded-xl"
          >
            <div className="flex justify-center items-center text-center max-w-[340px] text-dark-500 font-normal text-[13px]">
              <p>
                This product is not kept in ready stock. It will be manufactured
                or sourced only after an order is placed. Ideal for customized
                or special items.
              </p>
            </div>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default InventoryManagement;
