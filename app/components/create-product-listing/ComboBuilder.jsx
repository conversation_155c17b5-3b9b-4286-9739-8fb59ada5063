'use client';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { Formik, Field, Form } from 'formik';
import InputField from '../Inputs/InputField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import ComboBuilderTable from '../table/ComboBuilderTable';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import ProductSelectionTable from '../table/ProductSelectionTable';
import { getVariantsAsync } from '@/store/api/productApi';
import { COMBO_BUILDER_SCHEMA } from '@/utils/schema';
import { useDispatch } from 'react-redux';

const ComboBuilder = forwardRef(({ onSuccess, valueComboData }, ref) => {
  const formikRef = React.useRef();
  const dispatch = useDispatch();
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const [productListData, setProductListData] = useState([]);
  const [filterText, setFilterText] = useState('');
  const [debouncedText, setDebouncedText] = useState(filterText);
  const [selectedIds, setSelectedIds] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState(
    valueComboData?.products || []
  );
  useImperativeHandle(ref, () => ({
    submitForm: () => {
      formikRef.current?.handleSubmit();
    },
    // getValues: () => formikRef.current?.values,
    resetForm: () => {
      formikRef.current?.resetForm();
    },
  }));

  useEffect(() => {
    const timeout = setTimeout(() => {
      setDebouncedText(filterText);
    }, 300);
    return () => clearTimeout(timeout);
  }, [filterText]);

  useEffect(() => {
    if (debouncedText) getProducts();
  }, [debouncedText]);

  async function getProducts() {
    const { payload } = await dispatch(
      getVariantsAsync({
        ...(debouncedText ? { 'filter[search]': debouncedText } : {}),
      })
    );
    setProductListData(payload);
  }

  const handleSaveAndNext = (values) => {
    const payload = {
      display_individual_prices: values.DisplayIndividualPrices,
      price: parseFloat(values.costPrice),
      products: values.selectedProducts?.map((product) => ({
        product_id: product.product_id,
        product_sku_id: product.product_sku_id,
        qty: product.qty || 1,
        price: product?.sku_total_price,
      })),
    };
    if (onSuccess) onSuccess(payload);
  };

  return (
    <Formik
      innerRef={formikRef}
      enableReinitialize={true}
      initialValues={{
        id: valueComboData?.id || '',
        searchProductOuter: '',
        costPrice: valueComboData?.price || '',
        DisplayIndividualPrices:
          valueComboData?.display_individual_prices || false,
        selectedProducts:
          selectedProducts?.map((data) => ({
            product_id: data?.product_id || data?.product?.id,
            product_sku_id: data?.product_sku_id || data?.id,
            sku_lable:
              data?.sku_lable || data?.label || data?.product?.sku?.label,
            qty: data?.qty || 1,
            price: data?.price,
            sku_total_price: data?.sku_total_price || data?.price,
          })) || [],
      }}
      validationSchema={COMBO_BUILDER_SCHEMA}
      onSubmit={handleSaveAndNext}
      validateOnChange={true}
      validateOnBlur={true}
    >
      {({ getFieldProps, setFieldValue, values, isValid, errors }) => (
        <Form>
          {/* Combo Builder */}
          <div className="bg-white border border-border-color rounded-xl mb-4">
            <div className="flex items-center justify-between gap-3 p-4">
              <h2 className="text-base font-semibold">Combo Builder</h2>
              <span className="text-[13px] text-gray-300">
                Group products together to offer attractive deals
              </span>
            </div>
            <div className="space-y-4 xl:space-y-6 p-4 pt-2">
              <div className="flex items-start gap-4">
                <div className="w-full border-1 border-dashed border-border-color rounded-xl p-4 min-h-[100px] flex items-center justify-center hover:bg-primary-500/5 hover:border-dark-500 transition-base">
                  <button 
                    type="button"
                    onClick={() => setIsAddProductOpen(true)}
                    className="btn btn-outline-gray flex items-center gap-2"
                  >
                    <span className="icon icon-plus text-sm"></span>
                    Browse products
                  </button>
                </div>
              </div>
              {values?.selectedProducts?.length > 0 ? (
                <ComboBuilderTable
                  data={values?.selectedProducts}
                  formik={{ values, setFieldValue }}
                />
              ) : (
                <div className="text-gray-300 text-sm block text-center">No products to display. Browse our available products.</div>
              )}
            </div>
          </div>

          {/* Pricing */}
          <div className="bg-white border border-border-color rounded-xl mb-4">
            <div className="flex items-center gap-3 p-4">
              <h2 className="text-base font-semibold">Pricing</h2>
            </div>

            <div className="space-y-4 xl:space-y-6 p-4 pt-2">
              <div className="flex items-start gap-4">
                <div className="flex-1 xl:flex-[0_0_264px]">
                  <InputField
                    id="costPrice"
                    name="costPrice"
                    type="number"
                    label="Cost Price"
                    placeholder="0.00"
                    marginBottom="mb-0"
                    rightText="$"
                    rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                    inputClassName="form-control !pl-7 !pr-4"
                    {...getFieldProps('costPrice')}
                  />
                  {errors.costPrice && (
                    <span className="text-danger-500 text-xs mt-1">
                      {errors.costPrice}
                    </span>
                  )}
                </div>

                <CustomCheckbox
                  id="DisplayIndividualPrices"
                  name="DisplayIndividualPrices"
                  label="Display Individual Prices"
                  checked={values.DisplayIndividualPrices}
                  onChange={(e) =>
                    setFieldValue('DisplayIndividualPrices', e.target.checked)
                  }
                  className="mt-8"
                />
              </div>
            </div>
          </div>

          {/* Add product */}
          <BaseOffCanvas
            isOpen={isAddProductOpen}
            onClose={(e) => {
              e?.preventDefault();
              setIsAddProductOpen(false);
              setFilterText('');
              setIsAddProductOpen(false);
            }}
            title="Add Product"
            size="lg"
          >
            <div className="flex items-center justify-between mb-6">
              <button
                className="btn btn-gray"
                onClick={(e) => {
                  e?.preventDefault();
                  setIsAddProductOpen(false);
                  setFilterText('');
                  setIsAddProductOpen(false);
                }}
              >
                Cancel
              </button>
              <button
                className="btn btn-primary"
                onClick={(e) => {
                  e.preventDefault();
                  const updatedProducts = [...values.selectedProducts];
                  selectedIds.forEach((item) => {
                    if (
                      !updatedProducts.find((p) => p.product_sku_id === item.id)
                    ) {
                      updatedProducts.push({
                        product_id: item.product?.id,
                        product_sku_id: item.id,
                        sku_lable: item.label,
                        qty: 1,
                        price: item.price,
                        sku_total_price: item.price,
                      });
                    }
                  });
                  setSelectedProducts(updatedProducts);
                  setFieldValue('selectedProducts', updatedProducts);
                  setFilterText('');
                  setIsAddProductOpen(false);
                }}
              >
                Add Product
              </button>
            </div>
            <div className="card !p-0">
              <div className="flex items-center gap-3 p-4">
                <h2 className="text-base font-semibold">Add Product</h2>
              </div>

              <div className="flex flex-col gap-4 p-4 pt-2">
                <div className="flex-1">
                  <InputField
                    id="searchProduct"
                    name="searchProduct"
                    type="text"
                    label="Select Products to add to your combo"
                    placeholder="Search products by Product Title, SKU, ID"
                    marginBottom="mb-0"
                    leftIcon="icon-search"
                    value={filterText}
                    onChange={(e) => setFilterText(e.target.value)}
                    inputClassName="w-full form-control !rounded-lg !px-3"
                  />
                </div>

                {debouncedText ? (
                  productListData?.data?.length > 0 ? (
                    <ProductSelectionTable
                      setSelectedIds={setSelectedIds}
                      data={productListData?.data}
                      productCombosList={values.selectedProducts}
                    />
                  ) : (
                    <div className="text-gray-300 text-sm block text-center">
                      Oops! No products match your criteria.
                    </div>
                  )
                ) : null}
              </div>
            </div>
          </BaseOffCanvas>
        </Form>
      )}
    </Formik>
  );
});

export default ComboBuilder;
