import React, { useEffect, useRef, useState } from 'react';
import DataTable from 'react-data-table-component';
import InputField from '../Inputs/InputField';
import SelectField from '../Inputs/SelectField';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import TierPricingContent from '../create-product-listing/TierPricingConfiguration';
import { Form, Formik } from 'formik';
import { getPricingSchema } from '@/utils/schema';
import { useDispatch, useSelector } from 'react-redux';
import {
  getProductDetails,
  getTaxationTypes,
  submitPricing,
} from '@/store/api/productApi';
import { ProductType } from '@/utils/constant';

const Pricing = ({ navigationComponent, module = ProductType?.RETAIL }) => {
  const [isAddTierOpen, setIsAddTierOpen] = useState(false);
  const [selctedTier, setSelectedTier] = useState(null);
  const [taxationTypes, setTaxationTypes] = useState([]);
  const [tableData, setTableData] = useState([]);
  const { productDetails, productSetDetails, productId } = useSelector(
    (state) => state.product
  );
  const productData =
    module === ProductType?.RETAIL ? productDetails : productSetDetails;
  const dispatch = useDispatch();
  const tierPricingRef = useRef();
  const requireDefaultSku = tableData.length > 0 ? true : false;

  const VariantsData = productData?.variations;
  useEffect(() => {
    let data = [];

    if (VariantsData?.length == 0 && module === ProductType?.BUNDLE) {
      data = [
        {
          id: 1,
          sku: '',
          additionalPrice: 0,
          finalPrice: 0,
          quantity: '1',
          minOrderQty: '1',
          outOfStock: '',
          restocking: '1',
          tierPricing: false,
          tiers: [],
          backorder: '',
          defaultSku: '',
          attributes: '',
          label: '',
        },
      ];
    } else {
      data =
        VariantsData?.map((e) => ({
          id: e.id,
          sku: e.sku,
          additionalPrice: '0.00',
          finalPrice: '0.00',
          quantity: '1',
          minOrderQty: '1',
          outOfStock: e.qty,
          restocking: '1',
          tierPricing: false,
          tiers: [],
          backorder: '',
          defaultSku: e?.is_default ? e.id : '',
          attributes: e?.attributes?.map((attr) => attr?.value),
          label: e.label,
        })) || [];
    }

    setTableData(data);
    fetchTaxationTypes();
  }, []);

  const fetchTaxationTypes = async () => {
    const { payload } = await dispatch(getTaxationTypes());
    setTaxationTypes(
      payload?.data?.map((e) => {
        return { label: e.name, value: e.id };
      })
    );
  };

  function convertVariants(variants, defaultSkuId, skuLabel) {
    if (variants.length === 0) {
      return variants;
    }
    return variants.map((variant) => {
      const converted = {
        ...(VariantsData.length > 0 && { id: variant.id }),
        sku: module === ProductType?.RETAIL ? variant?.label : skuLabel,
        qty: variant.outOfStock,
        additional_price: variant.additionalPrice,
        is_default: defaultSkuId == variant.id ? 1 : 0,
        is_bulk_available: 1,
        tier_prices: [],
      };

      if (variant.tierPricing && Array.isArray(variant.tiers)) {
        converted.tier_prices = variant.tiers.map((tier) => ({
          bundle_name: tier.title,
          bundle_start_unit: tier.minQuantity,
          bundle_end_unit: tier.maxQuantity,
          price: tier.price,
        }));
      }

      return converted;
    });
  }

  const customStyles = {
    headRow: {
      style: {
        backgroundColor: '#f5f6f4',
        borderRadius: '0',
        fontWeight: '500',
        fontSize: '12px',
      },
    },
    headCells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    cells: {
      style: {
        paddingLeft: '8px',
        paddingRight: '8px',
      },
    },
    rows: {
      style: {
        minHeight: '62px',
      },
    },
  };
  return (
    <>
      <Formik
        initialValues={{
          skuLabel: VariantsData?.[0]?.label,
          taxationType: productData?.pricing?.taxation_type || '',
          priceAfterTax: productData?.pricing?.price_after_tax || '',
          chargeTax: productData?.pricing?.is_tax_charge || false,
          samePriceForAll: productData?.pricing?.is_price_same || false,
          costPrice: productData?.pricing?.base_price || '',
          negotiationAllowed:
            productData?.pricing?.is_negotiation_enabled || false,
          makeToOrder: productData?.pricing?.is_under_make_to_order || false,
          minimumOrderQuantity: productData?.pricing?.min_order_qty || '',
          outOfStockThreshold:
            productData?.pricing?.out_of_stock_threshold || '',
          restockingThreshold: productData?.pricing?.restocking_threshold || '',
          backordersAllowed:
            productData?.pricing?.is_backorder_allowed === true
              ? 'Yes'
              : productData?.pricing?.is_backorder_allowed === false
                ? 'No'
                : '',

          negotiationQuantity: productData?.pricing?.negotiation_qty || '',
          autoAcceptThreshold:
            productData?.pricing?.auto_accept_threshold || '',
          defaultSku:
            VariantsData?.find((e) => e?.is_default == true)?.id || '',
          variants:
            Array.isArray(VariantsData) && VariantsData.length > 0
              ? VariantsData.map((e) => ({
                  id: e.id,
                  label: e.label,
                  additionalPrice: e.additional_price,
                  finalPrice: '',
                  quantity: 0,
                  minOrderQty: 1,
                  outOfStock: e.qty,
                  restocking: 0,
                  tierPricing: e.tier_prices.length > 0,
                  tiers: e.tier_prices.map((tier) => ({
                    id: tier.id,
                    maxQuantity: tier.bundle_end_unit,
                    minQuantity: tier.bundle_start_unit,
                    title: tier.bundle_name,
                    price: tier.price,
                  })),
                  backorder: false,
                  defaultSku: e.is_default ? e.id : '',
                }))
              : module === ProductType?.BUNDLE
                ? [
                    {
                      id: 1,
                      label: '',
                      additionalPrice: 0,
                      finalPrice: '',
                      quantity: 0,
                      minOrderQty: 1,
                      outOfStock: '',
                      restocking: 0,
                      tierPricing: '',
                      tiers: [],
                      backorder: false,
                      defaultSku: '',
                    },
                  ]
                : [],
        }}
        validationSchema={getPricingSchema(module, tableData)}
        context={{ module: ProductType?.BUNDLE }}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
        onSubmit={async (values) => {
          const {
            taxationType: taxation_type,
            priceAfterTax: price_after_tax,
            chargeTax,
            costPrice: base_price,
            negotiationAllowed,
            makeToOrder,
            minimumOrderQuantity: min_order_qty,
            outOfStockThreshold: out_of_stock_threshold,
            restockingThreshold: restocking_threshold,
            backordersAllowed,
            negotiationQuantity: negotiation_qty,
            autoAcceptThreshold: auto_accept_threshold,
            variants,
            tierPricing,
            samePriceForAll,
            skuLabel,
          } = values;

          const { payload } = await dispatch(
            submitPricing({
              taxation_type,
              price_after_tax,
              is_tax_charge: chargeTax ? 1 : 0,
              base_price,
              is_negotiation_enabled: negotiationAllowed ? 1 : 0,
              is_under_make_to_order: makeToOrder ? 1 : 0,
              min_order_qty,
              out_of_stock_threshold,
              restocking_threshold,
              is_backorder_allowed: backordersAllowed == 'Yes' ? 1 : 0,
              negotiation_qty,
              auto_accept_threshold,
              sku: convertVariants(variants, values.defaultSku, skuLabel),
              is_price_same: samePriceForAll ? 1 : 0,
              product_id: productId,
            })
          );

          if (payload.status || payload.ok) {
            await dispatch(getProductDetails(productId));
          }

          return payload?.ok;
        }}
      >
        {(formik) => {
          useEffect(() => {
            if (formik.values.samePriceForAll) {
              const updatedVariants = formik.values.variants?.map((e) => ({
                ...e,
                additionalPrice: '',
              }));
              formik.setFieldValue('variants', updatedVariants);
            }
          }, [formik?.values?.samePriceForAll]);

          let columns = [
            {
              name: 'SKU Label',
              selector: (row) => row.label,
              width: '325px',
              cell: (row) => {
                if (module == ProductType?.RETAIL) {
                  return (
                    <span className="text-dark-500 text-[13px]">
                      {row.label}
                    </span>
                  );
                } else {
                  return (
                    <InputField
                      type="text"
                      id={'skuLabel'}
                      name={'skuLabel'}
                      marginBottom="mb-0 w-full"
                      value={formik.values.skuLabel}
                      generateLabel="Generate code"
                      inputClassName="form-control"
                      formik={formik}
                      onGenerateValue={() => {
                        const timestamp = Date.now().toString().slice(-6);
                        const random = Math.random()
                          .toString(36)
                          .substring(2, 5)
                          .toUpperCase();
                        const newSku = `COA-VDV066-${timestamp}-${random}`;
                        formik.setFieldValue(`skuLabel`, newSku);
                      }}
                      readOnly={true}
                    />
                  );
                }
              },
            },
            {
              name: 'Attributes',
              selector: (row) => row.attributes,
              grow: 2,
              cell: (row) => {
                return (
                  <span className="text-dark-500 text-[13px]">
                    {row.attributes}
                  </span>
                );
              },
            },
            {
              name: 'Additional Price',
              width: '110px',
              cell: (row) => (
                <InputField
                  type="number"
                  name="additionalPrice"
                  placeholder="0.00"
                  rightText="$"
                  disabled={formik?.values?.samePriceForAll}
                  rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                  inputClassName="form-control !pl-7 !pr-0"
                  marginBottom="mb-0"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.additionalPrice || ''
                  }
                  onChange={(e) => {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === row.id
                        ? { ...variant, additionalPrice: e.target.value }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }}
                />
              ),
            },
            {
              name: 'Price/unit',
              selector: (row) => row.finalPrice,
              cell: (row) => {
                let Price;
                if (module == ProductType?.BUNDLE) {
                  Price =
                    formik?.values?.variants.length > 0
                      ? Number(formik?.values?.costPrice) +
                        Number(formik?.values?.variants[0].additionalPrice)
                      : Number(formik?.values?.costPrice);
                } else {
                  if (formik.values.samePriceForAll) {
                    Price = formik.values?.costPrice;
                  } else {
                    Price =
                      formik?.values?.variants.length > 0
                        ? Number(
                            formik?.values?.variants.find((e) => row.id == e.id)
                              ?.additionalPrice
                          ) + Number(formik?.values?.costPrice)
                        : 0 + Number(formik?.values?.costPrice);
                  }
                }

                return (
                  <span className="text-dark-500 text-[13px]">$ {Price}</span>
                );
              },
            },
            {
              name: 'Stock',
              cell: (row) => (
                <InputField
                  type="number"
                  name="outOfStock"
                  value={
                    formik.values.variants?.find((e) => e.id === row.id)
                      ?.outOfStock
                  }
                  marginBottom="mb-0"
                  min="0"
                  onChange={(e) => {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === row.id
                        ? { ...variant, outOfStock: e.target.value }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }}
                />
              ),
            },
            {
              name: 'Tier Pricing',
              center: true,
              cell: (row) => (
                <div className="flex justify-center">
                  <CustomCheckbox
                    id={`tier-pricing-${row.id}`}
                    name="tierPricing"
                    checked={
                      formik.values.variants?.find((e) => e.id === row.id)
                        ?.tierPricing || false
                    }
                    onChange={(e) => {
                      const newVariants = formik.values.variants.map(
                        (variant) =>
                          variant.id === row.id
                            ? { ...variant, tierPricing: e.target.checked }
                            : variant
                      );
                      formik.setFieldValue('variants', newVariants);
                      setSelectedTier((prev) =>
                        e.target.checked ? row.id : prev
                      );
                      if (e.target.checked) {
                        setIsAddTierOpen(true);
                      }
                    }}
                  />
                </div>
              ),
            },

            {
              name: 'Default SKU',
              center: true,
              cell: (row) => (
                <div className="flex flex-col items-center justify-center relative">
                  {/* Radio Wrapper */}
                  <div className="relative w-4 h-4 mb-1">
                    {/* Hidden Native Input */}
                    <input
                      type="radio"
                      name="defaultSku"
                      value={row.id}
                      checked={formik.values.defaultSku === row.id}
                      onChange={() => {
                        formik.setFieldValue('defaultSku', row.id);
                      }}
                      className="form-radio absolute z-10 opacity-0 w-full h-full cursor-pointer"
                    />

                    {/* Custom Styled Radio */}
                    <div
                      className={`absolute w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all
              ${formik.values.defaultSku === row.id ? 'border-primary-500' : 'border-gray-300'}`}
                    >
                      {formik.values.defaultSku === row.id && (
                        <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                      )}
                    </div>
                  </div>

                  {/* Error Message */}
                  {formik.touched.defaultSku && formik.errors.defaultSku && (
                    <span className="text-danger-500 text-xs mt-1 text-center whitespace-nowrap">
                      {formik.errors.defaultSku}
                    </span>
                  )}
                </div>
              ),
            },
          ];

          columns =
            module == ProductType?.RETAIL
              ? columns
              : columns.filter((e) => e.name !== 'Attributes');

          return (
            <Form onSubmit={formik.handleSubmit}>
              {navigationComponent &&
                React.cloneElement(navigationComponent, {
                  ...navigationComponent.props,
                  formik,
                })}

              <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
                <div className="max-w-[1152px] mx-auto px-6 pb-8">
                  <div className="bg-white border border-border-color rounded-xl">
                    <div className="flex items-center justify-between gap-3 p-4">
                      <h2 className="text-base font-semibold">
                        Set Pricing & Inventory
                      </h2>
                    </div>

                    <div className="space-y-4 xl:space-y-6 p-4 pt-2">
                      <div className="flex items-start gap-4">
                        <div className="flex-1 xl:flex-[0_0_672px]">
                          <InputField
                            id="costPrice"
                            name="costPrice"
                            type="number"
                            label="Cost Price"
                            placeholder="0.00"
                            marginBottom="mb-0"
                            rightText="$"
                            value={formik.values?.costPrice}
                            rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                            inputClassName="form-control !pl-7 !pr-4"
                            formik={formik}
                          />
                        </div>
                        {VariantsData?.length ? (
                          <CustomCheckbox
                            id="samePriceForAll"
                            name="samePriceForAll"
                            label="Same price for all"
                            checked={formik.values.samePriceForAll}
                            onChange={(e) => {
                              formik.setFieldValue(
                                'samePriceForAll',
                                e.target.checked
                              );
                            }}
                            className="mt-8"
                          />
                        ) : null}
                      </div>
                      <div className="flex items-start flex-wrap gap-4">
                        <CustomCheckbox
                          id="chargeTax"
                          name="chargeTax"
                          label="Charge tax on this product"
                          checked={formik.values.chargeTax}
                          onChange={(e) => {
                            formik.setFieldValue('chargeTax', e.target.checked);
                          }}
                        />
                        <CustomCheckbox
                          id="negotiationAllowed"
                          name="negotiationAllowed"
                          label="Negotiation Allowed"
                          checked={formik.values.negotiationAllowed}
                          onChange={(e) => {
                            formik.setFieldValue(
                              'negotiationAllowed',
                              e.target.checked
                            );
                          }}
                        />
                        <CustomCheckbox
                          id="makeToOrder"
                          name="makeToOrder"
                          label="This product is make to order"
                          checked={formik.values.makeToOrder}
                          onChange={(e) => {
                            formik.setFieldValue(
                              'makeToOrder',
                              e.target.checked
                            );
                          }}
                        />
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-x-4 gap-y-4 xl:gap-y-6">
                        <InputField
                          id="minimumOrderQuantity"
                          name="minimumOrderQuantity"
                          type="number"
                          marginBottom="mb-0"
                          label="Minimum Order Quantity"
                          value={formik.values?.minimumOrderQuantity}
                          formik={formik}
                        />
                        <InputField
                          id="outOfStockThreshold"
                          name="outOfStockThreshold"
                          type="number"
                          marginBottom="mb-0"
                          label="Out of Stock Threshold"
                          value={formik.values?.outOfStockThreshold}
                          formik={formik}
                        />
                        <InputField
                          id="restockingThreshold"
                          name="restockingThreshold"
                          type="number"
                          marginBottom="mb-0"
                          label="Restocking Threshold"
                          value={formik.values?.restockingThreshold}
                          formik={formik}
                        />
                        <div className="flex flex-col mb-0">
                          <span
                            className="form-label"
                          >
                            Backorders Allowed
                          </span>
                          <SelectField
                            id="backordersAllowed"
                            name="backordersAllowed"
                            label="Backorders Allowed"
                            placeholder="Select option"
                            className="single-select"
                            options={[
                              { value: 'Yes', label: 'Yes' },
                              { value: 'No', label: 'No' },
                            ]}
                            formik={formik}
                          />
                          {formik.touched.backordersAllowed &&
                            formik.errors.backordersAllowed && (
                              <div className="text-danger-500 text-xs mt-1">
                                {formik.errors.backordersAllowed}
                              </div>
                            )}
                        </div>
                        {/* Taxation type filed */}
                        {formik.values.chargeTax && (
                          <div className="grid grid-cols-1 md:grid-cols-2 col-span-2 gap-4 w-full overflow-hidden transition-all duration-300 ease-in-out">
                            <div className="flex flex-col mb-4 lg:mb-0">
                              <span
                                className="form-label"
                              >
                                Taxation Type
                              </span>
                              <SelectField
                                id="taxationType"
                                name="taxationType"
                                label="Taxation Type"
                                placeholder="Select option"
                                className="single-select"
                                options={taxationTypes}
                                formik={formik}
                              />
                              {formik.touched.taxationType &&
                                formik.errors.taxationType && (
                                  <div className="text-danger-500 text-xs mt-1">
                                    {formik.errors.taxationType}
                                  </div>
                                )}
                            </div>

                            <InputField
                              id="priceAfterTax"
                              name="priceAfterTax"
                              type="number"
                              label="Price after tax"
                              placeholder="0.00"
                              rightText="$"
                              marginBottom="mb-0"
                              value={formik.values?.priceAfterTax}
                              rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
                              inputClassName="form-control !pl-7 !pr-4"
                              formik={formik}
                            />
                          </div>
                        )}

                        {formik.values.negotiationAllowed && (
                          <>
                            <InputField
                              id="negotiationQuantity"
                              name="negotiationQuantity"
                              type="number"
                              marginBottom="mb-0"
                              label="Negotiation Quantity"
                              value={formik.values?.negotiationQuantity}
                              formik={formik}
                            />
                            <InputField
                              id="autoAcceptThreshold"
                              name="autoAcceptThreshold"
                              type="number"
                              marginBottom="mb-0"
                              label="Auto-Accept Threshold"
                              value={formik.values?.autoAcceptThreshold}
                              formik={formik}
                            />
                          </>
                        )}
                      </div>

                      <DataTable
                        columns={columns}
                        data={tableData}
                        customStyles={customStyles}
                        fixedHeader
                        className="custom-table auto-height-table configure-table"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Add Tier Pricing */}
              <BaseOffCanvas
                isOpen={isAddTierOpen}
                onClose={() => {
                  if (
                    formik.values.variants.find((e) => e.id == selctedTier)
                      .tiers.length == 0
                  ) {
                    const newVariants = formik.values.variants.map((variant) =>
                      variant.id === selctedTier
                        ? { ...variant, tierPricing: false }
                        : variant
                    );
                    formik.setFieldValue('variants', newVariants);
                  }

                  setIsAddTierOpen(false);
                }}
                title="Add Tier Pricing"
              >
                <div className="flex items-center justify-between mb-4 xl:mb-6">
                  <button
                    type="button"
                    className="btn btn-gray"
                    onClick={() => setIsAddTierOpen(false)}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={() => {
                      if (tierPricingRef.current) {
                        tierPricingRef.current.saveTiers();
                      }
                    }}
                  >
                    Save
                  </button>
                </div>
                <TierPricingContent
                  ref={tierPricingRef}
                  selctedTierId={selctedTier}
                />
              </BaseOffCanvas>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default Pricing;
