'use client';
import React from 'react';
import FileUpload from '../Inputs/FileUpload';
import { useFormikContext } from 'formik';
import CustomCheckbox from '../Inputs/CustomCheckbox';

function UploadAndMapFields() {
  const formik = useFormikContext();

  return (
    <div className="bg-white border border-border-color rounded-xl">
      <div className="flex items-center justify-between gap-3 p-4">
        <h2 className="font-bold">Import products by CSV</h2>
      </div>

      <div className="p-4 pt-2">
        <FileUpload
          accept=".csv"
          maxFileSizeMB={6}
          value={formik.values.csvFile}
          error={formik.errors.csvFile}
          onChange={(file) => formik.setFieldValue('csvFile', file)}
        />

        <div className="flex flex-col gap-4 mt-6">
          <CustomCheckbox
            id="importAndReplace"
            name="importAndReplace"
            label="Import and replace existing products"
            subText="Updates products that match by SKU or ID and adds any new listings found in the file."
            checked={formik.values.importAndReplace == 'Yes'}
            onChange={(e) => {
              formik.setFieldValue('importAndReplace', e.target.checked ? 'Yes' : 'No');
            }}
          />
          <CustomCheckbox
            id="importNewProductsOnly"
            name="importNewProductsOnly"
            label="Import new products only"
            checked={formik.values.importNewProductsOnly == 'Yes'}
            onChange={(e) => {
              formik.setFieldValue('importNewProductsOnly', e.target.checked ? 'Yes' : 'No');
            }}
          />
        </div>
      </div>
    </div>
  );
}

export default UploadAndMapFields;
