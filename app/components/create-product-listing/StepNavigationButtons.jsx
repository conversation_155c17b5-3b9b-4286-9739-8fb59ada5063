// components/create-product-listing/StepNavigationButtons.jsx
import React from 'react';
import { useFormikContext } from 'formik';
import { ProductType } from '@/utils/constant';

const StepNavigationButtons = ({
  activeStep,
  handleBack,
  handleNext,
  setIsModalOpen,
  setPendingSections,
  module,
}) => {
  const formik = useFormikContext(); // gets Formik instance in context

  const handleNextClick = async (submission_type) => {
    const errors = await formik.validateForm(); // Validate the form manually

    if (Object.keys(errors).length === 0) {
      const submitResult = await formik.submitForm(); // onSubmit will return `payload.ok`

      if (submitResult == true || Array.isArray(submitResult)) {
        if (submission_type === 'Submit') {
          if (Array.isArray(submitResult)) {
            const arr = [];
            const totalSteps =
              module == ProductType?.RETAIL
                ? [0, 1, 2, 3, 4, 5, 6, 7]
                : [0, 1, 2, 3, 4, 5, 6];
            const completedSteps = submitResult;

            const adjustedCompletedSteps = completedSteps.map((n) => n - 1); // [0, 7]

            const fullRange = totalSteps.filter(
              (n) => !adjustedCompletedSteps.includes(n)
            );
            setPendingSections(fullRange);
            handleNext(fullRange);
          } else {
            handleNext();
          }
        } else {
          setIsModalOpen(true);
        }
      }
    } else {
      formik.setTouched(
        Object.keys(errors).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
    }
  };

  return (
    <div className="flex justify-between items-center mb-4 xl:mb-6 max-w-[1152px] px-6 mx-auto">
      <button
        className="btn btn-gray"
        type="button"
        onClick={() => handleNextClick('Draft')}
      >
        Save as draft
      </button>
      <div className="flex gap-2">
        <button
          className="btn btn-outline-gray"
          onClick={handleBack}
          disabled={activeStep === 0}
        >
          Back
        </button>
        <button
          type="button"
          className="btn"
          onClick={() => handleNextClick('Submit')}
        >
          {activeStep === 7 ? 'Send for Approval' : 'Save and Next'}
        </button>
      </div>
    </div>
  );
};

export default StepNavigationButtons;
