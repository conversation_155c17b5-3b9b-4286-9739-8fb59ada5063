'use client';

import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';
import { components } from 'react-select';
import InputField from '../Inputs/InputField';
import { useDispatch, useSelector } from 'react-redux';
import ProductDescription from './ProductDescription';
import ImageUpload from '../Inputs/ImageUpload';
import BaseOffCanvas from '../offCanvas/BaseOffCanvas';
import ProductConfigurations from '../create-product-listing/ProductConfigurations';
import { Form, Formik, useFormikContext } from 'formik';
import CustomCheckbox from '../Inputs/CustomCheckbox';
import { GENERAL_INFORMATION_SCHEMA } from '@/utils/schema';
import {
  getBrandsAsync,
  getExternalProductIdType,
  getProductStyles,
  submitGeneralInfo,
} from '@/store/api/productApi';
import {
  getIndustryTagsAsync,
  onboardingUserAsync,
} from '@/store/api/onboardingApi';
import { setProductId } from '@/store/actions/userActions';
import { ProductType } from '@/utils/constant';

const SelectField = dynamic(() => import('../Inputs/SelectField'), {
  ssr: false,
});

function GeneralInformation({
  navigationComponent,
  module = ProductType?.RETAIL,
}) {
  const [isConfigOpen, setIsConfigOpen] = useState(false);
  const [brands, setBrands] = useState([]);
  const dispatch = useDispatch();
  const { onboardingData, franchiseIndustryTags } = useSelector(
    (state) => state.onboarding
  );
  const { productId } = useSelector((state) => state.product);
  const { productDetails, productSetDetails } = useSelector(
    (state) => state.product
  );

  const productData =
    module === ProductType?.RETAIL ? productDetails : productSetDetails;

  const [porductStyles, setProductStyles] = useState([]);
  const [externalProductIdType, setExternalProductIdType] = useState([]);
  const [defaultImageIndex, setDefaultImageIndex] = useState(null);

  useEffect(() => {
    setOptions();
  }, []);

  const setOptions = async () => {
    try {
      const [productStyleData, externalProductIdData] = await Promise.all([
        dispatch(getProductStyles()),
        dispatch(getExternalProductIdType()),
      ]);

      const productStyles =
        productStyleData?.payload?.data?.map((e) => ({
          label: e.name,
          value: e.id,
        })) || [];

      const externalTypes =
        externalProductIdData?.payload?.data?.map((e) => ({
          label: e.name,
          value: e.id,
        })) || [];

      setProductStyles(productStyles);
      setExternalProductIdType(externalTypes);
    } catch (error) {
      console.error('Failed to fetch options:', error);
      setProductStyles([]);
      setExternalProductIdType([]);
    }
  };

  const [images, setImages] = useState([]);

  const productCategories = onboardingData?.product_categories?.map((item) => {
    return { value: item.id, label: item.name };
  });

  async function getIndustryTags() {
    const { payload } = await dispatch(getIndustryTagsAsync({ name: 'Hotel' }));
  }

  useEffect(() => {
    const getBrands = async () => {
      const { payload } = await dispatch(getBrandsAsync());
      setBrands(payload.data?.map((e) => ({ value: e.id, label: e.name })));
    };
    getBrands();
    dispatch(onboardingUserAsync());
  }, []);
  const transformData = (data) => {
    return data?.map((franchise) => ({
      label: franchise.name, // Parent franchise as label
      options: franchise.sub_franchises?.map((sub) => ({
        value: sub.id, // Convert ID to string
        label: sub.name,
      })),
    }));
  };

  const IndustryTags = onboardingData?.industry_tags?.map((item) => {
    return { value: item.id, label: item.name };
  });
  const franchiseTagsOptions = transformData(franchiseIndustryTags) || [];
  return (
    <>
      <Formik
        initialValues={{
          title: productData?.['general-info']?.name || '',
          category: productData?.['general-info']?.category?.id || '',
          productImages: '',
          productVideoUrl: productData?.['general-info']?.video_url || '',
          industryTags:
            productData?.['general-info']?.industry?.map((e) => ({
              label: e.name,
              value: e.id,
            })) || [],
          productStyle: productData?.['general-info']?.product_style?.id || '',
          brandName:
            productData?.['general-info']?.brand_manufacturer?.id || '',
          manufacturerPartNumber:
            productData?.['general-info']?.manufacturer_part_number || '',
          modelNumber: productData?.['general-info']?.model_number || '',
          externalProductIdType:
            productData?.['general-info']?.external_product_type?.id || '',
          externalProductId:
            productData?.['general-info']?.external_product_id || '',
          hotelFranchiseName:
            productData?.['general-info']?.franchise?.map((e) => ({
              label: e.name,
              value: e.id,
            })) || [],
          noBrandOrManufacturer:
            productData?.['general-info']?.is_have_brand_manufacturer ===
            undefined
              ? ''
              : !productData['general-info'].is_have_brand_manufacturer,
          noProductId:
            productData?.['general-info']?.have_product_id === undefined
              ? ''
              : !productData['general-info'].have_product_id,
          coverImage: '',
        }}
        validationSchema={GENERAL_INFORMATION_SCHEMA}
        validateOnChange={true}
        validateOnBlur={true}
        enableReinitialize={true}
        onSubmit={async (values) => {
          const {
            title: name,
            category: category,
            productImages,
            productVideoUrl: video_url,
            industryTags,
            productStyle: product_style,
            brandName: brand_manufacturer,
            manufacturerPartNumber: manufacturer_part_number,
            modelNumber: model_number,
            externalProductIdType: external_product_type,
            externalProductId: external_product_id,
            hotelFranchiseName,
            noBrandOrManufacturer: is_have_brand_manufacturer,
            noProductId: have_product_id,
          } = values;

          const { payload } = await dispatch(
            submitGeneralInfo({
              name,
              category,
              images: productImages.map((e) => e.file),
              video_url,
              industry: industryTags.map((e) => e.value),
              product_style,
              brand_manufacturer,
              manufacturer_part_number,
              model_number,
              external_product_type,
              external_product_id,
              franchise:
                (hotelFranchiseName &&
                  hotelFranchiseName.map((e) => e.value)) ||
                '',
              is_have_brand_manufacturer: is_have_brand_manufacturer ? 0 : 1,
              have_product_id: have_product_id ? 0 : 1,
              cover_image: values.coverImage[0].file,
              product_type: module,
              ...(productId != null && { product_id: productId }),
            })
          );
          if (payload.status || payload.ok) {
            dispatch(setProductId(payload.data.id));
          }
          return payload?.ok;
        }}
      >
        {(formik) => {
          useEffect(() => {
            if (formik.values.noBrandOrManufacturer) {
              formik.setFieldValue('brandName', '');
            }
            // setBrands([])
          }, [formik.values.noBrandOrManufacturer]);

          useEffect(() => {
            const hasHotel =
              formik?.values?.industryTags &&
              formik?.values?.industryTags?.some(
                (tag) => tag.label?.toLowerCase() === 'hotel'
              );

            getIndustryTags();
            if (!hasHotel && formik.values.hotelFranchiseName?.length > 0) {
              formik.setFieldValue('hotelFranchiseName', []);
              formik.setFieldTouched('hotelFranchiseName', false);
            }
          }, [formik.values.industryTags]);

          useEffect(() => {
            if (formik.values.noProductId) {
              formik.setFieldValue('externalProductIdType', '', false);
              formik.setFieldValue('externalProductId', '', false);
            }
          }, [formik.values.noProductId]);

          return (
            <Form onSubmit={formik.handleSubmit}>
              {navigationComponent &&
                React.cloneElement(navigationComponent, {
                  ...navigationComponent.props,
                  formik,
                })}
              <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
                <div className="max-w-[1152px] mx-auto px-6 pb-8">
                  <div className="bg-white border border-border-color rounded-xl">
                    <div className="flex items-center justify-between gap-3 p-4">
                      <h2 className="font-bold">General Information</h2>

                      <div className="flex items-center gap-3">
                        <span className="text-xs text-gray-400">
                          Product ID: {productDetails?.product_id}
                        </span>
                        <span className="text-dark-500/10">|</span>
                        <span className="text-xs text-gray-400">
                          Vendor ID: #VX321
                        </span>
                      </div>
                    </div>
                    <div className="space-y-4 p-4 pt-2">
                      <div className="flex flex-col mb-4 xl:mb-6">
                        <label htmlFor="title" className="form-label">
                          Product Title
                        </label>
                        <textarea
                          id="title"
                          name="title"
                          className="form-control"
                          placeholder="eg. Marino 3 Seater Ocean Blue Suede Velvet Fabric Sofa with Two Cushions Perfect for Home Office Guests Living Room (3 Seater, Blue)(3 Year Warranty)"
                          rows="3"
                          resize="none"
                          value={formik.values.title}
                          onChange={formik.handleChange}
                          maxLength={150}
                        />
                        {formik.errors?.title && formik.touched?.title && (
                          <p className="text-danger-500 text-xs mt-1">
                            {formik.errors?.title}
                          </p>
                        )}
                      </div>
                      <div className="flex flex-col mb-4 xl:mb-6">
                        <span className="form-label">
                          Product Category
                        </span>
                        <SelectField
                          className="single-select"
                          name="category"
                          placeholder=""
                          components={{ Group: components.Group }}
                          options={productCategories}
                          formik={formik}
                        />
                        {formik.errors?.category &&
                          formik.touched?.category && (
                            <p className="text-danger-500 text-xs mt-1">
                              {formik.errors?.category}
                            </p>
                          )}{' '}
                      </div>
                      <ImageUpload
                        formik={formik}
                        images={images}
                        name="productImages"
                        nameCoverImage="coverImage"
                        setImages={setImages}
                        defaultImageIndex={defaultImageIndex}
                        setDefaultImageIndex={setDefaultImageIndex}
                        maxFiles={5}
                        maxSize={3000000}
                        heading="Image"
                        className="mb-4 xl:mb-6"
                        uploadText="Add multiple images"
                        thumbnailInfo={true}
                      />

                      <div className="flex flex-col">
                        <InputField
                          id="productVideoUrl"
                          name="productVideoUrl"
                          value={formik.values.productVideoUrl}
                          type="text"
                          label="Video URL"
                          optional={true}
                          marginBottom="mb-0"
                          placeholder="www.youtube.com/hubsups/desk"
                          required={false}
                          formik={formik}
                          length={255}
                        />
                        <p className="text-xs text-gray-300 mt-1 text-right font-medium">
                          Supported URL: Youtube or Vimeo
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-0">
                        <div className="flex flex-col mb-4">
                          <span className="form-label">
                            Industry Type
                          </span>
                          <SelectField
                            id="industryTags"
                            name="industryTags"
                            label="Industry Tags"
                            className="single-select"
                            placeholder={null}
                            isMulti={true}
                            options={IndustryTags}
                            formik={formik}
                          />
                          {formik.errors.industryTags &&
                            formik.touched.industryTags && (
                              <p className="text-danger-500 text-xs mt-1">
                                {formik.errors.industryTags}
                              </p>
                            )}{' '}
                        </div>
                        <div className="flex flex-col mb-4">
                          <span className="form-label">
                            Product Style
                          </span>
                          <SelectField
                            className="single-select"
                            name="productStyle"
                            placeholder=""
                            options={porductStyles}
                            formik={formik}
                          />
                          {formik.errors.productStyle &&
                            formik.touched.productStyle && (
                              <p className="text-danger-500 text-xs mt-1">
                                {formik.errors.productStyle}
                              </p>
                            )}{' '}
                        </div>
                      </div>

                      <div className="flex flex-col mb-4">
                        <span className="form-label">
                          Hotel/Franchise Name
                        </span>
                        <SelectField
                          options={franchiseTagsOptions}
                          isMulti={true}
                          closeMenuOnSelect={false}
                          isSearchable
                          // isClearable
                          name="hotelFranchiseName"
                          className="single-select multi-select"
                          placeholder="Select option"
                          isDisabled={
                            !Array.isArray(formik.values.industryTags) ||
                            !formik.values.industryTags.some(
                              (e) => e.label?.toLowerCase() === 'hotel'
                            )
                          }
                          menuPortalClassName="multiselect-menu-portal"
                          components={{ Group: components.Group }}
                          formik={formik}
                        />
                        {formik.errors.hotelFranchiseName &&
                          formik.touched.hotelFranchiseName && (
                            <p className="text-danger-500 text-xs mt-1">
                              {formik.errors.hotelFranchiseName}
                            </p>
                          )}{' '}
                      </div>

                      <div className="flex flex-col mb-4">
                        <span className="form-label">
                          Brand or Manufacturer
                        </span>
                        <SelectField
                          className="single-select"
                          name="brandName"
                          placeholder=""
                          options={brands}
                          formik={formik}
                          isDisabled={formik.values.noBrandOrManufacturer}
                        />
                        {formik.errors.brandName &&
                          formik.touched.brandName && (
                            <p className="text-danger-500 text-xs mt-1">
                              {formik.errors.brandName}
                            </p>
                          )}{' '}
                        <CustomCheckbox
                          id="manufacturerName"
                          className="mt-2"
                          name="noBrandOrManufacturer"
                          label="This product does not have a brand or manufacturer name"
                          checked={formik.values.noBrandOrManufacturer}
                          onChange={(e) => {
                            formik.setFieldValue(
                              'noBrandOrManufacturer',
                              e.target.checked
                            );
                          }}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-0">
                        <InputField
                          id="manufacturerPartNumber"
                          name="manufacturerPartNumber"
                          type="number"
                          value={formik.values.manufacturerPartNumber}
                          label="Manufacturer Part Number"
                          placeholder=""
                          required={false}
                          formik={formik}
                          length={100}
                        />
                        <InputField
                          id="modelNumber"
                          name="modelNumber"
                          value={formik.values.modelNumber}
                          type="text"
                          label="Product Model Number"
                          placeholder=""
                          required={false}
                          formik={formik}
                          length={100}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-0">
                        <div className="flex flex-col mb-0">
                          <span className="form-label">
                            External Product ID Type
                          </span>
                          <SelectField
                            className="single-select"
                            name="externalProductIdType"
                            placeholder="eg. UPC, ASIN, etc.."
                            options={externalProductIdType}
                            formik={formik}
                            isDisabled={formik.values.noProductId}
                          />
                          {formik.errors.externalProductIdType &&
                            formik.touched.externalProductIdType && (
                              <p className="text-danger-500 text-xs mt-1">
                                {formik.errors.externalProductIdType}
                              </p>
                            )}{' '}
                          <CustomCheckbox
                            id="externalProductId"
                            className="mt-2"
                            name="noProductId"
                            label="I don't have a Product ID"
                            checked={formik.values.noProductId}
                            onChange={(e) => {
                              formik.setFieldValue(
                                'noProductId',
                                e.target.checked
                              );
                            }}
                          />
                        </div>
                        <InputField
                          id="externalProductId"
                          name="externalProductId"
                          type="text"
                          label="External Product ID"
                          value={formik.values.externalProductId}
                          placeholder="Example: 7145321975843"
                          required={false}
                          formik={formik}
                          length={100}
                          disabled={formik.values.noProductId}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>

      <BaseOffCanvas
        isOpen={isConfigOpen}
        onClose={() => setIsConfigOpen(false)}
        title="Product Attributes"
      >
        <ProductConfigurations onClose={() => setIsConfigOpen(false)} />
      </BaseOffCanvas>
    </>
  );
}

export default GeneralInformation;
