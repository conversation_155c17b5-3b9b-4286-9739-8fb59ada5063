import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import Pricing from '../create-product-listing/Pricing';
import { Formik } from 'formik';
import { PRODUCT_DRAFT_SCHEMA, PRODUCT_SCHEMA } from '@/utils/schema';
import { useDispatch, useSelector } from 'react-redux';
import GeneralInformation from '../create-product-listing/GeneralInformation';
import PackageDetails from '../create-product-listing/PackageDetails';
import ShippingLogistics from '../create-product-listing/ShippingLogistics';
import PromotionDiscounts from '../create-product-listing/PromotionDiscounts';
import VariantsConfiguration from '../create-product-listing/VariantsConfiguration';
import InventoryManagement from '../create-product-listing/InventoryManagement';
import ProductSpecifications from '../create-product-listing/ProductSpecifications';
import ProductValidity from '../create-product-listing/ProductValidity';
import ProductIdentity from '../create-product-listing/ProductIdentity';
import PricingInventory from '../create-product-listing/PricingInventory';
import {
  createProductAsync,
  editProductAsync,
  getProductAsync,
} from '@/store/api/productApi';
import { setProductId } from '@/store/actions/userActions';
import CreatableMultiSelect from '../Inputs/createTable';

export const formatDate = (dateValue) => {
  if (!dateValue) return '';
  const date = new Date(dateValue);
  const formatted = date.toISOString().split('T')[0];
  return formatted;
};

const CreateProduct = () => {
  const { productId } = useSelector((state) => state.product);
  const dispatch = useDispatch();
  const [isDraft, setIsDraft] = useState(false);

  const createPayload = (payload) => {
    const {
      title: name,
      category,
      brandName: brand_manufacturer,
      modelNumber: model_number,
      basePrice: base_price,
      totalQuantity: qty,
      industryTags: industry,
      hotelFranchiseName: franchise,
      productDescription: description,
      productSummary: summary,
      productVideoUrl: video_url,
      weight,
      weightUnit: weight_unit,
      dimensionUnit: dimensions_unit,
      length: dimensions_length,
      width: dimensions_width,
      height: dimensions_height,
      productUnit: product_unit,
      preferredCarrier: preffered_carrier,
      shippingCost: shipping_cost,
      isFreeDelivery,
      minLeadTime: lead_min_day,
      maxLeadTime: lead_max_day,
      availableShipping: is_immediate_delivery,
      handlingFee: handling_fee,
      preferredCarrier: ship_by,
      pickupAddress: pickup_address,
      hasWarranty,
      durationType,
      durationValue,
      acceptReturns,
      returnDays,
      returnPolicy,
      discountName,
      hasOffer,
      offerType,
      buyQuantity,
      getQuantity,
      discountValidityFrom,
      discountValidityTo,
      promoCode,
      discountType,
      productImages,
      validityFrom,
      validityTo,
      policyFile,
      acceptScratchDent,
    } = payload;
    const modifiedPayload = {
      images: productImages,
      general: {
        name,
        category,
        brand_manufacturer,
        model_number,
        base_price,
        qty,
        franchise: franchise.map((e) => e.value),
        industry: industry.map((e) => e.value),
        video_url,
        description,
        summary,
      },
      package_details: {
        product_unit,
        weight,
        weight_unit,
        dimensions_unit,
        dimensions_length,
        dimensions_width,
        dimensions_height,
      },
      shipping_and_logistic: {
        pickup_address,
        ship_by,
        preffered_carrier,
        shipping_cost,
        is_free_delivery: isFreeDelivery ? 1 : 0,
        lead_min_day,
        lead_max_day,
        is_immediate_delivery,
        handling_fee,
      },
      warranty_and_return: {
        is_warranty: hasWarranty == 'Yes' ? 1 : 0,
        is_life_time_validity: '0',
        duration_type: durationType,
        duration_value: Number(durationValue),
        is_return: acceptReturns === 'Yes' ? 1 : 0,
        days_of_return: returnDays,
        return_policy_description: returnPolicy,
        validity_start_date: formatDate(validityFrom),
        validity_end_date: formatDate(validityTo),
        is_scratch_dent: acceptScratchDent ? '1' : '0',
        return_doc: policyFile,
      },
      promotion_and_discount: {
        is_promotion: hasOffer == 'Yes' ? 1 : 0,
        promotion_type: 'free',
        discount_buyx: Number(buyQuantity),
        discount_gety: Number(getQuantity),
        validity_from: formatDate(discountValidityFrom),
        validity_to: formatDate(discountValidityTo),
        promo_code: promoCode,
        discount_type: discountType,
      },
      status: 'Draft',
    };
    return modifiedPayload;
  };

  const initialValues = {
    title: '',
    category: '',
    sku: '',
    productID: '',
    brandName: '',
    modelNumber: '',
    industryTags: [],
    hotelFranchiseName: [],
    basePrice: '',
    productSummary: '',
    productDescription: '',
    productUnit: '',
    weight: '',
    weightUnit: 'kg',
    length: '',
    width: '',
    height: '',
    dimensionUnit: 'inch',
    pickupAddress: '',
    preferredCarrier: '',
    shippingCost: '',
    isFreeDelivery: false,
    minLeadTime: '',
    maxLeadTime: '',
    availableShipping: '',
    handlingFee: '',
    address: '',
    addressType: '',
    state: '',
    urlKey: '',
    metaTitle: '',
    metaKeywords: '',
    metaDescription: '',
    productVideoUrl: '',
    productVisibilityUrl: '',
    hasOffer: 'No',
    offerType: 'Buy X get Y free',
    discountName: '',
    buyQuantity: '',
    getQuantity: '',
    validityFrom: '',
    validityTo: '',
    discountValidityFrom: '',
    discountValidityTo: '',
    promoCode: '',
    showOldPrice: false,
    hasWarranty: 'No',
    durationType: '',
    durationValue: '',
    acceptReturns: 'No',
    returnDays: '',
    returnPolicy: '',
    policyFile: null,
    totalQuantity: '',
    minOrderQuantity: '',
    outOfStockThreshold: '',
    backordersAllowed: '',
    restockingThreshold: '',
    negotiationEnabled: false,
    makeToOrder: false,
    productImages: [],
    discountValidity: [],
    selectedAttributesValue: [],
    productVariants: [],
    variations: {},
    acceptScratchDent: false,
  };

  useEffect(() => {
    if (productId) {
      dispatch(getProductAsync({ productId }));
    }
  }, []);

  return (
    <Formik
      initialValues={initialValues}
      validationSchema={isDraft ? PRODUCT_DRAFT_SCHEMA : PRODUCT_SCHEMA}
      validateOnBlur
      validateOnMount
      onSubmit={async (values) => {
        const modifiedPayload = createPayload(values);

        if (!productId) {
          const { payload } = await dispatch(
            createProductAsync(modifiedPayload)
          );
          dispatch(setProductId(payload?.data?.id));
        } else {
          await dispatch(editProductAsync({ ...modifiedPayload, productId }));
        }
      }}
    >
      {(formik) => {
        return (
          <>
            <div className="flex items-center justify-between p-6 mb-0 w-full max-w-[1200px] mx-auto">
              <div className="flex flex-col gap-1">
                <div className="inline-flex items-center gap-2">
                  <Link
                    href="/products"
                    className="flex justify-center items-center w-8 h-8 text-xl hover:bg-dark-500/10 rounded-lg transition-base"
                    title="Back"
                  >
                    <span className="icon icon-arrow-left" />
                  </Link>
                  <h1 className="text-xl font-bold">Create Product</h1>
                </div>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={async () => {
                    setIsDraft(true);
                    const errors = await formik.validateForm(); // Validate the form manually
                    if (Object.keys(errors).length === 0) {
                      await formik.submitForm(); // Wait for form submission
                    } else {
                      formik.setTouched(
                        Object.keys(errors).reduce((acc, key) => {
                          acc[key] = true;
                          return acc;
                        }, {})
                      );
                    }
                  }}
                  type="button"
                  className="btn btn-gray"
                >
                  Save as draft
                </button>
                <button
                  onClick={async () => {
                    setIsDraft(false);
                    const errors = await formik.validateForm(); // Validate the form manually
                    if (Object.keys(errors).length === 0) {
                      await formik.submitForm(); // Wait for form submission
                    } else {
                      formik.setTouched(
                        Object.keys(errors).reduce((acc, key) => {
                          acc[key] = true;
                          return acc;
                        }, {})
                      );
                    }
                  }}
                  className="btn"
                >
                  Sent for approval
                </button>
              </div>
            </div>

            <div className="w-full h-[calc(100dvh-170px)] overflow-y-auto">
              <div className="max-w-[1200px] mx-auto px-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="order-2 lg:order-1 flex-1">
                    {/* Start : General Information */}
                    <GeneralInformation productId={productId} />
                    {/* End : General Information */}

                    <VariantsConfiguration />

                    <Pricing />

                    <PricingInventory />

                    <InventoryManagement />

                    <PackageDetails />

                    <ShippingLogistics />

                    <ProductValidity />

                    <PromotionDiscounts />
                  </div>

                  {/* Right bar content */}
                  <div className="flex flex-col gap-4 order-1 lg:order-2 flex-[0_0_350px] 2xl:flex-[0_0_396px]">
                    <ProductIdentity />
                    <ProductSpecifications />

                    <div className="bg-white border border-border-color rounded-xl">
                      <div className="flex items-center gap-3 p-4">
                        <h2 className="text-sm font-bold">Search Tags</h2>
                      </div>
                      <div className="flex flex-col mb-0 p-4 pt-2">
                        <CreatableMultiSelect
                          name="searchTags"
                          formik={formik}
                          className="single-select"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        );
      }}
    </Formik>
  );
};

export default CreateProduct;
