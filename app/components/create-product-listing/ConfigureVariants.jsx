'use client';

import React, { useState } from 'react';
import InputField from '../Inputs/InputField';
import ImageUpload from '../Inputs/ImageUpload';
import InventoryManagement from './InventoryManagement';
import Pricing from '../create-product-listing/Pricing';
import PackageDetails from '../create-product-listing/PackageDetails';

const ConfigureVariants = ({
  variants,
  onVariantChange,
  onSubmit,
  variant,
  formik,
}) => {
  const [images, setImages] = useState([]);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const handleSubmit = () => {
    setSubmitAttempted(true);
    if (images.length > 0) {
      onSubmit();
    }
  };

  const showError = submitAttempted && images.length === 0;

  return (
    <div className="space-y-4">
      <div className="card !p-0">
        <div className="flex items-center gap-3 p-4">
          <h2 className="text-sm font-bold">
            Step 3: Configure Variant Details
          </h2>
        </div>

        <div className="grid grid-cols-2 gap-4 p-4">
          <div>
            <InputField
              label="Base Price"
              type="number"
              // prefix="$"
              marginBottom="mb-0"
              placeholder="0.00"
              rightText="$"
              rightTextClassName="top-1/2 -translate-y-1/2 left-3 right-auto !text-dark-500"
              inputClassName="form-control !pl-7 !pr-4"
              onChange={(e) => onVariantChange('basePrice', e.target.value)}
            />
          </div>
          <div>
            <InputField
              label="SKU Suffix"
              type="text"
              marginBottom="mb-0"
              value="24-BM04"
              disabled
            />
          </div>
        </div>
      </div>

      {variants.map((variant, index) => (
        <div key={index} className="card !p-0">
          <div className="flex items-center gap-3 p-4">
            <h3 className="text-sm font-semibold">
              24-BM04-BLK-LEA:{' '}
              {Object.entries(variant)
                .filter(([key]) => !['price', 'quantity'].includes(key))
                .map(([_, value]) => value)
                .join(' + ')}
            </h3>
          </div>

          <div className="p-4">
            <div className="grid grid-cols-2 gap-4">
              <InputField
                label="SKU Name"
                type="text"
                value={`24-BM04-${Object.entries(variant)
                  .filter(([key]) => !['price', 'quantity'].includes(key))
                  .map(([_, value]) =>
                    String(value).substring(0, 3).toUpperCase()
                  )
                  .join('-')}`}
                disabled
              />
              <InputField
                label="Universal Product Code"
                type="text"
                optional={true}
              />
            </div>

            <ImageUpload
              images={images}
              name="productImages"
              setImages={setImages}
              maxFiles={5}
              maxSize={3000000}
              heading="Variant Images"
              showError={showError}
            />
          </div>
        </div>
      ))}

      <Pricing formik={formik} />

      <InventoryManagement formik={formik} />

      <PackageDetails formik={formik} />
    </div>
  );
};

export default ConfigureVariants;
