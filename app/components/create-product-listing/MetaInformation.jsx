import React from 'react';
import Image from 'next/image';
import Input<PERSON>ield from '../Inputs/InputField';
import { Form, Formik } from 'formik';
import { META_INFO_SCHEMA } from '@/utils/schema';
import { useDispatch, useSelector } from 'react-redux';
import { submitProductMetaInfo } from '@/store/api/productApi';
import { ProductType } from '@/utils/constant';

function MetaInformation({
  navigationComponent,
  module = ProductType?.RETAIL,
}) {
  const dispatch = useDispatch();
  const { productDetails, productSetDetails, productId } = useSelector(
    (state) => state.product
  );
  const productData =
    module === ProductType?.RETAIL ? productDetails : productSetDetails;

  return (
    <Formik
      initialValues={{
        metaTitle: '' || productData?.meta?.meta_title,
        metaDescription: '' || productData?.meta?.meta_description,
      }}
      validationSchema={META_INFO_SCHEMA}
      validateOnChange={true}
      validateOnBlur={true}
      enableReinitialize={true}
      onSubmit={async (values) => {
        console.log(productDetails, 'DETAILS');
        const complete_steps =
          module == ProductType?.RETAIL
            ? [1, 2, 3, 4, 5, 6, 7]
            : [1, 2, 3, 4, 5, 6];
        const pending_steps = complete_steps.filter(
          (e) => !productData?.product_steps?.includes(e)
        );
        console.log(pending_steps, '@@@@@@@');

        const { metaTitle: meta_title, metaDescription: meta_description } =
          values;

        const { payload } = await dispatch(
          submitProductMetaInfo({
            meta_title,
            meta_description,
            product_id: productId,
            send_for_approval: pending_steps.length == 0 ? 1 : 0,
          })
        );

        return payload?.data?.product_steps;
      }}
    >
      {(formik) => {
        return (
          <Form onSubmit={formik.handleSubmit}>
            {navigationComponent &&
              React.cloneElement(navigationComponent, {
                ...navigationComponent.props,
                formik,
              })}
            <div className="w-full h-[calc(100dvh-148px)] 2xl:h-[calc(100dvh-170px)] overflow-y-auto">
              <div className="max-w-[1152px] mx-auto px-6 pb-8">
                <div className="bg-white border border-border-color rounded-xl mb-4">
                  <div className="flex items-center gap-3 p-4">
                    <h2 className="text-base font-bold">Meta Information</h2>
                  </div>

                  <div className="space-y-4 p-4 pt-2">
                    <div className="flex flex-col">
                      <InputField
                        id="metaTitle"
                        name="metaTitle"
                        type="text"
                        label="Meta Title"
                        value={formik.values.metaTitle}
                        placeholder={null}
                        required={false}
                        formik={formik}
                        length={100}
                      />

                      <div className="flex flex-col">
                        <div className="flex justify-between items-center gap-2">
                          <label
                            htmlFor="metaDescription "
                            className="form-label"
                          >
                            Meta Description
                          </label>
                          <button
                            type="button"
                            className="inline-flex items-center gap-2 text-xs font-bold cursor-pointer text-primary-500 transition-base text-gradient"
                          >
                            <Image
                              src="/images/ai-icon.svg"
                              alt="AI Icon"
                              width={15}
                              height={15}
                            />
                            Generate text
                          </button>
                        </div>
                        <textarea
                          id="metaDescription"
                          name="metaDescription"
                          placeholder={null}
                          rows="6"
                          className="form-control"
                          value={formik?.values?.metaDescription || ''}
                          maxLength={500}
                          onChange={formik.handleChange}
                        />
                        {formik.errors?.metaDescription &&
                          formik.touched?.metaDescription && (
                            <p className="text-danger-500 text-xs mt-1">
                              {formik.errors?.metaDescription}
                            </p>
                          )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );
}

export default MetaInformation;
