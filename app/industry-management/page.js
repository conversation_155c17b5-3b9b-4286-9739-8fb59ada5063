'use client';

import React, { useState, useRef, useEffect } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import IndustryManagement from '../components/master-management/IndustryManagement';
import { DndContext, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import SortableItem from '../components/table/SortableItem';
import FilterField from '../components/table/FilterField';

export default function Page() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const [displayProperties, setDisplayProperties] = useState({
    name: true,
    status: true,
  });
  const [items, setItems] = useState(Object.keys(displayProperties));
  const [initialFormValues, setInitialFormValues] = useState({ industryName: '', status: true });
  const [editIndustryId, setEditIndustryId] = useState(null);

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'General Masters' },
    { label: 'Industry Management' },
  ];

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (displayMenuRef.current && !displayMenuRef.current.contains(event.target)) {
        setIsDisplayMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      const oldIndex = items.indexOf(active.id);
      const newIndex = items.indexOf(over.id);
      const newItems = [...items];
      newItems.splice(oldIndex, 1);
      newItems.splice(newIndex, 0, active.id);
      setItems(newItems);
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar isCollapsed={isSidebarCollapsed} setIsCollapsed={setIsSidebarCollapsed} />
        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}
        >
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Industry Management</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex justify-end gap-3">
              <button
                type="button"
                className="btn btn-primary"
                onClick={() => {
                  setInitialFormValues({ industryName: '', status: true });
                  setEditIndustryId(null);
                  setIsAddModalOpen(true);
                }}
              >
                Add Industry
              </button>
            </div>
          </div>

          <div className="rounded-xl">
            {/* Filter filed */}
            <FilterField
              filterText={filterText}
              setFilterText={setFilterText}
              isSearchFilterOpen={isSearchFilterOpen}
              setIsSearchFilterOpen={setIsSearchFilterOpen}
              isDisplayMenuOpen={isDisplayMenuOpen}
              setIsDisplayMenuOpen={setIsDisplayMenuOpen}
              displayMenuRef={displayMenuRef}
            >
              <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
                <DndContext collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
                  <SortableContext items={items} strategy={verticalListSortingStrategy}>
                    <ul className="space-y-1">
                      {items.map((key) => (
                        <SortableItem
                          key={key}
                          id={key}
                          value={key}
                          checked={displayProperties[key]}
                          onChange={() =>
                            setDisplayProperties((prev) => ({
                              ...prev,
                              [key]: !prev[key],
                            }))
                          }
                        />
                      ))}
                    </ul>
                  </SortableContext>
                </DndContext>
              </div>
            </FilterField>

            <IndustryManagement
              filterText={filterText}
              displayProperties={displayProperties}
              isAddModalOpen={isAddModalOpen}
              setIsAddModalOpen={setIsAddModalOpen}
              initialFormValues={initialFormValues}
              setInitialFormValues={setInitialFormValues}
              editIndustryId={editIndustryId}
              setEditIndustryId={setEditIndustryId}
            />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}