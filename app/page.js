'use client';
import { useDispatch, useSelector } from 'react-redux';
import Login from './components/auth/Login';
import { useEffect } from 'react';
import { resetUserDetailsAction } from '@/store/actions/authActions';
import PublicLayout from './components/layout/PublicLayout';

export default function Home() {
  const { otpSent } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(resetUserDetailsAction());
  }, []);
  return (
    <>
      <PublicLayout>
        {/* {otpSent ? (
          <OtpVerify otpType={'user-register-password-otp'} />
        ) : (
        )} */}
        <Login />
      </PublicLayout>
    </>
  );
}
