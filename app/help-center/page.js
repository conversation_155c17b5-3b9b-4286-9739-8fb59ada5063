'use client';

import React, { useEffect, useState } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
// import HelpCenterTable from '@/app/components/table/HelpCenterTable';
import Sidebar from '@/app/components/sidebar/Sidebar';
import CreateTicket from '@/app/components/modals/CreateTicket';

const PickYourPlan = () => {
  const [activeTab, setActiveTab] = useState('All');
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  useEffect(() => {
    // Simulate data loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const [helpCenter, setHelpCenter] = useState([
    {
      id: '0',
      subject: 'Ticket is raised for delivery issue',
      ticketId: '#T1000',
      postedDate: 'Dec 17, 2022',
      supportType: 'Product Return',
      ticketStatus: 'Open',
    },
    {
      id: '01',
      subject: 'Ticket is raised for delivery issue',
      ticketId: '#T1001',
      postedDate: 'Dec 17, 2022',
      supportType: 'Product Return',
      ticketStatus: 'Open',
    },
    {
      id: '02',
      subject: 'Ticket is raised for delivery issue',
      ticketId: '#T1002',
      postedDate: 'Dec 17, 2022',
      supportType: 'Product Return',
      ticketStatus: 'Close',
    },
    {
      id: '03',
      subject: 'Ticket is raised for delivery issue',
      ticketId: '#T1003',
      postedDate: 'Dec 17, 2022',
      supportType: 'Product Return',
      ticketStatus: 'Resolve',
    }
    // Add more order data here
  ]);

  const tabs = [
    { id: 'All', count: null },
    { id: 'Open', count: 1 },
    { id: 'Close', count: 1 },
    { id: 'Resolve', count: 1, },
  ];
  const filteredOrders = helpCenter.filter((order) =>
    activeTab === 'All' ? true : order.ticketStatus === activeTab
  );
  const getStatusColor = (status) => {
    const colors = {
      Open: 'bg-green-500/10 text-green-500',
      Close: 'bg-info-500/10 text-info-500',
      Resolve: 'bg-gray-500/10 text-gray-500',
    };
    return colors[status] || colors['All'];
  };

  return (
    <PrivateLayout>
      <div className="flex bg-surface-300 rounded-xl ml-3 mt-[60px] w-[calc(100% - 12px)]">
        {/* Update the sidebar component to pass the state */}
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto bg-surface-200 rounded-s-xl rounded-bl-xl overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex items-center justify-between p-6 mb-0 w-full max-w-[1200px] mx-auto">
            <div className="flex flex-col gap-1">
              <h2 className="text-lg lg:text-xl font-bold">
              Help Center
              </h2>
            </div>

            <button type="button" onClick={() => setIsOpen(true)} className="btn btn-primary">
              Create a ticket
            </button>
          </div>

          <div className="w-full h-[calc(100dvh-137px)] overflow-y-auto">
            <div className="max-w-[1200px] mx-auto px-6 pb-8">
              <div className='rounded-xl'>
                <div className="flex items-center justify-between gap-2.5 bg-white p-2 rounded-tl-xl rounded-tr-xl border border-border-color ">
                  <div className="flex items-center gap-1.5 lg:gap-2 overflow-x-auto bg-white">
                    {tabs.map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`inline-flex items-center px-2 lg:px-3 py-1.5 text-xs font-semibold rounded-lg whitespace-nowrap cursor-pointer transition-base group hover:bg-dark-500/10 hover:text-dark-500 ${
                          activeTab === tab.id
                            ? 'bg-dark-500/10 text-dark-500'
                            : 'bg-transparent text-gray-400'
                        }`}
                      >
                        <span className="text-xs transition-base">{tab.id}</span>
                        {tab.id !== 'All' && (
                          <span
                            className={`rounded-[4px] px-1 py-[1px] text-xs ml-1.5 ${getStatusColor(tab.id)} transition-base`}
                          >
                            {tab.count}
                          </span>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
                {/* <HelpCenterTable data={filteredOrders} isLoading={isLoading}/> */}
              </div>
            </div>
          </div>
        </div>
      </div>


      <CreateTicket isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </PrivateLayout>
  );
};

export default PickYourPlan;
