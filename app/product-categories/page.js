'use client';

import React, { useState } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import CategoryManagement from '../components/product-categories/CategoryManagement';

export default function Page() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Catalogue Management' },
    { label: 'Product Categories' }
  ];
  
  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Product Categories</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            {/* <div className="flex justify-end gap-3">
              <button type="button" className="btn btn-primary flex items-center gap-2" onClick={() => setIsModalOpen(true)}>
                <span className="icon icon-plus text-base" />
                Add Product Style
              </button>
            </div> */}
          </div>

          <div className="rounded-xl">
            <CategoryManagement />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
