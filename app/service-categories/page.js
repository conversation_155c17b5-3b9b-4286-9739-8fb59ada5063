'use client';

import React, { useState } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import ServiceCategoriesManagement from '../components/service-categories/ServiceCategoriesManagement';

export default function Page() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Catalogue Management' },
    { label: 'Service Categories' }
  ];

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Service Categories</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          <div className="rounded-xl">
            <ServiceCategoriesManagement />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
