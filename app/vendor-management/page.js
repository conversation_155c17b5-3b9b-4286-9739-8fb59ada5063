'use client';

import React, { useState, useRef, useEffect } from 'react';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import VendorDirectory from '../components/vendor-management/VendorDirectory';
import { useDispatch, useSelector } from 'react-redux';
import { fetchVendorAsync } from '@/store/api/vendorsApi';

export default function Page() {
  const dispatch = useDispatch();
  const {
    vendors,
  } = useSelector((state) => state?.vendorManagement);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [filterText, setFilterText] = useState('');
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Vendor Management' }
  ];


  // Update the click outside effect to handle both dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        displayMenuRef.current &&
        !displayMenuRef.current.contains(event.target)
      ) {
        setIsDisplayMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    let payload = {
      search: filterText,
      status: "Pending",
      name: filterText
    }
      dispatch(fetchVendorAsync(payload));
    }, [dispatch]);

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex justify-between items-center mb-4">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Vendor Management</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            {/* <div className="flex justify-end gap-3">
              <button type="button" className="btn btn-primary inline-flex items-center gap-1">
                <span className="icon icon-plus" />
                Add Vendor
              </button>
            </div> */}
          </div>

          <VendorDirectory data={vendors?.data} />
        </div>
      </div>
    </PrivateLayout>
  );
}
