'use client';

import React, { useState, useEffect, useRef } from 'react';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import Link from 'next/link';
import PrivateLayout from '../components/layout/PrivateLayout';
import Sidebar from '../components/sidebar/Sidebar';
import RolesAndPermissionsTable from '../components/table/RolesAndPermissionsTable';
import InputField from '../components/Inputs/InputField';
import Breadcrumb from '../components/Inputs/Breadcrumb';

// Add this new component above the Products component
const SortableItem = ({ id, value, checked, onChange }) => {
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <li
      ref={setNodeRef}
      style={style}
      className="flex items-center gap-2 px-2 py-1 hover:bg-light-500/5 rounded-lg"
    >
      <div className="flex items-center justify-between gap-2 w-full">
        <div className="inline-flex items-center gap-2 flex-1">
          <label
            className="flex items-center cursor-pointer relative"
            htmlFor={`check-${id}`}
          >
            <input
              type="checkbox"
              className="peer h-4 w-4 cursor-pointer transition-all appearance-none rounded border-2 border-gray-200 checked:bg-primary-500 checked:border-primary-500"
              id={`check-${id}`}
              checked={checked}
              onChange={onChange}
            />
            <span className="absolute text-white opacity-0 peer-checked:opacity-100 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3"
                viewBox="0 0 20 20"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth="1"
              >
                <path
                  fillRule="evenodd"
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
          </label>
          <label htmlFor={`check-${id}`} className="text-xs capitalize">
            {id.replace(/([A-Z])/g, ' $1').trim()}
          </label>
        </div>
        <span
          {...attributes}
          {...listeners}
          className="icon icon-dots-six-vertical text-gray-400 py-1 rounded-md hover:bg-dark-500/5 cursor-move transition-base duration-200"
        />
      </div>
    </li>
  );
};

export default function Orders() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const statusDropdownRef = useRef(null);
  const [filterText, setFilterText] = useState('');
  const [selectedTag, setSelectedTag] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const displayMenuRef = useRef(null);
  const [displayProperties, setDisplayProperties] = useState({
    sku: true,
    status: true,
    variants: true,
    price: true,
    quantity: true,
  });
  const [items, setItems] = useState(Object.keys(displayProperties));
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [paginationPerPage, setPaginationPerPage] = useState(5);
  useEffect(() => {
      // Simulate data loading
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 1000);
  
      return () => clearTimeout(timer);
    }, []);

  // Update the click outside effect to handle both dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        statusDropdownRef.current &&
        !statusDropdownRef.current.contains(event.target)
      ) {
        setIsFilterOpen(false);
      }
      if (
        displayMenuRef.current &&
        !displayMenuRef.current.contains(event.target)
      ) {
        setIsDisplayMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const [activeTab, setActiveTab] = useState('All');
  const rolesAndPermissionsData = [
    {
      id: 1,
      role: 'Store Owner',
      status: 'Active',
      description: 'Store Owner',
      userCount: 1,
      createdBy: 'Ferruchio Lamborghini',
      createdAt: '2023-01-01',
      permissions: ['all']
    },
    {
      id: 2,
      role: 'Warehouse Manager',
      status: 'Active',
      description: 'Warehouse Manager',
      userCount: 1,
      createdBy: 'Enrique Iglesias',
      createdAt: '2023-01-01',
      permissions: ['inventory', 'shipping', 'orders']
    },
    {
      id: 3,
      role: 'Accounts Manager',
      status: 'Inactive',
      description: 'Accounts Manager',
      userCount: 1,
      createdBy: 'Michael Jackson',
      createdAt: '2023-01-01',
      permissions: ['finance', 'reports']
    },
    {
      id: 4,
      role: 'Support Staff',
      status: 'Active',
      description: 'Support Staff',
      userCount: 1,
      createdBy: 'John Doe',
      createdAt: '2023-01-01',
      permissions: ['customer_service', 'orders']
    },
    {
      id: 5,
      role: 'Marketing Manager',
      status: 'Active',
      description: 'Marketing Manager',
      userCount: 1,
      createdBy: 'Jane Smith',
      createdAt: '2023-01-01',
      permissions: ['marketing', 'analytics']
    }
  ];

  // Add this new handler
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.indexOf(active.id);
        const newIndex = items.indexOf(over.id);

        const newItems = [...items];
        newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, active.id);

        return newItems;
      });
    }
  };

  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Roles & Permissions' }
  ];

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        {/* Update the sidebar component to pass the state */}
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />

        {/* Update the content wrapper div className */}
        <div
          className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}
        >
          <div className="flex justify-between items-center py-6 px-4 2xl:px-8">
            <div className="flex flex-col gap-1">
              <h1 className="text-xl font-bold">Roles & Permissions</h1>
              <Breadcrumb items={breadcrumbItems} />
            </div>
            <div className="flex gap-3">
              <button className="btn btn-outline-gray flex items-center gap-2">
                <span className="icon icon-upload-simple text-base" />
                Export
              </button>
              <Link href="/roles/add-edit-role" className="btn btn-primary flex items-center gap-2">
                <span className="icon icon-plus text-base" />
                Add Role
              </Link>
            </div>
          </div>

          <div className="rounded-xl pb-6 px-4 2xl:px-8">
            {/* Status Tabs */}
            {!isSearchFilterOpen && (
              <div className="flex items-center justify-between gap-2.5 bg-white p-2 rounded-tl-xl rounded-tr-xl border border-border-color">
                <InputField
                  type="search"
                  placeholder="Search"
                  value={filterText}
                  marginBottom="mb-0 w-full max-w-[300px]"
                  leftIcon="icon-search"
                  onChange={(e) => setFilterText(e.target.value)}
                  inputClassName="form-control !rounded-lg !min-h-[32px] !max-h-[32px] !py-1.5 !px-3"
                />

                {/* Table or Grid view option */}
                <div className="flex items-center gap-2">
                  <button className="flex items-center gap-2 p-1.5 text-dark-500 rounded-lg border border-border-color hover:border-dark-500 cursor-pointer transition-base" >
                    <span className="icon icon-funnel text-md" />
                  </button>

                  <div className="relative" ref={displayMenuRef}>
                    <button
                      onClick={() => setIsDisplayMenuOpen(!isDisplayMenuOpen)}
                      className="flex items-center gap-2 p-1.5 rounded-lg border border-border-color hover:border-dark-500 cursor-pointer transition-base"
                    >
                      <span className="icon icon-square-split-horizontal text-dark-500 text-md" />
                      {/* <span className="text-xs">View</span> */}
                    </button>

                    {isDisplayMenuOpen && (
                      <div className="absolute top-full right-0 mt-1 w-[180px] bg-white rounded-xl shadow-custom p-2 z-20">
                        {/* <span className="text-sm px-3 py-2 font-bold inline-block">Display Properties</span> */}
                        <DndContext
                          collisionDetection={closestCenter}
                          onDragEnd={handleDragEnd}
                        >
                          <SortableContext
                            items={items}
                            strategy={verticalListSortingStrategy}
                          >
                            <ul className="space-y-1">
                              {items.map((key) => (
                                <SortableItem
                                  key={key}
                                  id={key}
                                  value={key}
                                  checked={displayProperties[key]}
                                  onChange={() =>
                                    setDisplayProperties((prev) => ({
                                      ...prev,
                                      [key]: !prev[key],
                                    }))
                                  }
                                />
                              ))}
                            </ul>
                          </SortableContext>
                        </DndContext>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Orders Table */}
            <RolesAndPermissionsTable data={rolesAndPermissionsData} isLoading={isLoading} paginationPerPage={paginationPerPage} />
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
