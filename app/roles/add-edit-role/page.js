'use client';

import React, { useState } from 'react';
import { Formik } from 'formik';
import PrivateLayout from '@/app/components/layout/PrivateLayout';
import Sidebar from '@/app/components/sidebar/Sidebar';
import InputField from '@/app/components/Inputs/InputField';
import Breadcrumb from '@/app/components/Inputs/Breadcrumb';
import CustomCheckbox from '@/app/components/Inputs/CustomCheckbox';
import Link from 'next/link';
import { ROLE_SCHEMA } from '@/utils/schema';

export default function Page() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [permissions, setPermissions] = useState({
    profile: ['View', 'Update'],
    clients: ['Create', 'View', 'Update', 'Delete', 'Download', 'Import'],
    expiredDemoUsers: ['View', 'Update'],
    adminUsers: ['Create', 'View', 'Update', 'Delete', 'Download', 'Import'],
    demoUsers: ['View', 'Update', 'Download', 'Delete'],
    demoRequests: ['Create', 'Update', 'View', 'Delete', 'Download', 'Calendar', 'Configuration'],
  });
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Roles & Permissions', link: '/roles' },
    { label: 'Add Roles & Permissions' },
  ];

  const [selectedPermissions, setSelectedPermissions] = useState(() => {
    const initial = {};
    Object.entries(permissions).forEach(([section, actions]) => {
      initial[section] = {};
      actions.forEach((action) => {
        initial[section][action] = false;
      });
    });
    return initial;
  });

  const handlePermissionChange = (section, permission, checked) => {
    setSelectedPermissions((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [permission]: checked,
      },
    }));
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />
        <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
          <div className="flex items-center justify-between w-full py-6 px-4 2xl:px-8">
            <div className="flex flex-col gap-1">
              <div className="flex flex-col gap-1">
                <h1 className="text-xl font-bold">Add Roles & Permission</h1>
                <Breadcrumb items={breadcrumbItems} />
              </div>
            </div>
            <div className="flex justify-end gap-3">
              <Link href="/roles" className="btn btn-outline-gray">Cancel</Link>
              <button type="submit" form="role-form" className="btn btn-primary">
                Save Changes
              </button>
            </div>
          </div>

          <div className="w-full h-[calc(100dvh-137px)] overflow-y-auto pb-6 px-4 2xl:px-8">
            <Formik
              initialValues={{ roleName: '', description: '' }}
              validationSchema={ROLE_SCHEMA}
              onSubmit={(values) => {
                const payload = {
                  ...values,
                  permissions: selectedPermissions,
                };
                console.log('Submitted Payload:', payload);
              }}
            >
              {(formik) => (
                <form onSubmit={formik.handleSubmit} id="role-form" className="space-y-4">
                  <div className="card !p-0">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">Role Details</h2>
                    </div>
                    <div className="p-4 grid grid-cols-1">
                      <InputField
                        id="roleName"
                        name="roleName"
                        label="Role Name"
                        placeholder="Enter role name"
                        formik={formik}
                      />
                      <InputField
                        id="description"
                        name="description"
                        label="Description"
                        type="textarea"
                        placeholder="Enter description"
                        formik={formik}
                        rows={4}
                      />
                    </div>
                  </div>

                  <div className="card !p-0">
                    <div className="flex items-center gap-3 p-4">
                      <h2 className="text-base font-semibold">Permissions</h2>
                    </div>
                    <div className="p-4 grid grid-cols-2 gap-4">
                      {Object.entries(permissions).map(([section, actions]) => (
                        <div key={section} className="border border-border-color rounded-xl p-4">
                          <h3 className="text-sm font-semibold capitalize mb-3">
                            {section.replace(/([A-Z])/g, ' $1').trim()}
                          </h3>
                          <div className="flex flex-wrap gap-3">
                            {actions.map((action) => (
                              <CustomCheckbox
                                key={`${section}-${action}`}
                                id={`${section}-${action}`}
                                label={action}
                                className={`px-3 py-2 border rounded-lg ${selectedPermissions[section][action] ? 'border-primary-500/40 bg-primary-500/5' : 'border-border-color'}`}
                                checked={selectedPermissions[section][action]}
                                onChange={(e) =>
                                  handlePermissionChange(section, action, e.target.checked)
                                }
                              />
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </PrivateLayout>
  );
}
