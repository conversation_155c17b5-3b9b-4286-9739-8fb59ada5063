'use client';
import React, { useState, useRef, useEffect } from 'react';
import PrivateLayout from '../components/layout/PrivateLayout';
import MasterTable from '../components/table/MasterTable';
import { Tooltip } from 'react-tooltip';
import ToggleSwitch from '../components/Inputs/ToggleSwitch';
import Sidebar from '../components/sidebar/Sidebar';
import Breadcrumb from '../components/Inputs/Breadcrumb';
import FilterField from '../components/table/FilterField';
import { DndContext, closestCenter } from '@dnd-kit/core';
import {
  SortableContext,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import SortableItem from '../components/table/SortableItem';
import BaseOffCanvas from '../components/offCanvas/BaseOffCanvas';
import InputField from '../components/Inputs/InputField';
import SelectField from '../components/Inputs/SelectField';

const SubscriptionManagement = () => {
  const [filterText, setFilterText] = useState('');
  const displayMenuRef = useRef(null);
  const statusDropdownRef = useRef(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isDisplayMenuOpen, setIsDisplayMenuOpen] = useState(false);
  const [isSearchFilterOpen, setIsSearchFilterOpen] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    packageId: true,
    name: true,
    price: true,
    features: true,
    duration: true,
    status: true
  });
  const [items, setItems] = useState(Object.keys(formData));
  const [tableData, setTableData] = useState([]);
  const breadcrumbItems = [
    { label: 'Dashboard', link: '/dashboard' },
    { label: 'Subscription Packages' }
  ];

  // Update the click outside effect to handle both dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        statusDropdownRef.current &&
        !statusDropdownRef.current.contains(event.target)
      ) {
        setIsFilterOpen(false);
      }
      if (
        displayMenuRef.current &&
        !displayMenuRef.current.contains(event.target)
      ) {
        setIsDisplayMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const columns = [
    {
      name: 'Package ID',
      selector: row => row.packageId,
      sortable: true
    },
    {
      name: 'Name',
      selector: row => row.name,
      sortable: true
    },
    {
      name: 'Price',
      selector: row => row.price,
      sortable: true
    },
    {
      name: 'Duration',
      selector: row => row.duration,
      sortable: true
    },
    {
      name: 'Subscribers',
      selector: row => row.subscribersCount,
      sortable: true
    },
    {
      name: 'Status',
      cell: row => (
        <div className="flex items-center">
          <ToggleSwitch
            checked={row.status}
            onChange={() => handleStatusToggle(row.id)}
          />
        </div>
      )
    },
    {
      name: 'Actions',
      cell: row => (
        <div className="flex gap-2">
          <button
            onClick={() => handleEdit(row)}
            className="icon icon-edit text-lg text-gray-500 hover:text-primary-500"
            data-tooltip-id="edit-tooltip"
            data-tooltip-content="Edit Package"
          />
          <button
            onClick={() => handleDelete(row.id)}
            className="icon icon-delete text-lg text-gray-500 hover:text-red-500"
            data-tooltip-id="delete-tooltip"
            data-tooltip-content="Delete Package"
          />
          <Tooltip id="edit-tooltip" />
          <Tooltip id="delete-tooltip" />
        </div>
      )
    }
  ];

  const fields = [
    {
      type: 'text',
      name: 'packageId',
      label: 'Package ID',
      placeholder: 'Enter package ID',
      required: true
    },
    {
      type: 'text',
      name: 'name',
      label: 'Package Name',
      placeholder: 'Enter package name',
      required: true
    },
    {
      type: 'text',
      name: 'price',
      label: 'Price',
      placeholder: 'Enter price',
      required: true
    },
    {
      type: 'text',
      name: 'features',
      label: 'Features',
      placeholder: 'Enter features (comma separated)',
      required: true
    },
    {
      type: 'select',
      name: 'duration',
      label: 'Duration',
      placeholder: 'Select duration',
      options: [
        { value: 'monthly', label: 'Monthly' },
        { value: 'quarterly', label: 'Quarterly' },
        { value: 'yearly', label: 'Yearly' }
      ],
      required: true
    },
    {
      type: 'toggle',
      name: 'status',
      label: 'Status'
    }
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    // Add your form submission logic here
    const newData = [...tableData, { ...formData, id: Date.now(), subscribersCount: 0 }];
    setTableData(newData);
    setIsModalOpen(false);
    setFormData({
      packageId: '',
      name: '',
      price: '',
      features: '',
      duration: '',
      status: true
    });
  };

  const handleEdit = (row) => {
    setFormData(row);
    setIsModalOpen(true);
  };

  const handleDelete = (id) => {
    setTableData(tableData.filter(item => item.id !== id));
  };

  const handleStatusToggle = (id) => {
    setTableData(tableData.map(item => {
      if (item.id === id) {
        return { ...item, status: !item.status };
      }
      return item;
    }));
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;
    if (active.id !== over.id) {
      const items = [...tableData];
      const [removed] = items.splice(active.id, 1);
      items.splice(over.id, 0, removed);
      setTableData(items);
    }
  };

  return (
    <PrivateLayout>
      <div className="flex rounded-xl mt-[60px] w-full">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          setIsCollapsed={setIsSidebarCollapsed}
        />
      </div>
      <div className={`content-wrapper collapsed-${isSidebarCollapsed} flex flex-col w-full mx-auto py-6 px-8 bg-surface-100 overflow-auto h-[calc(100dvh-60px)] transition-base`}>
        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col gap-1">
            <h1 className="text-xl font-bold">Subscription Packages</h1>
            <Breadcrumb items={breadcrumbItems} />
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => setIsModalOpen(true)}
              className="btn flex items-center gap-2"
            >
              <span className="icon icon-plus text-base" />
              Add New Package
            </button>
          </div>
        </div>

        <div className="rounded-xl">
          <FilterField
            filterText={filterText}
            setFilterText={setFilterText}
            isSearchFilterOpen={isSearchFilterOpen}
            setIsSearchFilterOpen={setIsSearchFilterOpen}
            isDisplayMenuOpen={isDisplayMenuOpen}
            setIsDisplayMenuOpen={setIsDisplayMenuOpen}
            displayMenuRef={displayMenuRef}
          >
            <div className="absolute top-full right-0 mt-1 w-[200px] bg-white rounded-xl shadow-custom p-2 z-20">
              <DndContext
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext
                  items={items}
                  strategy={verticalListSortingStrategy}
                >
                  <ul className="space-y-1">
                    {items.map((key) => (
                      <SortableItem
                        key={key}
                        id={key}
                        value={key}
                        checked={formData[key]}
                        onChange={() =>
                          setFormData((prev) => ({
                            ...prev,
                            [key]: !prev[key],
                          }))
                        }
                      />
                    ))}
                  </ul>
                </SortableContext>
              </DndContext>
            </div>
          </FilterField>
          <MasterTable
            columns={columns}
            data={tableData}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        </div>
      </div>

      {/* Add Subscription Package - OffCanvas */}
      <BaseOffCanvas
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Add Subscription Package"
        size="sm"
      >
        <form onSubmit={handleSubmit}>
          <div className="p-4 h-[calc(100dvh_-_117px)] overflow-y-auto"> 
            {fields.map((field) => {
              switch (field.type) {
                case 'text':
                  return (
                    <InputField
                      key={field.name}
                      label={field.label}
                      name={field.name}
                      value={formData[field.name]}
                      onChange={(e) => setFormData({...formData, [field.name]: e.target.value})}
                      placeholder={field.placeholder}
                      required={field.required}
                    />
                  );
                case 'select':
                  return (
                    <div key={field.name} className="flex flex-col mb-4">
                      <span className="form-label">
                        {field.label}
                      </span>
                      <SelectField
                        label={field.label}
                        name={field.name}
                        className="single-select"
                        placeholder={field.placeholder}
                        value={formData[field.name]}
                        onChange={(e) => setFormData({...formData, [field.name]: e.target.value})}
                        options={field.options}
                        required={field.required}
                      />
                    </div>
                  );
                case 'toggle':
                  return (
                    <div key={field.name} className="flex flex-col gap-2">
                      <span className="text-sm font-medium">{field.label}</span>
                      <ToggleSwitch
                        checked={formData[field.name]}
                        onChange={() => setFormData({...formData, [field.name]: !formData[field.name]})}
                      />
                    </div>
                  );
                default:
                  return null;
              }
            })}
          </div>
          <div className="flex justify-end p-4 gap-2.5 border-t border-border-color">
            <button
              type="button"
              onClick={() => setIsModalOpen(false)}
              className="btn btn-outline-gray"
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Save
            </button>
          </div>
        </form>
      </BaseOffCanvas>
    </PrivateLayout>
  );
};

export default SubscriptionManagement;