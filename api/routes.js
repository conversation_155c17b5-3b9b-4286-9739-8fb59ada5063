/** ***************** 
@Purpose : We can use following api calls and can change api name easily
<AUTHOR> INIC
***************** */
module.exports = {
  //Authentication api path
  GET_USER_REGISTERATION_OTP: '/v1/send-otp',
  VERIFY_USER_REGISTERATION_OTP: '/v1/register',
  USER_LOGIN: '/v1/login',
  FORGET_PASSWORD: '/v1/forgot-password',
  RESET_PASSWORD: '/v1/reset-password',
  VERIFY_FORGET_PASSWORD_OTP: '/v1/verify-otp',
  USER_LOGOUT: '/v1/logout',
  VERIFY_2FA: '/v1/verify-2fa',

  //User onboarding api paths
  USER_ONBOARDING: '/v1/onboarding-master',
  VENDOR_ONBOARDING: '/v1/onboarding',
  INDUSTRY_TAGS: '/v1/industry-tag-franchise',
  VENDOR_DETAILS: '/v1/vendor',
  GET_PHONE_OTP: '/v1/send-phone-otp',
  VERIFY_PHONE_OTP: '/v1/verify-phone-otp',
  FEEDBACK_VENDOR: '/v1/feedback',

  //Products api paths

  GET_PRODUCTS: '/v1/product',
  GET_BRANDS: '/v1/brands',
  GET_ATTRIBUTES: '/v1/attributes',
  ADD_ATTRIBUTES: '/v1/attributes/add-attribute-value',
  ARCHIEVE_PRODUCT: 'archive',
  UPDATE_PREFERENCE: '/v1/preferences',

  CREATE_PRODUCT: '/v1/products',
  EDIT_PRODUCT: '/v1/products',
  GET_PRODUCT: '/v1/products',

  GET_ADDRESS: '/v1/addresses',
  ADD_ADDRESS: '/v1/addresses',

  GET_UNITS: '/v1/units',
  GET_UNIT_MEASUREMENTS: '/v1/units/measurements',
  GET_UNIT_WEIGHTS: '/v1/units/weights',

  ADD_CATEGORY: '/v1/categories',

  CREATE_NEW_PRODUCT: '/v1/product',
  CREATE_VARIANT: '/attributes',
  GET_PRODUCT_DETAILS: '/v1/product/{id}',
  GET_PRODUCT_STYLES: '/v1/product-styles',
  GET_EXTERNAL_PRODUCT_ID_TYPE: '/v1/external-product-types',

  ADD_CATEGORY: '/v1/categories',

  ADD_GENERAL_INFO: '/v1/product/general-info',
  ADD_GENERAL_INFO_EDIT: '/v1/product/{id}/general-info',
  ADD_PRODUCT_DESCRIPTION: '/v1/product/{id}/description',
  ADD_PRODUCT_PRICING: '/v1/product/{id}/pricing',
  ADD_PRODUCT_PROMOTIONS: '/v1/product/{id}/promotions',
  ADD_PRODUCT_SHIPPING_INFO: '/v1/product/{id}/shipping',
  ADD_PRODUCT_POLICIES_INFO: '/v1/product/{id}/policies',
  ADD_PRODUCT_META_INFO: '/v1/product/{id}/meta-information',

  PRODUCT_APPROVAL: '/v1/product/{id}/send-for-approval',

  GET_COUNTRY_OF_ORIGIN: '/v1/countries',

  GET_PRODUCT_DETAILS: '/v1/product/{id}',

  GET_TAXATION_TYPES: '/v1/taxation-types',

  ADD_PROD_COMBO_GEN_INFO: '/v1/product-combos/general-info',

  PROD_COMBO_GEN_INFO_EDIT: '/v1/product-combos/{id}/general-info',

  GET_PROD_COMBO_ID: '/v1/product-combos/{id}',

  GET_VARIANTS: '/v1/product-combos/search-product-sku',

  UPDATE_PRODUCT_COMBO_DETAILS: '/v1/product-combos/{id}/combo-builder',

  GET_VALUE_COMBOS_LIST: '/v1/product-combos',

  // Industry API Paths
 GET_INDUSTRY_MANAGEMENT : '/v1/industry-tags',
 ADD_INDUSTRY_MANAGEMENT : '/v1/industry-tags',
 DELETE_INDUSTRY_MANAGEMENT : 'v1/industry-tags',
 FETCH_INDUSTRY_MANAGEMENT : '/v1/industry-tags',
 UPDATE_INDUSTRY_MANAGEMENT : '/v1/industry-tags',
 
// Brand API Paths
 LIST_ALL_BRAND : '/v1/brands',
 ADD_BRAND : '/v1/brands',
 DELETE_BRAND : 'v1/industry-tags',
 GET_SINGLE_BRAND : '/v1/brands',
 UPDATE_BRAND : '/v1/brands',
 
 // Franchise API Paths
 LIST_ALL_FRANCHISE : '/v1/franchises',
 ADD_FRANCHISE : '/v1/franchises',
 DELETE_FRANCHISE : 'v1/franchises',

 // Attribute API Paths
 LIST_ALL_ATTRIBUTES : '/v1/attributes',

 GET_FRANCHISE : '/v1/franchises',
 UPDATE_FRANCHISE : '/v1/franchises',

 // Vendor API Paths
 LIST_ALL_VENDORS: '/v1/vendors',

};
