// import { setAlert, setUi<PERSON>ey } from "@actions";
import { API_URL, API_URL_ADMIN } from '@/app/components/config'; // project configuration file
// import fetch from "isomorphic-unfetch";
import {
  getCookie,
  showErrorToast,
  showSuccessToast,
  showWarningToast,
  transformResponseHandler,
} from '@/utils/function';
import { cookiesKey } from '@/utils/cookiesStorageKeys';
import { DEFAULT_TIMEOUT } from '@/utils/constant';
import { store } from '@/store';
import { setUiKey } from '@/store/actions/userActions';

// import store from "../store/index"; // Access redux store data

export const googleAuthHandler = async (access_token) => {
  const getHeader = () => ({
    'Content-Type': 'application/json',
    Accept: 'application/json',
    Authorization: `Bearer ${access_token}`,
  });

  var requestOptions = {
    method: 'GET',
    headers: getHeader(),
    redirect: 'follow',
  };

  try {
    const response = await fetch(
      'https://www.googleapis.com/oauth2/v3/userinfo',
      requestOptions
    );
    if (!response.ok) {
      throw new Error('Failed to fetch user information');
    }
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching user information:', error);
    throw error;
  }
};

/** ***************** 
@purpose : Convert Response to JSON
@Parameter : {response, status}
<AUTHOR> INIC
***************** */
const makeJson = async (response, status) => {
  const json = await response.json();
  return Promise.resolve({ ...json, ok: response.ok, statusCode: status });
};
/** ***************** 
@purpose : API Response Handler
@Parameter : {alert, response}
<AUTHOR> INIC
***************** */

const responseHandler = async (res) => {
  const contentType = res && res.headers && res.headers.get('content-type');

  if (contentType && contentType.indexOf('application/json') !== -1) {
    const responseData = await makeJson(res, res.status);
    if (res.ok) {
      showSuccessToast(
        responseData?.data?.message ||
        responseData?.message ||
        'Request Successful'
      );
    } else if (res.status >= 400) {
      showErrorToast(
        responseData?.error?.message ||
        responseData?.message ||
        'Server Error, please try again later.'
      );
    }
    return responseData;
  } else {
    showErrorToast('Something went wrong');
    throw new Error('Invalid response format');
  }
};

/** ***************** 
@purpose : API Request Public Header
@Parameter : {}
<AUTHOR> INIC
***************** */
const getPublicHeaders = () => {
  return {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };
};

/** ***************** 
@purpose : API Request Auth Header
@Parameter : {}
<AUTHOR> INIC
***************** */

export const getAuthHeaders = () => {
  let token = getCookie(cookiesKey?.token);
  return {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    Authorization: `Bearer ${token}`,
  };
};

/** ***************** 
@purpose : Fetch Get API
@Parameter : {data}
<AUTHOR> INIC
***************** */
// export const get = async (...data) => {
//   const [url, alert = false] = data;
//   const response = await fetch(`${API_URL}${url}`, {
//     method: "get",
//     headers: getHeader(),
//   });
//   return response;
// };
export const get = async (
  url,
  params = {},
  alert = false,
  requiredAuth = false
) => {
  const queryParams = new URLSearchParams(params).toString();
  const apiUrl = `${API_URL}${url}${queryParams ? `?${queryParams}` : ''}`;
  const headers = requiredAuth ? getAuthHeaders() : getPublicHeaders();
  const response = await fetch(apiUrl, {
    method: 'GET',
    headers,
  });
  let responseData = await responseHandler(response);
  return responseData;
};

/** ***************** 
@purpose : Fetch Post API
@Parameter : { url,
    alert = true,
    body,
    isLoading = false,
    timeout = DEFAULT_TIMEOUT,}
<AUTHOR> INIC
***************** */

export const getFromAdmin = async (
  url,
  params = {},
  alert = false,
  requiredAuth = false
) => {
  const queryParams = new URLSearchParams(params).toString();
  const apiUrl = `${API_URL_ADMIN}${url}${queryParams ? `?${queryParams}` : ''}`;
  const headers = requiredAuth ? getAuthHeaders() : getPublicHeaders();
  const response = await fetch(apiUrl, {
    method: 'GET',
    headers,
  });
  let responseData = await responseHandler(response);
  return responseData;
};



/** ***************** 
@purpose : Fetch Post API
@Parameter : { url,
    alert = true,
    body,
    isLoading = false,
    timeout = DEFAULT_TIMEOUT,}
<AUTHOR> INIC
***************** */
export const post = async (...data) => {
  const [
    url,
    body,
    requiredAuth = false,
    alert = true,
    isLoading = false,
    timeout = DEFAULT_TIMEOUT,
  ] = data;

  const isFormData = body instanceof FormData;
  const headers = requiredAuth ? getAuthHeaders() : getPublicHeaders();

  // Remove 'Content-Type' if sending FormData (let browser handle it)
  if (isFormData && headers['Content-Type']) {
    delete headers['Content-Type'];
  }

  const response = await fetch(`${API_URL}${url}`, {
    method: 'POST',
    headers,
    body: isFormData ? body : JSON.stringify(body),
    // fetch doesn't support timeout directly — remove if unused
    // You can implement timeout manually if needed using AbortController
  });

  const responseData = await responseHandler(response);
  return responseData;
};
/** ***************** 
@purpose : Fetch Put API
@Parameter : {data}
<AUTHOR> INIC
***************** */
export const put = async (...data) => {
  const [
    url,
    body,
    requiredAuth = true,
    alert = false,
    isLoading = false,
    timeout = DEFAULT_TIMEOUT,
  ] = data;

  const isFormData = body instanceof FormData;
  const headers = requiredAuth ? getAuthHeaders() : getPublicHeaders();
  if (isFormData && headers['Content-Type']) {
    delete headers['Content-Type'];
  }
  const response = await fetch(`${API_URL}${url}`, {
    method: 'PUT',
    headers,
    body: isFormData ? body : JSON.stringify(body),
  });
  const responseData = await responseHandler(response);
  return responseData;
};
/** ***************** 
@purpose : Fetch Delete API
@Parameter : {data}
<AUTHOR> INIC
***************** */
export const remove = async (...data) => {
  const [url, alert = false, isLoading = false, timeout = DEFAULT_TIMEOUT] =
    data;
  if (isLoading) {
    store.dispatch(setUiKey('isLoading', true));
  }

  const response = await fetch(`${API_URL}/${url}`, {
    method: 'delete',
    headers: getAuthHeaders(),
    timeout,
  })
  const responseData = await responseHandler(response)
  return responseData
};
/** ***************** 
@purpose : Fetch Patch API
@Parameter : {data}
<AUTHOR> INIC
***************** */
export const patch = (...data) => {
  const [
    url,
    alert = false,
    body,
    isLoading = false,
    timeout = DEFAULT_TIMEOUT,
  ] = data;
  if (isLoading) {
    store.dispatch(setUiKey('isLoading', true));
  }
  return fetch(`${API_URL}/${url}`, {
    method: 'patch',
    headers: getHeader(),
    body: JSON.stringify(body),
    timeout,
  })
    .then(responseHandler(alert))
    .catch(responseHandler(alert));
};
/** ***************** 
@purpose : Fetch Post API
@Parameter : {data}
<AUTHOR> INIC
***************** */
export const image = (...data) => {
  const [
    url,
    alert = false,
    body,
    isLoading = false,
    timeout = DEFAULT_TIMEOUT,
  ] = data;
  if (isLoading) {
    store.dispatch(setUiKey('isLoading', true));
  }
  return fetch(`${API_URL}/${url}`, {
    method: 'post',
    headers: {},
    body,
    timeout,
  })
    .then(responseHandler(alert))
    .catch(responseHandler(alert));
};
