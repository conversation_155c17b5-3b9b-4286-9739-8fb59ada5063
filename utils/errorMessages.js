/******************* 
  @purpose : To use these constants to show error messages 
  @Parameter : {}
  <AUTHOR> INIC
  ***************** */

export const errorMessages = {
  MOBILE: 'Phone number is required ',
  MOBILE_MATCHES: 'Phone number is not valid',
  MO<PERSON><PERSON>_LENGTH: 'Phone number is  not more than 10 digit',
  OTP: 'OTP is required',
  OTP_DIGIT: 'Must be exactly 4 digits',
  BUSINESS_NAME: 'Business name is required',
  BUSINESS_EMAIL_ID_INVALID: 'Business email Id is invalid',
  BUSINESS_EMAIL_ID_REQUIRED: 'Business email Id is required',
  PASSWORD_REQUIRED: 'Password is required',
  PASSWORD_LENGTH_ERROR: 'Password must be at least 6 characters long',
  INDUSTRY_NAME_REQUIRED: 'Industry name is required',
  BRAND_NAME_REQUIRED: 'Brand name is required',
  FRANCHISE_NAME_REQUIRED: 'Franchise name is required',
  FRANCHISE_PARENT_BRAND_REQUIRED: 'Parent brand is required',
  UNIT_OF_MASTER_SCHEMA_REQUIRED: 'UoM Code is required',
  PRODUCT_STYLE_SCHEMA_REQUIRED: 'Style name is required',
  MANUFACTURER_BRAND_SCHEMA_REQUIRED: 'Manufacturer name is required',
  MANUFACTURER_BRAND_COUNTRY_REQUIRED: 'Country is required',
  ROLE_NAME_REQUIRED: 'Role name is required',
  DESCRIPTION_NAME_REQUIRED: 'Description is required',
};
