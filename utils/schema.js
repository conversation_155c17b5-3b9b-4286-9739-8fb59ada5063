import * as Yup from 'yup';
import { errorMessages } from './errorMessages';
import { InputValidator, ProductType } from './constant';
import { isValidPhoneNumber } from 'react-phone-number-input';

/** ***************** 
@purpose : Register Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const REGISER_SCHEMA = () =>
  Yup.object({
    businessName: Yup.string().required(errorMessages.BUSINESS_NAME),
    businessEmailId: Yup.string()
      .matches(
        InputValidator.emailRegExp,
        errorMessages.BUSINESS_EMAIL_ID_INVALID
      )
      .required(errorMessages.BUSINESS_EMAIL_ID_REQUIRED),
    password: Yup.string()
      .max(12, 'Password must be 8 to 12 characters')
      .min(8, 'Password must be 8 to 12 characters')
      .matches(InputValidator.passwordRegExp, ' ')
      .required(errorMessages.PASSWORD_REQUIRED),
  });

/** ***************** 
@purpose : Login Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */
export const LOGIN_SCHEMA = () =>
  Yup.object({
    businessEmailId: Yup.string()
      .email(errorMessages.BUSINESS_EMAIL_ID_INVALID)
      .required(errorMessages.BUSINESS_EMAIL_ID_REQUIRED),
    password: Yup.string().required(errorMessages.PASSWORD_REQUIRED),
  });

/** ***************** 
@purpose : Industry Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const INDUSTRY_SCHEMA = () =>
  Yup.object({
    industryName: Yup.string()
      .required(errorMessages.INDUSTRY_NAME_REQUIRED),
    status: Yup.boolean().required(),
  });

/** ***************** 
@purpose : Brand Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const BRAND_SCHEMA = () =>
  Yup.object({
    brandName: Yup.string()
      .required(errorMessages.BRAND_NAME_REQUIRED),
    status: Yup.boolean().required(),
  });

/** ***************** 
@purpose : Franchise Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const FRANCHISE_SCHEMA = () =>
  Yup.object({
    franchiseName: Yup.string()
      .required(errorMessages.FRANCHISE_NAME_REQUIRED),
    parentBrand: Yup.string()
      .required(errorMessages.FRANCHISE_PARENT_BRAND_REQUIRED),
    status: Yup.boolean().required(),
  });

/** ***************** 
@purpose : Unit of Master Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const UNIT_OF_MASTER_SCHEMA = () =>
  Yup.object({
    code: Yup.string()
      .required(errorMessages.UNIT_OF_MASTER_SCHEMA_REQUIRED)
      .matches(/^[A-Z]{2,4}$/, 'UoM code must be exactly 2–4 uppercase letters'),
    fullName: Yup.string(),
    status: Yup.boolean()
  });

/** ***************** 
@purpose : Role & Permission Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const ROLE_SCHEMA = Yup.object({
  roleName: Yup.string().required(errorMessages.ROLE_NAME_REQUIRED),
  description: Yup.string().required(errorMessages.DESCRIPTION_NAME_REQUIRED),
});

/** ***************** 
@purpose : Product Style Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const PRODUCT_STYLE_SCHEMA = () =>
  Yup.object({
    styleName: Yup.string()
      .required(errorMessages.PRODUCT_STYLE_SCHEMA_REQUIRED)
      .test(
        'normalized-case',
        'First letter should be uppercase and rest lowercase',
        (value) => {
          if (!value) return false;
          const normalized = value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
          return value === normalized;
        }
      ),
  });

/** ***************** 
@purpose : Product Style Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const MANUFACTURER_BRAND_SCHEMA = () =>
  Yup.object().shape({
    manufacturerName: Yup.string().required(errorMessages.MANUFACTURER_BRAND_SCHEMA_REQUIRED),
    countryOfOrigin: Yup.string().required(errorMessages.MANUFACTURER_BRAND_COUNTRY_REQUIRED),
    status: Yup.boolean(),
  });

/** ***************** 
@purpose : Login 2Fa Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */
export const LOGIN_2FA_SCHEMA = () =>
  Yup.object({
    code: Yup.string()
      .length(6, 'OTP must be 6 digits')
      .matches(/^\d+$/, 'OTP must contain only numbers')
      .required('OTP is required'),
  });

/** ***************** 
@purpose : Foget Password Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const FORGET_PASSWORD_SCHEMA = {
  EMAIL: () =>
    Yup.object({
      email: Yup.string()
        .email(errorMessages.BUSINESS_EMAIL_ID_INVALID)
        .required(errorMessages.BUSINESS_EMAIL_ID_REQUIRED),
    }),

  PASSWORD: () =>
    Yup.object({
      password: Yup.string()
        .max(12, 'Password must be 8 to 12 characters')
        .min(8, 'Password must be 8 to 12 characters')
        .matches(InputValidator.passwordRegExp, ' ')
        .required('Password is required'),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('password'), null], 'Passwords must match')
        .required('Confirm password is required'),
    }),
};

/** ***************** 
@purpose : OTP Verification Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const OTP_VERIFICATION_SCHEMA = () =>
  Yup.object({
    otp: Yup.string()
      .length(6, 'OTP must be 6 digits')
      .matches(/^\d+$/, 'OTP must contain only numbers')
      .required('OTP is required'),
  });

/** ***************** 
@purpose : User Onboarding Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const SELLER_INFORMATION_SCHEMA = Yup.object({
  firstName: Yup.string().required('First name is required'),
  lastName: Yup.string().required('Last name is required'),
  countryOfCitizenship: Yup.string().required(
    'Country of citizenship is required'
  ),
  SelectID: Yup.string().required('ID is required'),
  address: Yup.string().required('Address is required'),
  city: Yup.string().required('City is required'),
  state: Yup.string().required('State is required'),
  postalCode: Yup.string().required('Postal code is required'),
  primaryContactPerson: Yup.string().required('Please select an option'),
  phone: Yup.string().when(
    ['phoneNumberExists', 'isPhoneVerified'],
    ([phoneNumberExists, isPhoneVerified]) => {
      const exists = phoneNumberExists === true || phoneNumberExists === 'true';
      const verified = isPhoneVerified === true || isPhoneVerified === 'true';
      if (!exists) {
        return Yup.string().required('Phone number is required');
      } else if (!verified) {
        return Yup.string().test(
          'is-verified',
          'Phone number is not verified',
          () => false
        );
      }

      return Yup.string();
    }
  ),

  isPhoneVerified: Yup.string().required('Phone number is not verified'),
  isPrimaryContact: Yup.string().required('Please select an option'),
  terms: Yup.bool().oneOf([true], 'You must accept the terms'),
  ssnNumber: Yup.string().when('isPrimaryContact', {
    is: 'Yes',
    then: (schema) => schema.required('Enter SSN number & verify it'),
    otherwise: (schema) => schema.notRequired(),
  }),
  UploadID: Yup.mixed().when('SelectID', (value) => {
    return value[0]
      ? Yup.mixed().required('ID image is required')
      : Yup.mixed().notRequired();
  }),
  // .test('fileSize', 'File too large', value => value && value.size > 5 * 1024 * 1024) // max 5MB
  // .test('fileFormat', 'Unsupported Format', value => value && ['image/jpg', 'image/jpeg', 'image/png'].includes(value.type))
});

export const BUSINESS_INFORMATION_SCHEMA = Yup.object().shape({
  businessType: Yup.string().required('Business Type is required'),

  registeredBusiness: Yup.string().required('Business Name is required'),

  industry: Yup.array()
    .min(1, 'Please select at least one industry')
    .required('Industry is required'),

  registeredBusiness: Yup.string().required(
    'Registered Business Name is required'
  ),
  yearEstablishment: Yup.number().required('Year of Establishment is required'),

  numberEmployees: Yup.string().required('Number of Employees is required'),
  primaryLocation: Yup.string().required('Primary Location is required'),
  taxID: Yup.string().required('Tax ID is required'),
  businessLicenceNumber: Yup.string().required(
    'Business License Number is required'
  ),
  isApprovedVendor: Yup.string().when('industry', {
    is: (industries) =>
      Array.isArray(industries) &&
      industries.some((item) => {
        if (typeof item === 'string') return item.toLowerCase() === 'hotel';
        if (typeof item?.Label === 'string')
          return item.Label.toLowerCase() === 'hotel';
        return false;
      }),
    then: () => Yup.string().required('This field is required'),
    otherwise: () => Yup.string().notRequired(),
  }),

  // isApprovedVendor: Yup.string()
  //   .oneOf(["Yes", "No"], "Please select Yes or No")
  //   .required("This field is required"),

  hotelFranchise: Yup.array().when('isApprovedVendor', {
    is: 'Yes',
    then: (schema) =>
      schema.min(1, 'Please select at least one Hotel/Franchise'),
    otherwise: (schema) => schema.notRequired(),
  }),

  businessPhoneNumber: Yup.string()
    .required('Phone number is required')
    .test('isValidPhone', 'Invalid phone number', function (value) {
      const { businessCountryCode } = this.parent; // Get country code from the same form values
      const fullNumber = `${businessCountryCode}${value}`;
      return isValidPhoneNumber(fullNumber); // Validate the full number
    }),
  businessWebsite: Yup.string().notRequired().url('Please enter a valid URL'),
});

export const PRODUCT_SCHEMA = Yup.object().shape({
  title: Yup.string()
    .required('Product Title is required')
    .max(150, 'Product Title must be at most 150 characters'),

  category: Yup.string().required('Category is required'),
  sku: Yup.string().required('SKU is required'),
  productSummary: Yup.string().required('Product Summary is required'),
  productDescription: Yup.string().required('Product Description is required'),
  brandName: Yup.string().required('Brand Name is required'),

  modelNumber: Yup.string()
    .max(50, 'Model Number must be at most 50 characters')
    .required('Model Number is required'),

  productImages: Yup.array()
    .min(1, 'Please upload at least one product image')
    .required('Product images are required'),

  basePrice: Yup.number()
    .typeError('Base Price must be a number')
    .required('Base Price is required')
    .min(0.01, 'Base Price must be at least $0.01'),

  industryTags: Yup.array().min(1, 'At least one Industry Tag is required'),

  hotelFranchiseName: Yup.array().when(
    'industryTags',
    ([...tags] = [], schema) => {
      const hasHotel = tags?.some(
        (tag) => tag?.label?.toLowerCase() === 'hotel'
      );
      return hasHotel
        ? schema.min(1, 'Please select at least one hotel/franchise')
        : schema.notRequired();
    }
  ),

  productVideoUrl: Yup.string()
    .trim()
    .nullable()
    .matches(
      /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/|vimeo\.com\/)[\w\-]+(&\S*)?$/,
      'Enter a valid YouTube or Vimeo URL'
    )
    .notRequired(),

  productVisibilityUrl: Yup.string()
    .trim()
    .nullable()
    .url('Enter a valid URL')
    .notRequired(),

  totalQuantity: Yup.number()
    .typeError('Total Quantity must be a number')
    .required('Total Quantity is required'),

  preferredCarrier: Yup.string().trim().nullable().notRequired(),

  hasOffer: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Offer selection is required'),

  offerType: Yup.string().when('hasOffer', {
    is: 'Yes',
    then: (schema) => schema.required('Offer Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  discountName: Yup.string().when('hasOffer', {
    is: 'Yes',
    then: (schema) => schema.required('Discount Name is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  discountType: Yup.string().when(['hasOffer', 'offerType'], {
    is: (hasOffer, offerType) =>
      hasOffer === 'Yes' && offerType === 'Buy X get Y free',
    then: (schema) => schema.required('Discount Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  buyQuantity: Yup.number().when('hasOffer', {
    is: 'Yes',
    then: (schema) =>
      schema
        .required('Buy quantity is required')
        .typeError('Buy quantity must be a number')
        .min(1, 'Must be at least 1'),
    otherwise: (schema) => schema.notRequired(),
  }),

  getQuantity: Yup.number().when('hasOffer', {
    is: 'Yes',
    then: (schema) =>
      schema
        .required('Get quantity is required')
        .typeError('Get quantity must be a number')
        .min(1, 'Must be at least 1'),
    otherwise: (schema) => schema.notRequired(),
  }),

  promoCode: Yup.string().when('hasOffer', {
    is: 'Yes',
    then: (schema) => schema.required('Promo Code is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  discountValidity: Yup.mixed().when('hasOffer', {
    is: 'Yes',
    then: () =>
      Yup.array()
        .of(Yup.date().typeError('Invalid date'))
        .test(
          'valid-range',
          'Please select a valid start and end date',
          function (value) {
            if (
              !Array.isArray(value) ||
              value.length !== 2 ||
              !value[0] ||
              !value[1]
            ) {
              return this.createError({
                message: 'Please select a start and end date',
              });
            }

            const [start, end] = value;
            if (new Date(start) >= new Date(end)) {
              return this.createError({
                message: 'Start date must be before end date',
              });
            }

            return true;
          }
        ),
    otherwise: () => Yup.mixed().notRequired(),
  }),
  hasWarranty: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Please specify warranty availability'),

  durationType: Yup.string().when('hasWarranty', {
    is: 'Yes',
    then: (schema) => schema.required('Duration Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  durationValue: Yup.string().when('hasWarranty', {
    is: 'Yes',
    then: (schema) => schema.required('Duration Value is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  acceptReturns: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Please specify return policy availability'),

  returnDays: Yup.string().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Return days are required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  returnPolicy: Yup.string().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Return policy is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  policyFile: Yup.mixed().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Policy document is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  validityFrom: Yup.date().nullable().required('Duration date is required'),
  // .when('lifeTimeValidity', {
  //   is: 'Yes',
  //   then: (schema) => schema.required('Duration date is required'),
  //   otherwise: (schema) => schema.notRequired(),
  // })
  validityTo: Yup.date()
    .nullable()
    .transform((value, originalValue) => {
      // Convert empty string to null
      return originalValue === '' ? null : value;
    })
    .when(
      ['lifeTimeValidity', 'validityFrom'],
      ([lifeTimeValidity, validityFrom], schema) => {
        if (lifeTimeValidity === 'Yes') {
          return schema.notRequired();
        }

        const isFromValid =
          !!validityFrom && !isNaN(new Date(validityFrom).getTime());

        return isFromValid
          ? schema
            .required('Duration to date is required')
            .min(
              Yup.ref('validityFrom'),
              'Duration to date must be after From date'
            )
          : schema.notRequired();
      }
    ),

  returnPolicy: Yup.string().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Return policy is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  policyFile: Yup.mixed().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Policy document is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  minLeadTime: Yup.number()
    .required('Minimum lead time is required')
    .min(0, 'Minimum must be 0 or more'),

  maxLeadTime: Yup.number().moreThan(
    Yup.ref('minLeadTime'),
    'Max lead time must be greater than min lead time'
  ),
});

export const PRODUCT_DRAFT_SCHEMA = Yup.object().shape({
  title: Yup.string()
    .max(150, 'Product Title must be at most 150 characters')
    .required('Product Title  is required'),

  category: Yup.string().required('Category is required'),
  minLeadTime: Yup.number().min(0, 'Minimum must be 0 or more'),
  maxLeadTime: Yup.number().moreThan(
    Yup.ref('minLeadTime'),
    'Max lead time must be greater than min lead time'
  ),
});

/** ***************** 
@purpose : Add Variant Validation Schema
<AUTHOR> INIC
@Parameter : {lang} 
***************** */

export const VARIANTS_ADD_SCHEMA = () => {
  return Yup.object().shape({
    skuSuffix: Yup.string().required('SKU Suffix is required'),
    variations: Yup.array().of(
      Yup.object().shape({
        upc: Yup.string().required('UPC is required'),
        images: Yup.array().min(1, 'At least one image is required'),
      })
    ),
  });
};

export const VARIANTS_CONFIGURATION_ADD_SCHEMA = (attributesValue = []) => {
  return Yup.object().shape({
    selectedVariantsAttributes: Yup.array()
      .min(1, 'Please add at least one variation')
      .of(
        Yup.object().shape({
          sku: Yup.string().required('SKU is required'),
          upc: Yup.string().required('UPC is required'),
          images: Yup.array()
            .min(1, 'At least one image is required')
            .required(),
          ...Object.fromEntries(
            attributesValue.map((attr) => [
              attr.value,
              Yup.string().required(`${attr.label} is required`),
            ])
          ),
        })
      ),
  });
};

export const ATTRIBUES_AND_SKU_SUFFIX_SCHEMA = () => {
  return Yup.object().shape({
    selectedAttributes: Yup.array().min(
      1,
      'Please select at least one attribute'
    ),
    hasVariations: Yup.boolean().notRequired(),
    selectedAttributesValues: Yup.array().of(
      Yup.object().shape({
        name: Yup.object()
          .shape({
            label: Yup.string().required('Attribute name is required'),
            value: Yup.string().required('Attribute ID is required'),
          })
          .required('Attribute name is required'),
        values: Yup.array()
          .of(
            Yup.object().shape({
              label: Yup.string().required('Value label is required'),
              value: Yup.string().required('Value ID is required'),
            })
          )
          .min(1, 'Please select at least one value for this attribute')
          .required('Values are required'),
      })
    ),
    singleProductSku: Yup.string().when('hasVariations', {
      is: (val) => val === false || val === 'false',
      then: (schema) => schema.required('SKU is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    singleProductUpc: Yup.string().when('hasVariations', {
      is: (val) => val === false || val === 'false',
      then: (schema) => schema.required('UPC is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    selectedVariantsAttributes: Yup.array().when('hasVariations', {
      is: true,
      then: (schema) =>
        schema
          .min(1, 'Please add at least one variant')
          .required('Variants are required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    skuSuffix: Yup.string().when('hasVariations', {
      is: true,
      then: (schema) => schema.required('SKU Suffix is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
};

export const GENERAL_INFORMATION_SCHEMA = Yup.object().shape({
  title: Yup.string()
    .required('Product Title is required')
    .max(150, 'Product Title must be at most 150 characters'),

  category: Yup.string().required('Category is required'),
  noProductId: Yup.boolean().notRequired(),

  externalProductIdType: Yup.string().when('noProductId', {
    is: (value) => {
      return value == false || value == undefined;
    },
    then: (schema) => schema.required('External ProductId Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  externalProductId: Yup.string().when('noProductId', {
    is: (value) => {
      return value == false || value == undefined;
    },
    then: (schema) => schema.required('External ProductId is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  modelNumber: Yup.string()
    .max(50, 'Model Number must be at most 50 characters')
    .required('Model Number is required'),

  productImages: Yup.array()
    .min(1, 'Please upload at least one product image')
    .required('Product images are required'),

  coverImage: Yup.array()
    .min(1, 'Please select a cover image')
    .required('Cover image is required'),

  industryTags: Yup.array().min(1, 'At least one Industry Tag is required'),

  hotelFranchiseName: Yup.array().when(
    'industryTags',
    ([...tags] = [], schema) => {
      const hasHotel = tags?.some(
        (tag) => tag?.label?.toLowerCase() === 'hotel'
      );
      return hasHotel
        ? schema.min(1, 'Please select at least one hotel/franchise')
        : schema.notRequired();
    }
  ),

  productVideoUrl: Yup.string()
    .required('Product video URL is required')
    .test(
      'is-valid-video-url',
      'Please enter a valid YouTube or Vimeo URL',
      (value) => {
        if (!value) return false;
        const youtubeRegex =
          /^(https?:\/\/)?(www\.)?(youtube\.com|youtu\.be)\/.+$/;
        const vimeoRegex = /^(https?:\/\/)?(www\.)?vimeo\.com\/.+$/;
        return youtubeRegex.test(value) || vimeoRegex.test(value);
      }
    ),
  productStyle: Yup.string().required('Product style is required'),
  manufacturerPartNumber: Yup.string().required(
    'Manufacturer part number is required'
  ),

  noBrandOrManufacturer: Yup.boolean().notRequired(),
  brandName: Yup.string().when('noBrandOrManufacturer', {
    is: (value) => {
      return value == false || value == undefined;
    },
    then: (schema) => schema.required('Brand Name is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export const PRODUCT_DESCRIPTION_SCHEMA = Yup.object().shape({
  productDescription: Yup.string()
    .trim()
    .required('Product Description is required'),
  keyHighlights: Yup.array()
    .test('debug-array', 'Invalid array', function (value) {
      return true;
    })
    .of(
      Yup.string()
        .trim()
        .required('Highlight is required')
        .test('debug-highlight', 'Invalid highlight', function (value) {
          return true;
        })
    )
    .min(1, 'At least one highlight is required'),

  productDocument: Yup.mixed().test(
    'fileExists',
    'Product Specifications is required',
    (value) => {
      return value instanceof File; // ✅ File object must be present
    }
  ),
});

export const getPricingSchema = (module, tableData) =>
  Yup.object().shape({
    chargeTax: Yup.boolean().required(),
    taxationType: Yup.string().when('chargeTax', {
      is: true,
      then: (schema) => schema.required('Taxation type is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
    priceAfterTax: Yup.number()
      .typeError('Price after tax must be a number')
      .when('chargeTax', {
        is: true,
        then: (schema) => schema.required('Price after tax is required'),
        otherwise: (schema) => schema.notRequired(),
      }),

    samePriceForAll: Yup.boolean().required(),
    costPrice: Yup.number()
      .typeError('Cost price must be a number')
      .min(0, 'Cost price cannot be negative')
      .required('Cost price is required'),

    negotiationAllowed: Yup.boolean().required(),
    autoAcceptThreshold: Yup.number()
      .typeError('Auto accept threshold must be a number')
      .when('negotiationAllowed', {
        is: true,
        then: (schema) => schema.required('Auto accept threshold is required'),
        otherwise: (schema) => schema.notRequired(),
      }),

    minimumOrderQuantity: Yup.number()
      .typeError('Minimum order quantity must be a number')
      .min(1, 'Must be at least 1')
      .required('Minimum order quantity is required'),

    outOfStockThreshold: Yup.number()
      .typeError('Out of stock threshold must be a number')
      .min(0, 'Cannot be negative')
      .required('Out of stock threshold is required'),

    restockingThreshold: Yup.number()
      .typeError('Restocking threshold must be a number')
      .min(0, 'Cannot be negative')
      .required('Restocking threshold is required'),

    backordersAllowed: Yup.string()
      .oneOf(['Yes', 'No'])
      .required('Backorders allowed is required'),

    negotiationQuantity: Yup.number().when('negotiationAllowed', {
      is: true,
      then: (schema) => schema.required('Negotiation quantity is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
    skuLabel: Yup.string().when('$module', {
      is: (val) => {
        return module === ProductType?.BUNDLE;
      },
      then: (schema) => schema.required('Sku Label is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    defaultSku: Yup.string().when('$module', {
      is: (val) => {
        return tableData.length > 0;
      },
      then: (schema) => schema.required('default sku is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

export const PRICING_SCHEMA = Yup.object().shape({
  chargeTax: Yup.boolean().required(),
  taxationType: Yup.string().when('chargeTax', {
    is: true,
    then: (schema) => schema.required('Taxation type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  priceAfterTax: Yup.number()
    .typeError('Price after tax must be a number')
    .when('chargeTax', {
      is: true,
      then: (schema) => schema.required('Price after tax is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

  samePriceForAll: Yup.boolean().required(),

  costPrice: Yup.number()
    .typeError('Cost price must be a number')
    .min(0, 'Cost price cannot be negative')
    .required('Cost price is required'),

  negotiationAllowed: Yup.boolean().required(),
  autoAcceptThreshold: Yup.number()
    .typeError('Auto accept threshold must be a number')
    .when('negotiationAllowed', {
      is: true,
      then: (schema) => schema.required('Auto accept threshold is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

  minimumOrderQuantity: Yup.number()
    .typeError('Minimum order quantity must be a number')
    .min(1, 'Must be at least 1')
    .required('Minimum order quantity is required'),

  outOfStockThreshold: Yup.number()
    .typeError('Out of stock threshold must be a number')
    .min(0, 'Cannot be negative')
    .required('Out of stock threshold is required'),

  restockingThreshold: Yup.number()
    .typeError('Restocking threshold must be a number')
    .min(0, 'Cannot be negative')
    .required('Restocking threshold is required'),

  backordersAllowed: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Backorders allowed is required'),

  negotiationQuantity: Yup.number().when('negotiationAllowed', {
    is: true,
    then: (schema) => schema.required('Negotiation quantity is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  skuLabel: Yup.string().when('$module', {
    is: (val) => {
      return val === ProductType?.BUNDLE;
    },
    then: (schema) => schema.required('Label is required for product set'),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export const PRICING_SCHEMA_WITH_TIER = Yup.object().shape({
  chargeTax: Yup.boolean().required(),
  taxationType: Yup.string().when('chargeTax', {
    is: true,
    then: (schema) => schema.required('Taxation type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  priceAfterTax: Yup.number()
    .typeError('Price after tax must be a number')
    .when('chargeTax', {
      is: true,
      then: (schema) => schema.required('Price after tax is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

  samePriceForAll: Yup.boolean().required(),

  defaultSku: Yup.string().required('default sku is required'),

  costPrice: Yup.number()
    .typeError('Cost price must be a number')
    .min(0, 'Cost price cannot be negative')
    .required('Cost price is required'),

  negotiationAllowed: Yup.boolean().required(),
  autoAcceptThreshold: Yup.number()
    .typeError('Auto accept threshold must be a number')
    .when('negotiationAllowed', {
      is: true,
      then: (schema) => schema.required('Auto accept threshold is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

  minimumOrderQuantity: Yup.number()
    .typeError('Minimum order quantity must be a number')
    .min(1, 'Must be at least 1')
    .required('Minimum order quantity is required'),

  outOfStockThreshold: Yup.number()
    .typeError('Out of stock threshold must be a number')
    .min(0, 'Cannot be negative')
    .required('Out of stock threshold is required'),

  restockingThreshold: Yup.number()
    .typeError('Restocking threshold must be a number')
    .min(0, 'Cannot be negative')
    .required('Restocking threshold is required'),

  backordersAllowed: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Backorders allowed is required'),

  negotiationQuantity: Yup.number().when('negotiationAllowed', {
    is: true,
    then: (schema) => schema.required('Negotiation quantity is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export const PROMOTION_AND_DISCOUNTS_SCHEMA = Yup.object().shape({
  hasOffer: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Offer selection is required'),

  offerType: Yup.string().when('hasOffer', {
    is: 'Yes',
    then: (schema) => schema.required('Offer Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  discountName: Yup.string().when('hasOffer', {
    is: 'Yes',
    then: (schema) => schema.required('Discount Name is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  discountType: Yup.string().when(['hasOffer', 'offerType'], {
    is: (hasOffer, offerType) => {
      return hasOffer === 'Yes' && offerType === 'discount';
    },
    then: (schema) => schema.required('Discount Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  buyQuantity: Yup.number().when('hasOffer', {
    is: 'Yes',
    then: (schema) =>
      schema
        .required('Buy quantity is required')
        .typeError('Buy quantity must be a number')
        .min(1, 'Must be at least 1'),
    otherwise: (schema) => schema.notRequired(),
  }),

  getQuantity: Yup.number().when('hasOffer', {
    is: 'Yes',
    then: (schema) =>
      schema
        .required('Get quantity is required')
        .typeError('Get quantity must be a number')
        .min(1, 'Must be at least 1'),
    otherwise: (schema) => schema.notRequired(),
  }),

  promoCode: Yup.string().when('hasOffer', {
    is: 'Yes',
    then: (schema) => schema.required('Promo Code is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  discountValidityFrom: Yup.date()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .when('hasOffer', {
      is: 'Yes',
      then: (schema) =>
        schema
          .typeError('Invalid start date')
          .required('Start date is required if offer is enabled'),
      otherwise: (schema) => schema.notRequired(),
    }),

  discountValidityTo: Yup.date()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .when(['hasOffer', 'discountValidityFrom'], {
      is: (hasOffer, fromDate) => hasOffer === 'Yes' && !!fromDate,
      then: (schema) =>
        schema
          .typeError('Invalid end date')
          .required('End date is required if start date is selected')
          .test(
            'end-after-start',
            'End date must be after start date',
            function (toDate) {
              const { discountValidityFrom } = this.parent;
              if (!discountValidityFrom || !toDate) return true;
              return new Date(toDate) > new Date(discountValidityFrom);
            }
          ),
      otherwise: (schema) => schema.notRequired(),
    }),

  selectedVariants: Yup.array().when('hasOffer', {
    is: 'Yes',
    then: (schema) =>
      schema
        .min(1, 'At least one variant must be selected')
        .required('Attributes are required'),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export const PROMOTION_AND_DISCOUNTS_SCHEMA_WITHOUT_VARIANT =
  Yup.object().shape({
    hasOffer: Yup.string()
      .oneOf(['Yes', 'No'])
      .required('Offer selection is required'),

    offerType: Yup.string().when('hasOffer', {
      is: 'Yes',
      then: (schema) => schema.required('Offer Type is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    discountName: Yup.string().when('hasOffer', {
      is: 'Yes',
      then: (schema) => schema.required('Discount Name is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    discountType: Yup.string().when(['offerType', 'hasOffer'], {
      is: (hasOffer, offerType) => {
        return hasOffer === 'Yes' && offerType === 'discount';
      },
      then: (schema) => schema.required('Discount Type is required'),
      otherwise: (schema) => schema.notRequired(),
    }),

    buyQuantity: Yup.number().when('hasOffer', {
      is: 'Yes',
      then: (schema) =>
        schema
          .required('Buy quantity is required')
          .typeError('Buy quantity must be a number')
          .min(1, 'Must be at least 1'),
      otherwise: (schema) => schema.notRequired(),
    }),

    getQuantity: Yup.number().when('hasOffer', {
      is: 'Yes',
      then: (schema) =>
        schema
          .required('Get quantity is required')
          .typeError('Get quantity must be a number')
          .min(1, 'Must be at least 1'),
      otherwise: (schema) => schema.notRequired(),
    }),

    promoCode: Yup.string().when('hasOffer', {
      is: 'Yes',
      then: (schema) => schema.required('Promo Code is required'),
      otherwise: (schema) => schema.notRequired(),
    }),
    discountValidityFrom: Yup.date()
      .nullable()
      .transform((value, originalValue) =>
        originalValue === '' ? null : value
      )
      .when('hasOffer', {
        is: 'Yes',
        then: (schema) =>
          schema
            .typeError('Invalid start date')
            .required('Start date is required if offer is enabled'),
        otherwise: (schema) => schema.notRequired(),
      }),

    discountValidityTo: Yup.date()
      .nullable()
      .transform((value, originalValue) =>
        originalValue === '' ? null : value
      )
      .when(['hasOffer', 'discountValidityFrom'], {
        is: (hasOffer, fromDate) => hasOffer === 'Yes' && !!fromDate,
        then: (schema) =>
          schema
            .typeError('Invalid end date')
            .required('End date is required if start date is selected')
            .test(
              'end-after-start',
              'End date must be after start date',
              function (toDate) {
                const { discountValidityFrom } = this.parent;
                if (!discountValidityFrom || !toDate) return true;
                return new Date(toDate) > new Date(discountValidityFrom);
              }
            ),
        otherwise: (schema) => schema.notRequired(),
      }),
  });

export const PACKAGING_INFORMATION_SCHEMA = Yup.object().shape({
  preferredCarrier: Yup.string().notRequired(),
  shippingCarrier: Yup.string().when('preferredCarrier', {
    is: 'seller',
    then: (schema) =>
      schema.required(
        'Shipping Carrier is required when using by vendor option'
      ),
    otherwise: (schema) => schema.notRequired(),
  }),
  minLeadTime: Yup.number().min(0, 'Minimum must be 0 or more'),
  maxLeadTime: Yup.number().moreThan(
    Yup.ref('minLeadTime'),
    'Max lead time must be greater than min lead time'
  ),
  product_unit: Yup.string().required('Product unit is required'),
  weight: Yup.number()
    .typeError('Weight must be a number')
    .required('Weight is required'),
  weight_unit: Yup.string().when('weight', {
    is: (weight) => {
      return !!weight;
    },
    then: (schema) => schema.required('Weight unit is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  dimensions_length: Yup.string().required('Length is required'),
  dimensions_width: Yup.string().required('Width is required'),
  dimensions_height: Yup.string().required('Height is required'),
  minLeadTime: Yup.string().required('Minimum lead time is required'),
  maxLeadTime: Yup.string().required('Maximum lead time is required'),
  preferredCarrier: Yup.string().required('Preferred carrier is required'),
  shippingCost: Yup.string().when('isFreeDelivery', {
    is: (val) => {
      return val == false || val == null;
    },
    then: (schema) => schema.required('Shipping cost is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  dimensions_unit: Yup.string().when(
    ['dimensions_height', 'dimensions_width', 'dimensions_length'],
    {
      is: (height, width, length) => {
        // Receive individual values
        return !!length && !!width && !!height; // Check if they are truthy
      },
      then: (schema) => schema.required('Dimension units are required'),
      otherwise: (schema) => schema.notRequired(),
    }
  ),

  handlingFee: Yup.string().required('Handling fee is required'),
  availableShipping: Yup.string().required('Available shipping is required'),
  pickupAddress: Yup.string().required('Location is required'),
  components: Yup.array()
    .test('debug-array', 'Invalid array', function (value) {
      return true;
    })
    .of(
      Yup.string()
        .trim()
        .required('Component is required')
        .test('debug-highlight', 'Invalid highlight', function (value) {
          return true;
        })
    )
    .min(1, 'At least one highlight is required'),
});

export const RESIDENTIAL_ADDRESS_SECTION = Yup.object().shape({
  address: Yup.string().required('Address is required'),
  city: Yup.string().required('City is required'),
  state: Yup.string().required('State is required'),
  postalCode: Yup.string().required('Postal code is required'),
  locationLabel: Yup.string().required('Location label is Required'),
});

export const PROUDCT_POLICIES_SCHEMA = Yup.object().shape({
  countryOfOrigin: Yup.string().required('Country of origin is required'),

  tariffPercentage: Yup.number()
    .typeError('Tariff percentage must be a number')
    .min(0, 'Tariff percentage cannot be negative')
    .required('Tariff percentage is required'),

  discountValidity: Yup.mixed().when('hasOffer', {
    is: 'Yes',
    then: () =>
      Yup.array()
        .of(Yup.date().typeError('Invalid date'))
        .test(
          'valid-range',
          'Please select a valid start and end date',
          function (value) {
            if (
              !Array.isArray(value) ||
              value.length !== 2 ||
              !value[0] ||
              !value[1]
            ) {
              return this.createError({
                message: 'Please select a start and end date',
              });
            }

            const [start, end] = value;
            if (new Date(start) >= new Date(end)) {
              return this.createError({
                message: 'Start date must be before end date',
              });
            }

            return true;
          }
        ),
    otherwise: () => Yup.mixed().notRequired(),
  }),
  hasWarranty: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Please specify warranty availability'),

  durationType: Yup.string().when('hasWarranty', {
    is: 'Yes',
    then: (schema) => schema.required('Duration Type is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  durationValue: Yup.string().when('hasWarranty', {
    is: 'Yes',
    then: (schema) => schema.required('Duration Value is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  acceptReturns: Yup.string()
    .oneOf(['Yes', 'No'])
    .required('Please specify return policy availability'),

  returnDays: Yup.string().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Return days are required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  returnPolicy: Yup.string().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Return policy is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  policyFile: Yup.mixed().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Policy document is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
  validityFrom: Yup.date()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .required('Start date is required'),

  validityTo: Yup.date()
    .nullable()
    .transform((value, originalValue) => (originalValue === '' ? null : value))
    .when('lifeTimeValidity', {
      is: 'Yes',
      then: (schema) => schema.notRequired(),
      otherwise: (schema) =>
        schema
          .test(
            'required-if-start-selected',
            'End date is required if start date is selected',
            function (toDate) {
              const { validityFrom } = this.parent;
              return !validityFrom || !!toDate;
            }
          )
          .test(
            'end-after-start',
            'End date must be after start date',
            function (toDate) {
              const { validityFrom } = this.parent;
              if (!validityFrom || !toDate) return true;
              return new Date(toDate) > new Date(validityFrom);
            }
          ),
    }),

  returnPolicy: Yup.string().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Return policy is required'),
    otherwise: (schema) => schema.notRequired(),
  }),

  policyFile: Yup.mixed().when('acceptReturns', {
    is: 'Yes',
    then: (schema) => schema.required('Policy document is required'),
    otherwise: (schema) => schema.notRequired(),
  }),
});

export const META_INFO_SCHEMA = Yup.object().shape({
  metaTitle: Yup.string().required('Meta title is required'),
  metaDescription: Yup.string().required('Meta Description is required'),
});

export const COMBO_BUILDER_SCHEMA = Yup.object().shape({
  costPrice: Yup.number()
    .typeError('Cost price must be a number')
    .required('Cost price is required')
    .test(
      'is-costPrice-valid',
      'Total amount cannot be greater than cost price',
      function (value) {
        const { selectedProducts } = this.parent; // Access the parent context which should contain selectedProducts array
        const totalAmount = selectedProducts.reduce(
          (total, product) => total + product.sku_total_price,
          0
        ); // Calculate the total amount from selectedProducts
        if (totalAmount < value) {
          return false; // If totalAmount is less than costPrice, validation fails
        }

        return true; // Otherwise, validation passes
      }
    ),
});