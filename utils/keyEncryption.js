import 'dotenv/config';
import config from '../app/components/config/index.js';
const { ENCRYPTION_IV, ENCRYPTION_KEY } = config;


import CryptoJS from 'crypto-js';

const key = CryptoJS.enc.Utf8.parse(ENCRYPTION_KEY);
const iv = CryptoJS.enc.Utf8.parse(ENCRYPTION_IV);

export function decrypt(encryptedFromPHP) {
    // Step 1: decode outer base64 (this gives us the inner base64)
    const innerBase64 = CryptoJS.enc.Base64.parse(encryptedFromPHP).toString(CryptoJS.enc.Utf8);

    // Step 2: decrypt inner base64 using AES-256-CBC
    const decrypted = CryptoJS.AES.decrypt(innerBase64, key, {
        iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });

    return decrypted.toString(CryptoJS.enc.Utf8);
}


