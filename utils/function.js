'use client';

import generalConfig from '@/app/components/config/generalConfig';
import Cookies from 'js-cookie';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
/** ***************** 
@Purpose : Used to show error toast messages
@Parameter : message
<AUTHOR> INIC
***************** */

export const showErrorToast = (message) => {
  toast.error(message, {
    toastId: message,
    position: 'top-right',
    autoClose: 1800,
    hideProgressBar: true,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
};

/** ***************** 
@Purpose : Used to show sucess toast messages
@Parameter : message
<AUTHOR> INIC
***************** */

export const showSuccessToast = (message) => {
  toast.success(message, {
    toastId: message,
    position: 'top-right',
    autoClose: 1800,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
};

/** ***************** 
@Purpose : Used to show sucess toast messages
@Parameter : message
<AUTHOR> INIC
***************** */

export const showWarningToast = (message) => {
  toast.warning(message, {
    toastId: message,
    position: 'top-right',
    autoClose: 1800,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
  });
};

export const transformResponseHandler = (resp, alert = true) => {
  if (alert && resp?.message) {
    messageNotification(resp?.message, !resp?.statusCode ? 'error' : 'success');
  }
  const transformedResp = {
    status: resp.status,
    data: resp.data,
    total: resp.total,
    extraMeta: resp.extra_meta,
  };
  if (resp.extra_meta?.access_token) {
    transformedResp.accessToken = resp.extra_meta.access_token;
    transformedResp.refreshToken = resp.extra_meta.refreshToken;
  }
  return transformedResp;
};

/** ***************** 
@Purpose : Used for show message notification
@Parameter : text, type, autoClose, position
<AUTHOR> INIC
***************** */

export const messageNotification = (
  text,
  type = 'success',
  autoClose = generalConfig.TOAST_TIMER,
  position = 'top-right'
) => {
  toast[type](text, {
    toastId: text,
    position,
    autoClose,
    hideProgressBar: false,
    closeOnClick: true,
    pauseOnHover: true,
    draggable: true,
    progress: undefined,
  });
};

/** ***************** 
@purpose : Get Cookie
@Parameter : {name}
<AUTHOR> INIC
***************** */
export function getCookie(name) {
  if (name && typeof window !== 'undefined') return Cookies.get(name);
  return '';
}
/** ***************** 
@purpose : Set Cookie
@Parameter : {name, value}
<AUTHOR> INIC
***************** */
export function setCookie(name, value) {
  if (name) {
    // expires after= 50000 days
    Cookies.set(name, value, { expires: 50000 });
  }
}
/** ***************** 
@purpose : Remove Cookie
@Parameter : {name}
<AUTHOR> INIC
***************** */
export function removeCookie(name) {
  if (name) {
    Cookies.remove(name);
  }
}

/** ***************** 
@purpose : Use for display timer (mm:ss)
@Parameter : {startTime}
<AUTHOR> INIC
***************** */
export const useCountdown = (startTime = '00:00') => {
  const toSeconds = (timeStr) => {
    const [m, s] = timeStr.split(':').map(Number);
    return m * 60 + s;
  };

  const [timeLeft, setTimeLeft] = useState(toSeconds(startTime ?? ''));

  useEffect(() => {
    setTimeLeft(toSeconds(startTime ?? ''));
  }, [startTime]);

  useEffect(() => {
    if (timeLeft <= 0) return;

    const interval = setInterval(() => {
      setTimeLeft((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [timeLeft]);

  const formatted = `${String(Math.floor(timeLeft / 60)).padStart(2, '0')}:${String(timeLeft % 60).padStart(2, '0')}`;

  return formatted;
};

/** ***************** 
@purpose : Conver json into formData
@Parameter : {payload}
<AUTHOR> INIC
***************** */

export const convertToFormData = (payload)=>{

  const formData = new FormData();

  Object.entries(payload).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      if (value.every((item) => typeof item === 'object' && item !== null)) {
        formData.append(key, JSON.stringify(value));
      } else {

        value.forEach((item, index) => {
          formData.append(`${key}[${index}]`, item);
        });
      }
    } else if (typeof value === 'object' && value !== null) {
      formData.append(key, value);
    } else {
      formData.append(key, value);
    }
  });

  return formData
}
