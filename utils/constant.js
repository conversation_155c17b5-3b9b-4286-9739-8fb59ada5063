export const DEFAULT_TIMEOUT = 20000; // milliseconds
/** ***************** 
  @purpose : Used For Regex
  @Parameter : {}
  <AUTHOR> INIC
  ***************** */

export const InputValidator = {
  phoneRegExp: /([+]?\d{1,2}[.-\s]?)?(\d{3}[.-]?){2}\d{4}/,
  usPhoneRegExp:
    /^((\+1|1)?( |-)?)?(\([2-9][0-9]{2}\)|[2-9][0-9]{2})( |-)?([2-9][0-9]{2}( |-)?[0-9]{4})$/,
  passwordRegExp:
    /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/,
  emailRegExp:
    /^(([^<>()\]\\.,;:\s@"]+(\.[^<>()\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
  otpRegExp: /[0-9]{6,6}$/,
  alphaOnlyRegx: /^[^\d]+$/,
  passwordRegExp:
    /^(?=.*[0-9])(?=.*[- ?!@#$%^&*\/\\])(?=.*[A-Z])(?=.*[a-z])[a-zA-Z0-9- ?!@#$%^&*\/\\]{6,16}$/,
};

export const onboardingStatus = {
  DRAFT: 'Draft',
  COMPLETED: 'Completed',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
};

export const usaStateOptions = [
  { value: 'Arizona', label: 'Arizona - Tucson' },
  { value: 'Georgia', label: 'Georgia - Savannah' },
  { value: 'Kansas', label: 'Kansas - Wichita' },
  { value: 'Massachusetts', label: 'Massachusetts - Worcester' },
  { value: 'Nevada', label: 'Nevada - Las Vegas' },
  { value: 'New Mexico', label: 'New Mexico - Albuquerque' },
  { value: 'Ohio', label: 'Ohio - Cleveland' },
  { value: 'Texas', label: 'Texas - Houston' },
];

export const usaCityOptions = [
  { value: 'Tucson', label: 'Tucson, Arizona' },
  { value: 'Savannah', label: 'Savannah, Georgia' },
  { value: 'Wichita', label: 'Wichita, Kansas' },
  { value: 'Worcester', label: 'Worcester, Massachusetts' },
  { value: 'Las Vegas', label: 'Las Vegas, Nevada' },
  { value: 'Albuquerque', label: 'Albuquerque, New Mexico' },
  { value: 'Cleveland', label: 'Cleveland, Ohio' },
  { value: 'Houston', label: 'Houston, Texas' },
];

export const idTypeOptions = [
  { value: "Driving License", label: "Driving License" },
  { value: "Social Security", label: "Social Security" },
  { value: "Passport", label: "Passport" },
  { value: "Permanent Resident Card", label: "Permanent Resident Card" },
  { value: "Work Permit/Employer Authorisation", label: "Work Permit/Employer Authorisation" },
];

export const ProductType = {
  "RETAIL":"Retail",
  "BUNDLE":"Bundle"
}


export const userBlockedTime = '15:00';
